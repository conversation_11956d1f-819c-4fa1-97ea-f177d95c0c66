# Odevly - Scalable Homework Help Platform

A comprehensive platform connecting students with tutors for homework assistance, built with Next.js, Firebase, and optimized for high scalability.

## 🚀 Recent Scalability Improvements

### Critical Fixes Implemented

1. **Scheduled Function Optimization**
   - Changed cleanup function from every 1 minute to every 15 minutes
   - Added proper batching and limits to prevent function execution limits
   - Implemented pagination for large datasets

2. **Database Query Optimization**
   - Added comprehensive Firestore indexes for all major queries
   - Implemented pagination in `getAllDocs` function
   - Added proper limits and error handling

3. **Real-time Connection Management**
   - Enhanced `listenToDocument` with retry logic and connection monitoring
   - Added proper cleanup to prevent memory leaks
   - Implemented exponential backoff for failed connections

4. **API Rate Limiting & Retry Logic**
   - Added rate limiting for external API calls (Wise, Stripe)
   - Implemented exponential backoff retry mechanism
   - Added proper error handling for network failures

5. **Middleware Performance**
   - Added caching layer for user types to reduce Firestore calls
   - Implemented TTL-based cache with automatic cleanup
   - Reduced latency on protected routes

6. **Image Upload Optimization**
   - Added file size limits (5MB default)
   - Implemented client-side image compression
   - Added file type validation
   - Automatic resizing for large images

7. **Error Tracking & Monitoring**
   - Enhanced error tracking with context
   - Performance metrics collection
   - User-friendly error messages
   - Integration ready for monitoring services

## 📊 Performance Monitoring

### Built-in Metrics
- API call tracking
- Error rate monitoring
- Slow operation detection
- Connection status tracking

### Monitoring Setup
```javascript
// Access performance metrics
import { getPerformanceMetrics, getErrorLog } from '@/src/Utils/AppNotify';

const metrics = getPerformanceMetrics();
const errors = getErrorLog();
```

## 🔧 Environment Variables

### Required Environment Variables
```bash
# Firebase
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
FB_CLIENT_EMAIL=your_client_email
FB_PRIVATE_KEY=your_private_key

# Authentication
COOKIE_SECRET_CURRENT=your_cookie_secret
COOKIE_SECRET_PREVIOUS=your_previous_cookie_secret
INTERNAL_API_SECRET=your_internal_api_secret

# External APIs
STRIPE_API_KEY=your_stripe_secret_key
WISE_API_KEY=your_wise_api_key

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
MAINTENANCE_MODE=false
BASIC_AUTH_USER=admin
BASIC_AUTH_PASS=odevly
```

## 🚀 Deployment

### Prerequisites
- Node.js 18+
- Firebase CLI
- Firebase project with Firestore enabled

### Deployment Steps

1. **Install Dependencies**
   ```bash
   npm install
   cd functions && npm install
   ```

2. **Build the Application**
   ```bash
   npm run build
   cd functions && npm run build
   ```

3. **Deploy Firebase Functions**
   ```bash
   firebase deploy --only functions
   ```

4. **Deploy Firestore Indexes**
   ```bash
   firebase deploy --only firestore:indexes
   ```

5. **Deploy Firestore Rules**
   ```bash
   firebase deploy --only firestore:rules
   ```

6. **Deploy to Vercel/Netlify**
   ```bash
   # For Vercel
   vercel --prod
   
   # For Netlify
   netlify deploy --prod
   ```

## 📈 Scalability Features

### Database Optimization
- **Indexed Queries**: All major queries are properly indexed
- **Pagination**: Large datasets are paginated to prevent timeouts
- **Batch Operations**: Multiple operations are batched for efficiency
- **Connection Pooling**: Optimized Firestore connections

### Caching Strategy
- **User Type Caching**: 5-minute TTL cache for user types
- **Automatic Cleanup**: Memory leak prevention
- **Cache Invalidation**: Proper cache management

### Error Handling
- **Retry Logic**: Automatic retry for transient failures
- **Circuit Breaker**: Prevents cascade failures
- **Graceful Degradation**: App continues working during partial failures

### Rate Limiting
- **API Rate Limits**: 10 calls per minute per API
- **Exponential Backoff**: Intelligent retry delays
- **Request Queuing**: Prevents overwhelming external services

## 🔍 Monitoring & Alerting

### Recommended Monitoring Setup

1. **Sentry Integration**
   ```javascript
   // Add to your app
   import * as Sentry from "@sentry/nextjs";
   
   Sentry.init({
     dsn: "your-sentry-dsn",
     environment: process.env.NODE_ENV,
   });
   ```

2. **Firebase Monitoring**
   - Enable Firebase Performance Monitoring
   - Set up custom metrics for business KPIs
   - Configure alerts for error rates

3. **Uptime Monitoring**
   - Set up health check endpoints
   - Monitor response times
   - Alert on downtime

### Key Metrics to Monitor
- **Error Rate**: Should be < 1%
- **Response Time**: Should be < 2 seconds
- **Function Execution Time**: Should be < 10 seconds
- **Database Read/Write Operations**: Monitor for spikes
- **Memory Usage**: Should be stable

## 🛠️ Troubleshooting

### Common Issues

1. **Function Timeouts**
   - Check function execution logs
   - Verify batch sizes are reasonable
   - Monitor external API response times

2. **Database Performance**
   - Verify indexes are built
   - Check query complexity
   - Monitor read/write operations

3. **Memory Leaks**
   - Check listener cleanup
   - Monitor memory usage
   - Verify cache cleanup

4. **Rate Limiting**
   - Check API call frequency
   - Verify rate limit settings
   - Monitor external service quotas

### Debug Commands
```bash
# Check function logs
firebase functions:log

# Monitor performance
firebase functions:config:get

# Check database usage
firebase firestore:indexes

# Test locally
firebase emulators:start
```

## 📚 API Documentation

### Key Endpoints

- `POST /api/login` - User authentication
- `POST /api/logout` - User logout
- `GET /api/internal/user-type/[uid]` - Get user type (cached)
- `POST /functions/recordAnswerAndEarning` - Record tutor answers
- `POST /functions/initiateWisePayoutToTutor` - Process payouts

### Rate Limits
- Authentication: 100 requests/minute
- Database operations: 1000 requests/minute
- External APIs: 10 requests/minute

## 🔒 Security

### Implemented Security Measures
- **Authentication**: Firebase Auth with custom claims
- **Authorization**: Role-based access control
- **Input Validation**: All inputs are validated
- **Rate Limiting**: Prevents abuse
- **CORS**: Properly configured
- **HTTPS**: Enforced in production

## 📞 Support

For technical support or scalability questions:
- Check the troubleshooting section
- Review Firebase console logs
- Monitor performance metrics
- Contact the development team

## 🎯 Performance Targets

- **Page Load Time**: < 2 seconds
- **API Response Time**: < 1 second
- **Database Query Time**: < 500ms
- **Function Execution**: < 10 seconds
- **Uptime**: > 99.9%

---

**Built with scalability in mind for high-traffic applications.**

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.
