# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# firebase
/.firebase/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*


# local env files
.env
.env*.local
.env.development

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

firebase-debug.log

.idea/
