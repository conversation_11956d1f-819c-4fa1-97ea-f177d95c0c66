{"name": "homework", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.14", "@mui/material": "^5.15.14", "@mui/system": "^5.15.14", "@mui/x-date-pickers": "^7.23.2", "@next/bundle-analyzer": "^14.2.4", "@react-pdf/renderer": "^3.4.4", "@sentry/nextjs": "^7.109.0", "@stripe/react-stripe-js": "^2.7.1", "@stripe/stripe-js": "^3.4.1", "axios": "^1.6.8", "chart.js": "^4.4.2", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-promise": "^7.2.1", "file-saver": "^2.0.5", "firebase": "^10.9.0", "firebase-admin": "^13.4.0", "firebase-functions": "^6.3.2", "html-react-parser": "^5.1.10", "i18next": "^25.2.1", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mui-phone-number": "^3.0.3", "next": "^14.2.21", "next-firebase-auth-edge": "^1.9.1", "next-i18next": "^15.4.2", "next-seo": "^6.5.0", "prettier": "^3.2.5", "react": "^18", "react-chartjs-2": "^5.2.0", "react-datepicker": "^6.9.0", "react-dom": "^18", "react-i18next": "^15.5.3", "react-select-country-list": "^2.2.3", "react-slick": "^0.30.3", "react-timer-hook": "^3.0.7", "react-toastify": "^10.0.5", "react-webcam": "^7.2.0", "slick-carousel": "^1.8.1", "stripe": "^15.8.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.1.3", "eslint-plugin-n": "^16.6.2", "firebase-tools": "^13.5.2", "typescript": "^5.8.3"}, "pnpm": {"overrides": {"undici": "5.28.4"}}}