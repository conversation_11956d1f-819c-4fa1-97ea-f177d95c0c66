{"indexes": [{"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "users_questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "exclusive", "order": "ASCENDING"}, {"fieldPath": "isAnswered", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "users_questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "exclusive", "order": "ASCENDING"}, {"fieldPath": "isAnswered", "order": "ASCENDING"}, {"fieldPath": "__name__", "order": "ASCENDING"}]}, {"collectionGroup": "tutor_exclusive_questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "dateClaimed", "order": "ASCENDING"}]}, {"collectionGroup": "tutor_earnings_log", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "tutor_earnings_log", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "withdrawal_methods", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "certificates", "queryScope": "COLLECTION", "fields": [{"fieldPath": "certificateNumber", "order": "ASCENDING"}, {"fieldPath": "paid", "order": "ASCENDING"}]}, {"collectionGroup": "exams_completed", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "completed_at", "order": "DESCENDING"}]}, {"collectionGroup": "exams_questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "examId", "order": "ASCENDING"}, {"fieldPath": "is_archived", "order": "ASCENDING"}]}, {"collectionGroup": "users_skipped_questions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "questionId", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "users", "fieldPath": "email", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "users", "fieldPath": "type", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "users_questions", "fieldPath": "exclusive", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "users_questions", "fieldPath": "isAnswered", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "tutor_exclusive_questions", "fieldPath": "dateClaimed", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}]}