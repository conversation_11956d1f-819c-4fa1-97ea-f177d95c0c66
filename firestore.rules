rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    function isAuthenticated() {
      return request.auth != null;
    }

    // Get the requesting user's data (including their type)
    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }

    // Check if the requesting user is an admin
    function isAdmin() {
      return isAuthenticated() && getUserData().type == 'admin';
    }

    // Check if the user owns the document based on a specified field
    function isOwner(userIdField) {
      return isAuthenticated() && request.auth.uid == resource.data[userIdField];
    }

    // Check if the user is the creator of the document based on a specified field
     function isCreator(userIdField) {
       return isAuthenticated() && request.auth.uid == request.resource.data[userIdField];
     }

    // === Collection: users ===
    match /users/{userId} {
      // Read: Any authenticated user can read any user profile
      // Create: A user can create their own profile.
      //         - 'type' cannot be set to 'admin'.
      //         - 'type' must be 'user' or 'tutor'.
      //         - 'earnings' and 'revenue' cannot be set by client on creation.
      allow read: if isAuthenticated();
      allow create: if isAuthenticated()
                    && request.auth.uid == userId // Can only create their own user doc
                    && request.resource.data.type in ['user', 'tutor'] // Type must be user or tutor
                    && !('earnings' in request.resource.data) // Cannot set earnings on create
                    && !('revenue' in request.resource.data); // Cannot set revenue on create

      // Update: A user can update their own profile, BUT:
      //         - Cannot change their 'type'.
      //         - Cannot directly modify 'earnings' or 'revenue'.
      allow update: if isAuthenticated()
                    && request.auth.uid == userId // Can only update their own user doc
                    && request.resource.data.type == resource.data.type // Type cannot be changed
                    && !request.resource.data.keys().hasAny(['earnings', 'revenue']); // Client cannot provide these fields in update
    }

    // === Collection: admin_question_credits ===
    match /admin_question_credits/{docId} {
      // Read, Write (Create, Update, Delete): Only Admins
      allow read, write: if isAdmin();
    }

    // === Collection: admin_setting ===
    match /admin_setting/{docId} {
      // Read, Write (Create, Update, Delete): Only Admins
      allow read, write: if isAdmin();
    }

    // === Collection: applicant_answers ===
    match /applicant_answers/{answerId} {
      // Read: The user who answered ('answeredBy') or an Admin can read.
      allow read: if isAdmin() || isOwner('answeredBy');

      // Create: The user themselves, ensuring 'answeredBy' is set to their UID.
      allow create: if isCreator('answeredBy');

      // Update: Only the user who answered ('answeredBy') can update.
      allow update: if isOwner('answeredBy');

      // Delete: Only the user who answered ('answeredBy') or an Admin can delete.
      allow delete: if isAdmin() || isOwner('answeredBy');
    }

    // === Collection: applicant_questions ===
    match /applicant_questions/{questionId} {
      // Read: Any authenticated user can read questions.
      allow read: if isAuthenticated();

      // Write (Create, Update, Delete): Only Admins can manage questions.
      allow write: if isAdmin();
    }

    // === Collection: withdrawal_methods ===
    match /withdrawal_methods/{methodId} {
      // Read: Only the owner ('user' field) or an Admin can read.
      allow read: if isAdmin() || isOwner('user');

      // Create: The user themselves, ensuring the 'user' field is set to their UID.
      allow create: if isCreator('user');

      // Update: Only the owner ('user' field) can update.
      allow update: if isOwner('user');

      // Delete: Only the owner ('user' field) or an Admin can delete.
      allow delete: if isAdmin() || isOwner('user');
    }
  }
}