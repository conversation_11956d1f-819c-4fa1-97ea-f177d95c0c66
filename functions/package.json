{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"@sendgrid/mail": "^8.1.3", "firebase-admin": "^11.11.1", "firebase-functions": "^4.9.0", "next-firebase-auth-edge": "^1.9.1", "stripe": "^15.8.0"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.0.4", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.5"}, "private": true}