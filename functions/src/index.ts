const {defineSecret} = require("firebase-functions/params");
import * as nodeCrypto from 'crypto';
import * as functions from "firebase-functions";
import {logger as v1Logger} from "firebase-functions/v1";
import {error as v2ErrorLogger, log as v2Logger} from "firebase-functions/logger";

const {logger} = require("firebase-functions/v1");

const {onRequest, onCall, HttpsError} = require("firebase-functions/v2/https");
const {getAuth} = require("firebase-admin/auth");
const {log} = require("firebase-functions/logger");
const cors = require("cors")({origin: true});
const Stripe = require("stripe");
const admin = require("firebase-admin");
const FieldValue = admin.firestore.FieldValue;
admin.initializeApp();
const db = admin.firestore();
const STRIPE_API_KEY = defineSecret("STRIPE_API_KEY");
const WISE_API_KEY = defineSecret("WISE_API_KEY");
const WISE_API_BASE_URL = "https://api.sandbox.transferwise.tech";

// Rate limiting for external API calls
const apiCallTracker = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT = { maxCalls: 10, windowMs: 60000 }; // 10 calls per minute

function checkRateLimit(apiName: string): boolean {
    const now = Date.now();
    const tracker = apiCallTracker.get(apiName);
    
    if (!tracker || now > tracker.resetTime) {
        apiCallTracker.set(apiName, { count: 1, resetTime: now + RATE_LIMIT.windowMs });
        return true;
    }
    
    if (tracker.count >= RATE_LIMIT.maxCalls) {
        return false;
    }
    
    tracker.count++;
    return true;
}

async function makeApiCallWithRetry(
    url: string, 
    options: RequestInit, 
    apiName: string,
    maxRetries: number = 3
): Promise<globalThis.Response> {
    if (!checkRateLimit(apiName)) {
        throw new Error(`Rate limit exceeded for ${apiName}`);
    }
    
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const response = await fetch(url, options);
            
            if (response.ok) {
                return response;
            }
            
            // Don't retry on client errors (4xx)
            if (response.status >= 400 && response.status < 500) {
                return response;
            }
            
            lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
            
        } catch (error) {
            lastError = error instanceof Error ? error : new Error('Unknown error');
        }
        
        // Exponential backoff
        if (attempt < maxRetries) {
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
    
    throw lastError!;
}

import type { Request, Response } from "firebase-functions";
import type { CallableRequest } from "firebase-functions/v2/https";

interface UpdateUserRequestBody {
    uid: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    password?: string;
    phoneNumber?: string;
    certificateNumber?: string;
    billingAddress?: string;
    dateOfBirth?: number | string;
    educationLevel?: string;
    tutorExperience?: string;
    country?: string;
    address1?: string;
    address2?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    profileImageUrl?: string | null;
    revenue?: number;
    questions?: number;
    statistics?: Record<string, any>;
    achievements?: Record<string, any>;
    closePage?: boolean;
    userId?: string;
    attemptedExams?: any;
    birthMonth?: string;
    birthYear?: string;
}

export const adminUpdateUser = onRequest(
    async (request: Request, response: Response) => {
        cors(request, response, async () => {
            const body = request.body as UpdateUserRequestBody;

            let callerUid: string | undefined;
            let isAdmin = false;
            let adminUserName = "Unknown Admin";

            // verify admin
            const authorizationHeader = request.headers.authorization;
            if (authorizationHeader && authorizationHeader.startsWith("Bearer ")) {
                const idToken = authorizationHeader.split("Bearer ")[1];
                try {
                    const decodedToken = await getAuth().verifyIdToken(idToken);
                    callerUid = decodedToken.uid;
                    const callerDocSnap = await db.collection("users").doc(callerUid).get();
                    if (callerDocSnap.exists) {
                        const callerData = callerDocSnap.data();
                        if (callerData?.type === "admin") {
                            isAdmin = true;
                            adminUserName = (callerData.firstName && callerData.lastName)
                                ? `${callerData.firstName} ${callerData.lastName}`.trim()
                                : callerData.email || `Admin_${callerUid!.substring(0, 6)}`; // Fixed with !
                        }
                    }
                } catch (err) {
                    v2ErrorLogger("Auth error: Invalid token or failed to fetch admin role", {
                        error: err,
                        callerUidAttempt: callerUid,
                    });
                    response.status(403).json({status: false, message: "Authentication failed or not authorized."});
                    return;
                }
            } else {
                if (!body.closePage) {
                    v1Logger.warn("Update user attempt without authentication token.", {body});
                    response.status(401).json({status: false, message: "Authentication token required."});
                    return;
                }
            }

            if (body.hasOwnProperty("closePage")) {
                if (!body.userId) {
                    v2ErrorLogger("`userId` is required for `closePage` operation.", {body});
                    response.status(400).json({
                        status: false,
                        message: "Target User ID is required for this operation."
                    });
                    return;
                }
                try {
                    const updateData: { attempted_exams?: any } = {};
                    if (body.hasOwnProperty("attemptedExams")) {
                        updateData.attempted_exams = body.attemptedExams;
                    }
                    v2Logger(`Updating attempted_exams for user: ${body.userId}`, {updateData});
                    await db.collection("users").doc(body.userId).set(updateData, {merge: true});
                    v2Logger("attempted_exams updated successfully for user:", body.userId);
                    response.json({status: true, message: "Page state saved."});
                    return;
                } catch (err) {
                    v2ErrorLogger("Error updating attempted_exams:", {error: err, userId: body.userId});
                    response.status(500).json({status: false, message: "Failed to save page state."});
                    return;
                }
            }

            if (!isAdmin || !callerUid) { // Ensure callerUid is also set if admin is true
                v1Logger.warn(`Non-admin or unidentifiable admin (Caller: ${callerUid || "Unknown/NoToken"}) attempted to update user. Denying access.`, {targetUid: body.uid});
                response.status(403).json({
                    status: false,
                    message: "Forbidden: Admin privileges and identification required."
                });
                return;
            }

            if (!body.uid) {
                v2ErrorLogger("Target user `uid` is required for update.", {body});
                response.status(400).json({status: false, message: "Target user UID is required."});
                return;
            }

            const targetUserRef = db.collection("users").doc(body.uid);
            let targetUserSnap;
            let currentRevenue = 0;
            let targetUserName = `User_${body.uid.substring(0, 6)}`;

            try {
                targetUserSnap = await targetUserRef.get();
                if (targetUserSnap.exists) {
                    const targetUserData = targetUserSnap.data()!;
                    currentRevenue = targetUserData.revenue || 0;
                    targetUserName = (targetUserData.firstName && targetUserData.lastName)
                        ? `${targetUserData.firstName} ${targetUserData.lastName}`.trim()
                        : targetUserData.email || targetUserName; // Fallback to existing targetUserName
                } else {
                    v1Logger.warn(`Target user ${body.uid} not found in Firestore. Some operations might be limited.`);
                }
            } catch (err) {
                v2ErrorLogger(`Error fetching target user ${body.uid} for pre-update checks:`, {error: err});
            }


            try {
                const authDataToUpdate: {
                    email?: string;
                    password?: string;
                    displayName?: string;
                } = {};
                if (body.hasOwnProperty("email") && typeof body.email === 'string') {
                    authDataToUpdate.email = body.email;
                }
                if (body.hasOwnProperty("password") && body.password) {
                    authDataToUpdate.password = body.password;
                }
                const fName = body.hasOwnProperty("firstName") ? body.firstName : undefined;
                const lName = body.hasOwnProperty("lastName") ? body.lastName : undefined;

                if (fName !== undefined || lName !== undefined) {
                    let authUserDisplayName = targetUserSnap?.data()?.displayName; // Prefer Firestore data if available
                    if (!authUserDisplayName) {
                        const currentAuthUser = await getAuth().getUser(body.uid);
                        authUserDisplayName = currentAuthUser.displayName;
                    }
                    const currentDisplayNameParts = authUserDisplayName?.split(" ") || ["", ""];
                    const newFirstName = fName !== undefined ? fName : currentDisplayNameParts[0] || "";
                    const newLastName = lName !== undefined ? lName : currentDisplayNameParts.slice(1).join(" ") || "";
                    authDataToUpdate.displayName = `${newFirstName} ${newLastName}`.trim();
                }
                if (Object.keys(authDataToUpdate).length > 0) {
                    await getAuth().updateUser(body.uid, authDataToUpdate);
                    v2Logger(`Firebase Auth updated for user: ${body.uid}`, {authDataToUpdate});
                }

                // update document
                const firestoreDataToUpdate: Partial<UpdateUserRequestBody> = {};
                const directFirestoreFields: (keyof UpdateUserRequestBody)[] = [
                    "firstName", "lastName", "phoneNumber", "certificateNumber",
                    "billingAddress", "dateOfBirth", "email", "educationLevel",
                    "tutorExperience", "country", "address1", "address2",
                    "city", "state", "postalCode", "profileImageUrl",
                    "revenue", "questions", "statistics", "achievements",
                    "birthMonth", "birthYear",
                ];

                let newRevenueAmount: number | undefined = undefined;
                let revenueDelta = 0;

                directFirestoreFields.forEach((field) => {
                    if (body.hasOwnProperty(field)) {
                        if (field === "profileImageUrl" && body[field] === null) {
                            (firestoreDataToUpdate as any)[field] = "";
                        } else if (field === "revenue" && typeof body.revenue === 'number') {
                            newRevenueAmount = body.revenue;
                            revenueDelta = newRevenueAmount - currentRevenue;
                            (firestoreDataToUpdate as any)[field] = newRevenueAmount;
                        } else if (body[field] !== undefined) {
                            (firestoreDataToUpdate as any)[field] = body[field];
                        }
                    }
                });

                const batch = db.batch();

                if (Object.keys(firestoreDataToUpdate).length > 0) {
                    batch.set(targetUserRef, firestoreDataToUpdate, {merge: true});
                }

                if (revenueDelta !== 0 && newRevenueAmount !== undefined) {
                    const earningLogRef = db.collection("tutor_earnings_log").doc(); // Auto-generate ID
                    const logEntryType = revenueDelta > 0 ? "Admin Credit" : "Admin Deduction";
                    const logAmount = Math.abs(revenueDelta);

                    batch.set(earningLogRef, {
                        userId: body.uid,
                        date: admin.firestore.Timestamp.now(),
                        type: logEntryType,
                        amount: logAmount,
                        questionId: null,
                        userName: targetUserName,
                        adminUserId: callerUid!,
                        adminUserName: adminUserName,
                        previousRevenue: currentRevenue,
                        newRevenue: newRevenueAmount,
                        notes: `Admin adjustment: ${revenueDelta > 0 ? '+' : ''}${revenueDelta.toFixed(2)}`,
                    });
                    v2Logger(`Revenue change for tutor ${body.uid} by admin ${callerUid}. Delta: ${revenueDelta.toFixed(2)}`);
                }

                if (Object.keys(firestoreDataToUpdate).length > 0 || revenueDelta !== 0) {
                    await batch.commit();
                    v2Logger(`Firestore operations committed for user: ${body.uid}`, {
                        firestoreDataToUpdate,
                        revenueDelta
                    });
                } else {
                    v2Logger(`No Firestore changes to commit for user: ${body.uid}`);
                }


                response.json({status: true, message: "User updated successfully."});
            } catch (err: any) {
                v2ErrorLogger(`Error updating user ${body.uid}:`, {
                    errorCode: err.code,
                    errorMessage: err.message,
                    requestBody: body,
                });
                let message = "Failed to update user.";
                if (err.code === "auth/email-already-exists") {
                    message = "The email address is already in use by another account.";
                } else if (err.code === "auth/user-not-found") {
                    message = "User to update not found in Firebase Authentication.";
                } else if (err.message) {
                    message = err.message;
                }
                response.status(500).json({status: false, message});
            }
        });
    }
);


exports.updateUser = onRequest(async (request: Request, response: Response) => {
    cors(request, response, async () => {
        const {
            uid,
            email,
            firstName,
            lastName,
            password,
            phoneNumber,
            closePage,
            dateOfBirth,
            attemptedExams,
            certificateNumber,
            billingAddress,
            userId,
            educationLevel,
            tutorExperience,
            workTitle,
            country,
            address1,
            address2,
            city,
            state,
            postalCode,
            profileImageUrl,
        } = request.body;

        try {
            if (closePage) {
                log("Closing Page");
                await db.collection("users").doc(userId).set(
                    {
                        attempted_exams: attemptedExams,
                    },
                    {merge: true}
                );
                log("added");
                return response.json({status: true});
            } else {
                let resp;
                if (password) {
                    resp = await getAuth().updateUser(uid, {
                        displayName: `${firstName} ${lastName}`,
                        email,
                        password,
                    });
                } else {
                    resp = await getAuth().updateUser(uid, {
                        displayName: `${firstName} ${lastName}`,
                        email,
                    });
                }

                if (resp.uid) {
                    await db.collection("users").doc(resp.uid).set(
                        {
                            firstName: firstName,
                            lastName: lastName,
                            phoneNumber: phoneNumber,
                            certificateNumber: certificateNumber,
                            billingAddress,
                            dateOfBirth: dateOfBirth,
                            email: resp.email,
                            educationLevel: educationLevel || "",
                            tutorExperience: tutorExperience || "",
                            workTitle: workTitle || "",
                            country: country || "",
                            address1: address1 || "",
                            address2: address2 || "",
                            city: city || "",
                            state: state || "",
                            postalCode: postalCode || "",
                            ...(profileImageUrl ? {profileImageUrl} : {}),
                            ...(dateOfBirth ? {dateOfBirth} : {}),
                            ...(request.body.birthMonth ? {birthMonth: request.body.birthMonth} : {}),
                            ...(request.body.birthDay ? {birthDay: request.body.birthDay} : {}),
                            ...(request.body.birthYear ? {birthYear: request.body.birthYear} : {})
                        },
                        {merge: true}
                    );
                }
            }
            return response.json({status: true});
        } catch (error) {
            console.error("Error updating user:", error);
            return response.json({status: false, message: error || error});
        }
    });
});

exports.checkAccountExists = onRequest(
    async (request: Request, response: Response) => {
        cors(request, response, async () => {
            const {email} = request.body;
            const usersRef = db.collection("users");
            try {
                const querySnapshot = await usersRef.where("email", "==", email).get();
                if (querySnapshot.docs.length > 0) {
                    return response.json({status: true, exists: true});
                } else {
                    return response.json({status: true, exists: false});
                }
            } catch (error) {
                return response.json({status: false, exists: false});
            }
        });
    }
);


exports.getCertificateData = onRequest(async (request: Request, response: Response) => {
    cors(request, response, async () => {
        const {certificate_number} = request.body;
        try {
            const querySnapshot = await db
                .collection("certificates")
                .where("certificateNumber", "==", Number(certificate_number))
                .get();

            const docsArr: any = [];
            let expired = false;
            querySnapshot.forEach((doc: any) => {
                const docData = doc.data();
                if (docData.paid) {
                    if (
                        docData.expiryDateTime &&
                        new Date(docData.expiryDateTime) < new Date()
                    ) {
                        expired = true;
                    } else {
                        docsArr.push({
                            userName: docData.userName,
                            certificateNumber: docData.certificateNumber,
                            score: docData.score,
                            docId: doc.id,
                            imageUrl: docData.imageUrl,
                        });
                    }
                }
            });
            if (docsArr.length > 0 || expired) {
                return response.json({
                    status: true,
                    data: docsArr.length > 0 ? docsArr[0] : null,
                    expired,
                });
            } else {
                return response.json({status: false});
            }
        } catch (error) {
            logger.log("error", error);
            return response.json({status: false});
        }
    });
});

exports.getCertificateDataById = onRequest(
    async (request: Request, response: Response) => {
        cors(request, response, async () => {
            const {docId} = request.body;
            try {
                const doc = await db.collection("certificates").doc(docId).get();
                if (doc.exists) {
                    let expired = false;
                    let docObj: any = null;
                    const docData = doc.data();
                    if (docData.paid) {
                        if (
                            docData.expiryDateTime &&
                            new Date(docData.expiryDateTime) < new Date()
                        ) {
                            expired = true;
                        } else {
                            docObj = {
                                userName: docData.userName,
                                certificateNumber: docData.certificateNumber,
                                score: docData.score,
                                imageUrl: docData.imageUrl,
                            };
                        }
                    } else {
                        return response.json({status: false});
                    }

                    if (docObj || expired) {
                        return response.json({
                            status: true,
                            data: docObj ? docObj : null,
                            expired,
                        });
                    }
                } else {
                    return response.json({status: false});
                }
            } catch (error) {
                logger.log("error", error);
                return response.json({status: false});
            }
            // Ensure all code paths return
            return;
        });
    }
);

exports.recordAnswerAndEarning = onCall(
    {region: 'us-central1'},
    async (request: CallableRequest) => {
        const userId = request.auth?.uid;
        if (!userId) {
            throw new HttpsError(
                "unauthenticated",
                "You must be logged in to perform this action."
            );
        }

        const {
            questionDocId,
            originalQuestionId,
            isApplicantQuestion,
            answerText,
            answerImageUrl,
        } = request.data;

        if (
            !originalQuestionId ||
            !questionDocId ||
            !answerText
        ) {
            throw new HttpsError(
                "invalid-argument",
                "Missing required parameters. Ensure questionDocId, originalQuestionId, and answerText are provided."
            );
        }

        let earningAmount = 3;
        try {
            const adminSettingsRef = db.collection("test_question").doc("admin_setting");
            const adminSettingsSnap = await adminSettingsRef.get();
            if (adminSettingsSnap.exists) {
                const amountFromSettings = parseFloat(adminSettingsSnap.data()?.amount);
                if (!isNaN(amountFromSettings) && amountFromSettings > 0) {
                    earningAmount = amountFromSettings;
                } else {
                    logger.warn("Admin settings amount is invalid, using default earning amount.", {amount: adminSettingsSnap.data()?.amount});
                }
            } else {
                logger.warn("Admin settings document not found, using default earning amount.");
            }
        } catch (err) {
            logger.error("Error fetching admin settings for earning amount:", err);
        }

        const userRef = db.collection("users").doc(userId);
        const questionRef = isApplicantQuestion
            ? db.collection("applicant_questions").doc(originalQuestionId)
            : db.collection("users_questions").doc(originalQuestionId);

        const exclusiveQuestionRef = !isApplicantQuestion
            ? db.collection("tutor_exclusive_questions").doc(questionDocId)
            : null;

        try {
            await db.runTransaction(async (transaction: any) => {
                const userSnap = await transaction.get(userRef);
                const questionSnap = await transaction.get(questionRef);

                if (!userSnap.exists) {
                    throw new HttpsError("not-found", `User record not found for UID: ${userId}.`);
                }
                if (!questionSnap.exists) {
                    const path = isApplicantQuestion ? `applicant_questions/${originalQuestionId}` : `users_questions/${originalQuestionId}`;
                    throw new HttpsError("not-found", `Question record not found at ${path}.`);
                }

                const userData = userSnap.data()!;
                const questionData = questionSnap.data()!;

                let exclusiveQuestionData;
                if (exclusiveQuestionRef) {
                    const exclusiveQuestionSnap = await transaction.get(exclusiveQuestionRef);
                    if (!exclusiveQuestionSnap.exists) {
                        throw new HttpsError("not-found", `Exclusive question lock not found at tutor_exclusive_questions/${questionDocId}. It might have been processed or released.`);
                    }
                    exclusiveQuestionData = exclusiveQuestionSnap.data()!;
                }

                if (isApplicantQuestion) {
                    if ((userData.applicantQuestionAnswered ?? 0) >= 2) {
                        throw new HttpsError("failed-precondition", "Applicant has already answered the maximum number of questions (2).");
                    }
                } else {
                    if (questionData.isAnswered) {
                        throw new HttpsError("failed-precondition", `Question ${originalQuestionId} has already been answered.`);
                    }
                    if (!exclusiveQuestionData || exclusiveQuestionData.claimedBy !== userId) {
                        throw new HttpsError("permission-denied", `User ${userId} has not claimed question ${originalQuestionId} or the claim is invalid.`);
                    }
                }

                const userName = (userData.firstName && userData.lastName)
                    ? `${userData.firstName} ${userData.lastName}`.trim()
                    : userData.email || `User_${userId.substring(0, 6)}`;

                const userUpdateData: any = {
                    revenue: FieldValue.increment(earningAmount),
                    earnings: FieldValue.increment(earningAmount),
                };
                if (isApplicantQuestion) {
                    userUpdateData.applicantQuestionAnswered = FieldValue.increment(1);
                } else {
                    userUpdateData.answers = FieldValue.increment(1);
                }
                transaction.update(userRef, userUpdateData);

                const earningLogRef = db.collection("tutor_earnings_log").doc();
                transaction.set(earningLogRef, {
                    userId,
                    date: admin.firestore.Timestamp.now(),
                    type: isApplicantQuestion ? "Answer" : "Answer",
                    amount: earningAmount,
                    questionId: originalQuestionId,
                    userName: userName,
                });

                const tutorAnswerRef = db.collection("tutor_answers").doc();
                const tutorAnswerData: any = {
                    questionCustomId: questionData.id || originalQuestionId,
                    questionId: originalQuestionId,
                    question: questionData.question,
                    questionAskedByName: questionData.name || questionData.email || 'Anonymous',
                    questionAnsweredByName: userName,
                    answer: answerText,
                    answeredBy: userId,
                    date: admin.firestore.Timestamp.now(),
                };
                if (answerImageUrl) {
                    tutorAnswerData.imgUrl = answerImageUrl;
                }
                transaction.set(tutorAnswerRef, tutorAnswerData);

                if (!isApplicantQuestion) {
                    transaction.update(questionRef, {
                        isAnswered: true,
                        answeredUserId: userId,
                        answeredByUsername: userName,
                        exclusive: false,
                    });
                    if (exclusiveQuestionRef) {
                        transaction.delete(exclusiveQuestionRef);
                    }
                }
            });

            logger.info(`Successfully recorded answer and earning for user ${userId}, question ${originalQuestionId}. Amount: ${earningAmount}`);
            return {status: true, message: "Answer submitted and earning recorded successfully."};

        } catch (error: any) {
            logger.error(`Transaction failed for user ${userId}, question ${originalQuestionId}:`, error.message, {details: error.details});
            if (error instanceof HttpsError) {
                throw error;
            }
            throw new HttpsError("internal", "An internal error occurred while processing your answer.", {originalErrorMessage: error.message});
        }
    }
);

exports.initiateWisePayoutToTutor = onCall(
    {secrets: [WISE_API_KEY], region: 'us-central1', timeoutSeconds: 120},
    async (request: CallableRequest) => {
        const userId = request.auth?.uid;
        if (!userId) {
            throw new HttpsError("unauthenticated", "Authentication required to initiate payout.");
        }

        const {amount} = request.data;
        const floatAmount = parseFloat(amount);

        if (isNaN(floatAmount) || floatAmount <= 0) {
            throw new HttpsError("invalid-argument", "A valid positive amount is required for withdrawal.");
        }
        if (floatAmount > 100) {
            throw new HttpsError("failed-precondition", "Amount exceeds direct payout limit (100). This should be processed as a pending withdrawal.");
        }

        const apiKey = WISE_API_KEY.value();
        const userRef = db.collection("users").doc(userId);
        const withdrawalMethodsCollection = db.collection("withdrawal_methods");

        try {
            return await db.runTransaction(async (transaction: any) => {
                const userSnap = await transaction.get(userRef);
                if (!userSnap.exists) {
                    throw new HttpsError("not-found", "User record not found.");
                }
                const userData = userSnap.data()!;

                if (userData.revenue < floatAmount) {
                    throw new HttpsError("failed-precondition", "Insufficient balance for withdrawal.");
                }

                const withdrawalMethodQuery = withdrawalMethodsCollection.where("user", "==", userId).limit(1);
                const withdrawalMethodSnap = await transaction.get(withdrawalMethodQuery);

                if (withdrawalMethodSnap.empty) {
                    throw new HttpsError("not-found", "Withdrawal method not found for this user. Please set up a payout method.");
                }
                const withdrawalData = withdrawalMethodSnap.docs[0].data();

                const firstName = userData.firstName?.trim();
                const lastName = userData.lastName?.trim();

                if (!firstName || !lastName) {
                    logger.error(`User ${userId} attempting withdrawal without full name in profile. First: '${userData.firstName}', Last: '${userData.lastName}'.`);
                    throw new HttpsError("failed-precondition", "Your first and last name are required in your profile to process withdrawals. Please update your profile information.");
                }
                const accountHolderName = `${firstName} ${lastName}`;


                const profilesResponse = await makeApiCallWithRetry(
                    `${WISE_API_BASE_URL}/v2/profiles`,
                    {
                        headers: {Authorization: `Bearer ${apiKey}`},
                    },
                    'wise-profiles'
                );
                if (!profilesResponse.ok) {
                    const errorData = await profilesResponse.json();
                    logger.error("Wise API Error - Fetching profiles:", errorData);
                    throw new HttpsError("internal", `Failed to fetch Wise profiles: ${errorData.errors?.[0]?.message || profilesResponse.statusText}`);
                }
                const profiles = await profilesResponse.json();
                const businessProfile = profiles.find((p: { type: string; }) => p.type === "BUSINESS");
                if (!businessProfile) {
                    throw new HttpsError("internal", "Business profile not found in Wise account configuration.");
                }
                const businessProfileId = businessProfile.id;

                const recipientPayload: any = {
                    accountHolderName: accountHolderName,
                    currency: withdrawalData.format === "us" ? "USD" : "GBP",
                    type: "",
                    details: {
                        address: {
                            country: withdrawalData.countryCode,
                            city: withdrawalData.city,
                            postCode: withdrawalData.postCode,
                            firstLine: withdrawalData.firstLine,
                        },
                        legalType: "PRIVATE",
                    },
                };

                if (withdrawalData.format === "iban") {
                    recipientPayload.type = "iban";
                    recipientPayload.details.iban = withdrawalData.iban;
                } else if (withdrawalData.format === "uk") {
                    recipientPayload.type = "sort_code";
                    recipientPayload.details.accountNumber = withdrawalData.accountNumber;
                    recipientPayload.details.sortCode = withdrawalData.sortCode;
                } else if (withdrawalData.format === "us") {
                    recipientPayload.type = "aba";
                    recipientPayload.details.accountNumber = withdrawalData.accountNumber;
                    recipientPayload.details.routingNumber = withdrawalData.routingNumber;
                    recipientPayload.details.accountType = withdrawalData.accountType;
                    recipientPayload.details.address.state = withdrawalData.state;
                } else {
                    throw new HttpsError("invalid-argument", "Unsupported withdrawal format specified in user's payout method.");
                }

                const createRecipientResponse = await makeApiCallWithRetry(
                    `${WISE_API_BASE_URL}/v1/accounts`,
                    {
                        method: "POST",
                        headers: {Authorization: `Bearer ${apiKey}`, "Content-Type": "application/json"},
                        body: JSON.stringify(recipientPayload),
                    },
                    'wise-create-recipient'
                );

                if (!createRecipientResponse.ok) {
                    const errorData = await createRecipientResponse.json();
                    logger.error("Wise API Error - Creating recipient:", errorData, {sentPayload: recipientPayload});
                    let errorMessage = "Failed to create Wise recipient.";
                    if (errorData.errors && errorData.errors.length > 0 && errorData.errors[0].message) {
                        errorMessage += ` ${errorData.errors[0].message}`;
                    } else {
                        errorMessage += ` ${createRecipientResponse.statusText}`;
                    }
                    throw new HttpsError("internal", errorMessage);
                }
                const recipientAccount = await createRecipientResponse.json();
                const recipientAccountId = recipientAccount.id;

                const quotePayload = {
                    sourceCurrency: recipientPayload.currency,
                    targetCurrency: recipientPayload.currency,
                    sourceAmount: floatAmount,
                    targetAccount: recipientAccountId,
                    profile: businessProfileId,
                };

                const quoteResponse = await makeApiCallWithRetry(
                    `${WISE_API_BASE_URL}/v3/profiles/${businessProfileId}/quotes`,
                    {
                        method: "POST",
                        headers: {Authorization: `Bearer ${apiKey}`, "Content-Type": "application/json"},
                        body: JSON.stringify(quotePayload),
                    },
                    'wise-create-quote'
                );
                if (!quoteResponse.ok) {
                    const errorData = await quoteResponse.json();
                    logger.error("Wise API Error - Creating quote:", errorData, {payload: quotePayload});
                    throw new HttpsError("internal", `Failed to create Wise quote: ${errorData.errors?.[0]?.message || quoteResponse.statusText}`);
                }
                const quote = await quoteResponse.json();
                const quoteId = quote.id;

                // create transfer
                const customerTransactionId = nodeCrypto.randomUUID();

                // reference guid
                const shortUserIdPart = userId.substring(userId.length - 6);
                const transferReference = `POUT-${shortUserIdPart}`;

                const transferPayload = {
                    targetAccount: recipientAccountId,
                    quoteUuid: quoteId,
                    customerTransactionId: customerTransactionId,
                    details: {
                        reference: transferReference,
                        transferPurpose: "verification.transfers.purpose.pay.suppliers",
                    },

                };
                const createTransferResponse = await makeApiCallWithRetry(
                    `${WISE_API_BASE_URL}/v1/transfers`,
                    {
                        method: "POST",
                        headers: {Authorization: `Bearer ${apiKey}`, "Content-Type": "application/json"},
                        body: JSON.stringify(transferPayload),
                    },
                    'wise-create-transfer'
                );

                if (!createTransferResponse.ok) {
                    const errorData = await createTransferResponse.json();
                    logger.error("Wise API Error - Creating transfer:", errorData, {payload: transferPayload});
                    throw new HttpsError("internal", `Failed to create Wise transfer: ${errorData.errors?.[0]?.message || createTransferResponse.statusText}`);
                }
                const transfer = await createTransferResponse.json();
                const transferId = transfer.id;

                const fundTransferResponse = await makeApiCallWithRetry(
                    `${WISE_API_BASE_URL}/v1/transfers/${transferId}/payments`,
                    {
                        method: "POST",
                        headers: {Authorization: `Bearer ${apiKey}`, "Content-Type": "application/json"},
                        body: JSON.stringify({type: "BALANCE"}),
                    },
                    'wise-fund-transfer'
                );
                if (!fundTransferResponse.ok) {
                    const errorData = await fundTransferResponse.json();
                    logger.error("Wise API Error - Funding transfer:", errorData, {transferId: transferId});
                    throw new HttpsError("internal", `Failed to fund Wise transfer: ${errorData.errors?.[0]?.message || fundTransferResponse.statusText}. Transfer ID: ${transferId}`);
                }

                transaction.update(userRef, {
                    revenue: FieldValue.increment(-floatAmount),
                });

                const earningLogRef = db.collection("tutor_earnings_log").doc();
                transaction.set(earningLogRef, {
                    userId: userId,
                    date: admin.firestore.Timestamp.now(),
                    type: "Fund Withdrawal",
                    amount: floatAmount,
                    method: "Wise Direct",
                    transferId: transferId,
                    status: "Processed",
                });

                logger.info(`Wise payout successful for user ${userId}, amount ${floatAmount}, transferId ${transferId}.`);
                return {status: true, message: "Withdrawal processed successfully via Wise.", transferId: transferId};
            });
        } catch (error: any) {
            logger.error(`Wise payout transaction failed for user ${userId}, amount ${amount}: ${error.message}`, {
                details: error.details,
                code: error.code
            });
            if (error instanceof HttpsError) {
                throw error;
            }
            throw new HttpsError("internal", "An internal error occurred during the withdrawal process.", {originalErrorMessage: error.message});
        }
    }
);

exports.processPayment = onRequest(
    {
        secrets: [STRIPE_API_KEY],
    },
    async (request: Request, response: Response) => {
        cors(request, response, async () => {
            const {amount, currency} = request.body;
            const stripe = new Stripe(STRIPE_API_KEY.value(), {
                typescript: true,
            });

            try {
                const paymentIntent = await stripe.paymentIntents.create({
                    amount: Number(amount) * 100,
                    currency,
                });
                return response.json({
                    status: true,
                    paymentIntent: paymentIntent.client_secret,
                });
            } catch (error) {
                return response.json({status: false, error});
            }
        });
    }
);

exports.refundPayment = onRequest(
    {
        secrets: [STRIPE_API_KEY],
    },
    async (request: Request, response: Response) => {
        cors(request, response, async () => {
            const {paymentIntent} = request.body;
            const stripe = new Stripe(STRIPE_API_KEY.value(), {
                typescript: true,
            });
            try {
                const refund = await stripe.refunds.create({
                    payment_intent: paymentIntent,
                });
                return response.json({status: true, refund});
            } catch (error) {
                return response.json({status: false, error});
            }
        });
    }
);

exports.addIncompleteExam = onRequest(async (request: Request, response: Response) => {
    cors(request, response, async () => {
        const {user_id, examId, language, examsAttempted, examsCompleted} =
            JSON.parse(request.body);
        try {
            const batch = db.batch();
            const docRef = db.collection("exams_completed").doc();
            batch.set(
                docRef,
                {
                    score: 0,
                    user_id: user_id,
                    completed_at: new Date().getTime(),
                    status: "Incomplete",
                    payment: "Unpaid",
                    language,
                    examId,
                },
                {
                    merge: true,
                }
            );

            batch.set(
                db.collection("users").doc(user_id),
                {
                    attempted_exams: examsAttempted ? examsAttempted + 1 : 1,
                    completed_exams: examsCompleted ? examsCompleted + 1 : 1,
                },
                {merge: true}
            );

            await batch.commit();
            return response.json({
                status: true,
            });
        } catch (error) {
            logger.log(error);
            return response.json({
                status: false,
            });
        }
    });
});

exports.cleanupOldQuestions = functions.pubsub
    .schedule("every 15 minutes")
    .onRun(async (context: any) => {
        const collectionRef = db.collection("tutor_exclusive_questions");
        const now = Date.now();
        const cutoffTime = now - 15 * 60 * 1000;
        const BATCH_SIZE = 100;
        
        try {
            let lastDoc = null;
            let totalProcessed = 0;
            let totalDeleted = 0;
            
            do {
                let query = collectionRef.orderBy('dateClaimed', 'asc').limit(BATCH_SIZE);
                if (lastDoc) {
                    query = query.startAfter(lastDoc);
                }
                
                const snapshot = await query.get();
                
                if (snapshot.empty) {
                    logger.log("No more documents to process.");
                    break;
                }
                
                const batch = db.batch();
                let batchCount = 0;
                
                snapshot.docs.forEach((doc: any) => {
                    const data = doc.data();
                    const dateClaimed = data.dateClaimed;
                    
                    if (dateClaimed && dateClaimed <= cutoffTime) {
                        logger.log(`Marking document for deletion: ${doc.id}, dateClaimed: ${dateClaimed}`);
                        batch.delete(doc.ref);
                        
                        if (data.questionId) {
                            batch.update(db.collection("users_questions").doc(data.questionId), {
                                exclusive: false,
                            });
                        }
                        
                        batchCount++;
                        totalDeleted++;
                    }
                    
                    lastDoc = doc;
                    totalProcessed++;
                });
                
                if (batchCount > 0) {
                    await batch.commit();
                    logger.log(`Committed batch with ${batchCount} deletions`);
                }
                
                if (snapshot.docs.length < BATCH_SIZE) {
                    break;
                }
                
            } while (totalProcessed < 1000);
            
            logger.log(`Cleanup completed. Processed: ${totalProcessed}, Deleted: ${totalDeleted}`);
            return null;
            
        } catch (error) {
            logger.error("Error cleaning up documents:", error);
            return null;
        }
    });

exports.createTransfer = onRequest(
    {
        secrets: [WISE_API_KEY],
    },
    async (request: Request, response: Response) => {
        cors(request, response, async () => {
            const {
                iban,
                amount,
                source_currency,
                dest_currency,
                accountHolder,
                format,
                accountNumber,
                sortCode,
                city,
                countryCode,
                postCode,
                firstLine,
            } = request.body;
            const apiKey = WISE_API_KEY.value();

            try {
                const profileResponse = await fetch(
                    "https://api.sandbox.transferwise.tech/v2/profiles",
                    {
                        method: "GET",
                        headers: {
                            Authorization: `Bearer ${apiKey}`,
                        },
                    }
                );
                const profiles = await profileResponse.json();
                const businessProfile = profiles.find(
                    (profile: { type: string }) => profile.type === "BUSINESS"
                );
                const personalProfile = profiles.find(
                    (profile: { type: string }) => profile.type === "PERSONAL"
                );

                if (!businessProfile || !personalProfile)
                    throw new Error("Both business and personal profiles are required");
                const businessProfileId = businessProfile.id;
                const personalProfileId = personalProfile.id;

                const quoteResponse = await fetch(
                    `https://api.sandbox.transferwise.tech/v3/profiles/${businessProfileId}/quotes`,
                    {
                        method: "POST",
                        headers: {
                            Authorization: `Bearer ${apiKey}`,
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            profile: businessProfileId,
                            sourceCurrency: source_currency,
                            targetCurrency: dest_currency,
                            sourceAmount: amount,
                        }),
                    }
                );
                const quote = await quoteResponse.json();
                if (!quote.id) throw new Error("Failed to create quote");

                const accountsResponse = await fetch(
                    `https://api.sandbox.transferwise.tech/v2/accounts/?currency=${dest_currency}`,
                    {
                        method: "GET",
                        headers: {
                            Authorization: `Bearer ${apiKey}`,
                        },
                    }
                );
                const recipientAccounts = await accountsResponse.json();
                if (recipientAccounts.size == null)
                    throw new Error("Failed to get recipient accounts");
                const recipientAccount = recipientAccounts.content.find(
                    (account: { profileId: any }) =>
                        account.profileId == personalProfileId
                );

                const data: any = {
                    accountHolderName: accountHolder,
                    currency: dest_currency,
                    type: format === "iban" ? "iban" : "sort_code",
                    profile: personalProfileId,
                    ownedByCustomer: true,
                    details: {
                        address: {
                            city,
                            countryCode,
                            postCode,
                            firstLine,
                        },
                        legalType: "PRIVATE",
                    },
                };

                if (format === "iban") {
                    data.iban = iban;
                } else if (format === "uk") {
                    data.accountNumber = accountNumber;
                    data.sortCode = sortCode;
                }

                if (recipientAccount.id == null) {
                    const accountResponse = await fetch(
                        "https://api.sandbox.transferwise.tech/v1/accounts",
                        {
                            method: "POST",
                            headers: {
                                Authorization: `Bearer ${apiKey}`,
                                "Content-Type": "application/json",
                            },
                            body: JSON.stringify(data),
                        }
                    );
                    const recipientAccount = await accountResponse.json();
                    if (!recipientAccount.id)
                        throw new Error("Failed to create recipient account");
                }

                return response.json({
                    status: true,
                    profileId: personalProfileId,
                    quoteId: quote.id,
                    recipientAccountId: recipientAccount.id,
                    amount: amount,
                    sourceCurrency: source_currency,
                    destCurrency: dest_currency,
                    accountHolder: accountHolder,
                    iban: iban,
                });
            } catch (error: any) {
                return response.json({
                    status: false,
                    error: "Failed to prepare transfer",
                    details: error.message,
                });
            }
        });
    }
);

exports.confirmTransfer = onRequest(
    {
        secrets: [WISE_API_KEY],
    },
    async (request: Request, response: Response) => {
        cors(request, response, async () => {
            const {profileId, quoteId, recipientAccountId} = request.body;
            const apiKey = WISE_API_KEY.value();
            console.log(
                `profileId: ${profileId}, quoteId: ${quoteId}, recipientAccountId: ${recipientAccountId}`
            );

            try {
                const updateQuoteResponse = await fetch(
                    `https://api.sandbox.transferwise.tech/v3/profiles/${profileId}/quotes/${quoteId}`,
                    {
                        method: "PATCH",
                        headers: {
                            Authorization: `Bearer ${apiKey}`,
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            targetAccount: recipientAccountId,
                        }),
                    }
                );
                const updateQuote = await updateQuoteResponse.json();
                if (!updateQuote.id) throw new Error("Failed to update quote");

                const guidResponse = await fetch(
                    `https://www.uuidgenerator.net/api/guid`
                );
                if (!guidResponse.ok) throw new Error("Failed to generate GUID");
                const GUID = await guidResponse.text();

                const createTransferResponse = await fetch(
                    `https://api.sandbox.transferwise.tech/v1/transfers`,
                    {
                        method: "POST",
                        headers: {
                            Authorization: `Bearer ${apiKey}`,
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            targetAccount: recipientAccountId,
                            quoteUuid: quoteId,
                            customerTransactionId: GUID,
                            details: {
                                reference: "my ref",
                                transferPurpose: "verification.transfers.purpose.pay.bills",
                            },
                        }),
                    }
                );

                const createTransfer = await createTransferResponse.json();
                if (!createTransfer.id) throw new Error("Failed to create transfer");

                return response.json({
                    status: true,
                    ...createTransfer,
                });
            } catch (error: any) {
                return response.json({
                    status: false,
                    error: "Failed to prepare transfer",
                    details: error.message,
                });
            }
        });
    }
);