import {Achievement, RankDefinition} from "@/src/Types";

export const ACHIEVEMENTS: Achievement[] = [
    {
        id: 'achievement1',
        icon: '/icons/profile/champion-5.svg',
        title: 'completed_registration',
        description: '',
        value: 0,
        max: 100,
        isRegistration: true
    },
    {
        id: 'achievement2',
        icon: '/icons/profile/champion-1.svg',
        title: 'champion_novice',
        description: 'answered_100_questions',
        value: 0,
        max: 100
    },
    {
        id: 'achievement3',
        icon: '/icons/profile/champion-2.svg',
        title: 'champion_adept',
        description: 'answered_1000_questions',
        value: 0,
        max: 1000
    },
    {
        id: 'achievement4',
        icon: '/icons/profile/champion-3.svg',
        title: 'champion_expert',
        description: 'answered_5000_questions',
        value: 0,
        max: 5000
    },
    {
        id: 'achievement5',
        icon: '/icons/profile/champion-4.svg',
        title: 'champion_master',
        description: 'answered_10000_questions',
        value: 0,
        max: 10000
    },
    {
        id: 'achievement6',
        icon: '/icons/profile/champion-5.svg',
        title: 'streak_starter',
        description: 'achieved_day_streak',
        value: 0,
        max: 7
    },
    {
        id: 'achievement7',
        icon: '/icons/profile/champion-5.svg',
        title: 'streak_pro',
        description: 'achieved_30_day_streak',
        value: 0,
        max: 30
    },
    {
        id: 'achievement8',
        icon: '/icons/profile/champion-1.svg',
        title: 'top_Subject_Math',
        description: 'become_top_in_Math',
        value: 0,
        max: 100
    },
    {
        id: 'achievement9',
        icon: '/icons/profile/champion-2.svg',
        title: 'top_Subject_Science',
        description: 'Become_top_in_Science',
        value: 0,
        max: 100
    },
    {
        id: 'achievement10',
        icon: '/icons/profile/champion-3.svg',
        title: 'helping_hand',
        description: 'Helped 50 students',
        value: 0,
        max: 50
    },
    {
        id: 'achievement11',
        icon: '/icons/profile/champion-4.svg',
        title: 'super_helper',
        description: 'helped_200_students',
        value: 0,
        max: 200
    },
    {
        id: 'achievement12',
        icon: '/icons/profile/champion-5.svg',
        title: 'perfect_rate',
        description: 'acceptance_rate_month',
        value: 0,
        max: 100
    },
];

export const RANK_DEFINITIONS: RankDefinition[] = [
    {
        icon: '/icons/profile/badge-bronze.png',
        name: 'answer_apprentice',
        description: 'less_than_500_answers',
        rewards: 'bronze_frame',
        minAnswers: 0,
        nextRankAnswers: 500
    },
    {
        icon: '/icons/profile/badge-silver.png',
        name: 'homework_hero',
        description: '500_answers',
        rewards: 'silver_frame',
        bonus: '50_Bonus',
        minAnswers: 500,
        nextRankAnswers: 1500
    },
    {
        icon: '/icons/profile/badge-gold.png',
        name: 'knowledge_knight',
        description: '1500_answers',
        rewards: 'gold_frame',
        bonus: '100_Bonus',
        minAnswers: 1500,
        nextRankAnswers: 3000
    },
    {
        icon: '/icons/profile/badge-diamond.png',
        name: 'wisdom_wizard',
        description: '3000_answers',
        rewards: 'diamond_frame',
        bonus: '300_Bonus',
        minAnswers: 3000
    },
];

export const RANK_SLIDER_SETTINGS = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 4,
    slidesToScroll: 1,
    arrows: false,
    responsive: [
        {breakpoint: 1229, settings: {slidesToShow: 4}},
        {breakpoint: 959, settings: {slidesToShow: 3}},
        {breakpoint: 659, settings: {slidesToShow: 1, centerMode: true, centerPadding: "15px"}},
    ],
};

export const PROFILE_FIELDS = [
    {key: "acceptanceRate", label: "Acceptance Rate", placeholder: "e.g. 85", type: "number"},
    {key: "studentsHelped", label: "Students Helped", placeholder: "e.g. 120", type: "number"},
    {
        key: "topSubject", label: "Top Subject", placeholder: "e.g. Math", type: "dropdown",
        options: [
            {label: "Maths", value: "Maths"},
            {label: "Reading", value: "Reading"},
            {label: "Writing", value: "Writing"},
            {label: "Science", value: "Science"},
            {label: "Social Studies", value: "Social Studies"},
            {label: "Spelling", value: "Spelling"},
            {label: "Grammar", value: "Grammar"},
            {label: "Art", value: "Art"},
            {label: "Music", value: "Music"},
            {label: "Media Studies", value: "Media Studies"},
            {label: "Handwriting", value: "Handwriting"},
            {label: "Computer/Technology Skills", value: "Computer/Technology Skills"},
            {label: "Civics", value: "Civics"},
        ]
    },
    {key: "dailyStreak", label: "Daily Challenges Streak", placeholder: "e.g. 12", type: "number"},
    {
        key: "monthlyRank", label: "Monthly Rank", placeholder: "e.g. 5", type: "dropdown",
        options: [
            {label: "default", value: "0"},
            {label: "answer_apprentice", value: "1"},
            {label: "homework_hero", value: "2"},
            {label: "knowledge_knight", value: "3"},
            {label: "wisdom_wizard", value: "4"},
        ]
    },
    {key: "monthlyAnswers", label: "Monthly Answers", placeholder: "e.g. 80", type: "number"},
];
