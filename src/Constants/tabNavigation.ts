import {Tab} from "@/src/Types";

export const STUDENT_TABS: Tab[] = [
    {name: 'ask', icon: '/icons/menu/house-icon.svg', path: '/ask', index: 0},
    {name: 'history', icon: '/icons/menu/clock-icon.svg', path: '/my-questions', index: 1},
    {name: 'buy', icon: '/icons/menu/handbag-icon.svg', path: '/packs', index: 2},
    {name: 'settings', icon: '/icons/menu/gear-icon.svg', path: '/settings', index: 3},
    {name: 'transactions', icon: '/icons/menu/dollar-icon.svg', path: '/transactions', index: 4},
];

export const TUTOR_TABS: Tab[] = [
    {name: 'answer_question', icon: '/icons/menu/house-icon.svg', path: '/questions', index: 0},
    {name: 'history', icon: '/icons/menu/clock-icon.svg', path: '/history', index: 1},
    {name: 'earnings', icon: '/icons/menu/dollar-icon.svg', path: '/earnings', index: 2},
    {name: 'settings', icon: '/icons/menu/gear-icon.svg', path: '/tutor-settings', index: 3},
    {name: 'registration', icon: '/icons/menu/register-icon.svg', path: '/registration', index: 4},
];

export const ADMIN_TABS: Tab[] = [
    { name: 'dashboard', icon: '/icons/menu/house-icon.svg', path: '/admin/dashboard', index: 0 },  
    { name: 'users', icon: '/icons/menu/users-icon.svg', path: '/admin/users', index: 1 },
    { name: 'reports', icon: '/icons/menu/admin-applicant-icon.svg', path: '/admin/reports', index: 2 },
    { name: 'settings', icon: '/icons/menu/gear-icon.svg', path: '/admin/settings', index: 3 },
];

// Lookup maps O(1)
export const STUDENT_PATHS = STUDENT_TABS.map(tab => tab.path);
export const TUTOR_PATHS = TUTOR_TABS.map(tab => tab.path);
export const ADMIN_PATHS = ADMIN_TABS.map(tab => tab.path);

export const STUDENT_PATH_TO_TAB_INDEX = STUDENT_TABS.reduce((acc, tab) => {
    acc[tab.path] = tab.index;
    return acc;
}, {} as Record<string, number>);

export const TUTOR_PATH_TO_TAB_INDEX = TUTOR_TABS.reduce((acc, tab) => {
    acc[tab.path] = tab.index;
    return acc;
}, {} as Record<string, number>);

export const ADMIN_PATH_TO_TAB_INDEX = ADMIN_TABS.reduce((acc, tab) => {
    acc[tab.path] = tab.index;
    return acc;
}, {} as Record<string, number>);

export const SPECIAL_ROUTES = {
    PAYMENT: '/payment',
    PROFILE: '/profile',
    PAYOUT_METHODS: '/payout-method'
} as const;

export const SPECIAL_AUTH_PATHS = Object.values(SPECIAL_ROUTES);

export const PUBLIC_ROUTES = {
    LOGIN: '/login',
    SIGNUP: '/signup',
    FORGOT_PASSWORD: '/forgot-password',
    TERMS: '/terms',
    PRIVACY: '/privacy',
    HELP: '/help',
    BECOME_A_STUDENT: '/become-a-student'
} as const;

export const PUBLIC_PATHS = Object.values(PUBLIC_ROUTES);

export const ADMIN_ROUTES = {
    DASHBOARD: '/admin/dashboard',
    USERS: '/admin/users',
    REPORTS: '/admin/reports'
} as const;

export const TAB_ROUTES = {
    student: STUDENT_TABS,
    tutor: TUTOR_TABS,
    admin: ADMIN_TABS
} as const;

export const isPublicRoute = (pathname: string): boolean => {
    return PUBLIC_PATHS.includes(pathname as any);
};

export const isStudentRoute = (pathname: string): boolean => {
    return STUDENT_PATHS.includes(pathname);
};

export const isTutorRoute = (pathname: string): boolean => {
    return TUTOR_PATHS.includes(pathname) ||
        pathname === SPECIAL_ROUTES.PROFILE ||
        pathname === SPECIAL_ROUTES.PAYOUT_METHODS;
};

export const isAdminRoute = (pathname: string): boolean => {
    return ADMIN_PATHS.includes(pathname as any);
};

export const isSpecialAuthRoute = (pathname: string): boolean => {
    return SPECIAL_AUTH_PATHS.includes(pathname as any);
};

export const isProtectedRoute = (pathname: string, userId?: string): boolean => {
    if (pathname === '/') {
        return true;
    }

    return isStudentRoute(pathname) ||
        isTutorRoute(pathname) ||
        isAdminRoute(pathname) ||
        isSpecialAuthRoute(pathname);
};