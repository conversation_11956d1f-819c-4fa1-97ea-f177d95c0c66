'use client';

import React, { useMemo, useEffect } from "react";
import {
  createTheme,
  ThemeProvider as Mui<PERSON><PERSON><PERSON><PERSON>rovider,
  StyledEngineProvider,
  type Theme,
} from "@mui/material/styles";
import * as colors from "@mui/material/colors";
import CssBaseline from "@mui/material/CssBaseline";

const themeConfig = {
  palette: {
    mode: "light" as const,
    primary: {
      main: "#3F3F3F",
    },
    secondary: {
      main: colors.pink[500],
    },
    background: {
      default: "#fff",
      paper: "#fff",
    },
  },
  typography: {
    fontSize: 14,
    fontFamily: "var(--font-inter), Arial, sans-serif",
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 960,
      lg: 1200,
      xl: 1920,
    },
  },
} as const;

const createAppTheme = (): Theme => {
  return createTheme({
    palette: themeConfig.palette,
    typography: themeConfig.typography,
    breakpoints: themeConfig.breakpoints,
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          '#__next': {
            minHeight: '100vh',
            display: 'flex',
            flexDirection: 'column',
            '& > *': {
              flexShrink: 0,
            },
          },
          '*': {
            boxSizing: 'border-box',
          },
          'html, body': {
            margin: 0,
            padding: 0,
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: 'none', 
          },
        },
      },
    },
  });
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const theme = useMemo(() => createAppTheme(), []);

  useEffect(() => {
    const jssStyles = document.querySelector('#jss-server-side');
    if (jssStyles && jssStyles.parentElement) {
      jssStyles.parentElement.removeChild(jssStyles);
    }
  }, []);

  return (
      <StyledEngineProvider injectFirst>
        <MuiThemeProvider theme={theme}>
          <CssBaseline enableColorScheme />
          {children}
        </MuiThemeProvider>
      </StyledEngineProvider>
  );
};