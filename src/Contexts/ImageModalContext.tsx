"use client";

import { createContext, useContext, useState, ReactNode } from "react";
import { Dialog, DialogContent } from "@mui/material";

type ImageModalContextType = {
    openImage: (src: string) => void;
};

export const ImageModalContext = createContext<ImageModalContextType | undefined>(undefined);

export const ImageModalProvider = ({ children }: { children: ReactNode }) =>
{
    const [imageSrc, setImageSrc] = useState<string | null>(null);

    const openImage = (src: string) => setImageSrc(src);
    const closeModal = () => setImageSrc(null);

    return (
        <ImageModalContext.Provider value={{ openImage }}>
            {children}
            {imageSrc ?
                <Dialog
                    open={!!imageSrc}
                    onClose={closeModal}
                    maxWidth="xs"
                    fullWidth
                    PaperProps={{
                        style: { maxWidth: "600px" },
                    }}
                >
                    <DialogContent>
                        {imageSrc && <img src={imageSrc} alt="Preview" style={{ width: "100%", height: "auto" }} />}
                    </DialogContent>
                </Dialog> : ''
            }
        </ImageModalContext.Provider>
    );
};

export const useImageModal = () => {
    const context = useContext(ImageModalContext);
    if (!context) throw new Error("useImageModal must be used within an ImageModalProvider");
    return context;
};