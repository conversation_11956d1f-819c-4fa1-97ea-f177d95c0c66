"use client";
import React, {createContext, FC, useEffect, useRef, useState, useCallback} from "react";
import {listenToDocument, onAuthStateChangedListener, signOutUser,} from "@/src/Firebase/firebase.utils";
import {LocalizationProvider} from "@mui/x-date-pickers";
import {AdapterMoment} from "@mui/x-date-pickers/AdapterMoment";
import {User} from "firebase/auth";
import { useRouter } from "next/navigation";

const setCookieUserType = (userType: string) => {
    if (typeof document !== 'undefined') {
        document.cookie = `userType=${userType}; path=/; max-age=${60 * 60 * 24 * 7}`; // 7 days
    }
};

const clearCookieUserType = () => {
    if (typeof document !== 'undefined') {
        document.cookie = 'userType=; path=/; max-age=0';
    }
};

type UserContextType = {
    user: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        questions: number;
        type: string;
        provider: string;
        examsCompleted: number;
        examsAttempted: number;
        isApplicant: boolean;
        isAvailable: boolean;
        phoneNumber: string;
        certificateNumber: string;
        dateOfBirth: string;
        birthMonth: string;
        birthDay: string;
        birthYear: string;
        workTitle: string;
        educationLevel: string;
        tutorExperience: string;
        billingAddress: string;
        applicantQuestionAnswered: number;
        revenue: number;
        imgId: string;
        imgUrl: string;
        profileImageUrl?: string;
        pendingWithdrawals: number;
        pendingWithdrawalAmount: number;
        earnings: number;
        country: string;
        address1: string;
        address2: string;
        city: string;
        state: string;
        postalCode: string;
        statistics?: any;
        achievements?: any;
        seenAchievements?: Record<string, boolean>;
        createdAt?: any;
    };
    setUser: React.Dispatch<React.SetStateAction<UserContextType["user"]>>;
    fetching: boolean | null;
    setFetching: React.Dispatch<React.SetStateAction<boolean | null>>;
    initialLoad: boolean;
    isLoggingOut: boolean;
    setIsLoggingOut: React.Dispatch<React.SetStateAction<boolean>>;
    langSwitch: boolean;
    setLangSwitch: React.Dispatch<React.SetStateAction<boolean>>;
     logout: () => Promise<void>;
};

type Props = {
    children: React.ReactNode;
};

const defaultUser = {
    id: "", email: "", firstName: "", lastName: "", questions: 0, type: "",
    provider: "", examsCompleted: 0, examsAttempted: 0, applicantQuestionAnswered: 0,
    isAvailable: true, isApplicant: true, phoneNumber: "", certificateNumber: "",
    billingAddress: "", dateOfBirth: "", birthMonth: "", birthDay: "", birthYear: "",
    workTitle: "", educationLevel: "", tutorExperience: "", revenue: 0, imgId: "",
    pendingWithdrawalAmount: 0, imgUrl: "", profileImageUrl: "", pendingWithdrawals: 0, earnings: 0,
    country: "", address1: "", address2: "", city: "", state: "", postalCode: "",
    statistics: {}, achievements: {}, seenAchievements: {}, createdAt: null,
};

export const UserContext = createContext<UserContextType>({
    user: defaultUser,
    setUser: () => {
    },
    fetching: null,
    setFetching: () => {
    },
    initialLoad: true,
    isLoggingOut: false,
    setIsLoggingOut: () => {
    },
     logout: async () => {
    },
    langSwitch: false,
    setLangSwitch: () => {
    },
});

const UserProvider: FC<Props> = ({children}: Props) => {
    const [user, setUser] = useState<UserContextType["user"]>(defaultUser);
    const [fetching, setFetching] = useState<boolean | null>(true);
    const [initialLoad, setInitialLoad] = useState(true);
    const [isLoggingOut, setIsLoggingOut] = useState(false);
    const [langSwitch, setLangSwitch] = useState(false);
    const [, setConnectionError] = useState<string | null>(null);
    const [, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('connecting');
    const router = useRouter();
    
    const unsubscribeUserDoc = useRef<(() => void) | null>(null);
    const hasInitialized = useRef(false);
    const retryTimeout = useRef<NodeJS.Timeout | null>(null);
    const retryCount = useRef(0);
    const maxRetries = 3;

    const clearUser = useCallback(() => {
        setUser(defaultUser);
        clearCookieUserType();
        setConnectionError(null);
        setConnectionStatus('disconnected');
        if (unsubscribeUserDoc.current) {
            unsubscribeUserDoc.current();
            unsubscribeUserDoc.current = null;
        }
        if (retryTimeout.current) {
            clearTimeout(retryTimeout.current);
            retryTimeout.current = null;
        }
    }, [setUser, setConnectionError, setConnectionStatus]);

    const setupUserListener = useCallback((firebaseUser: User) => {
        if (unsubscribeUserDoc.current) {
            unsubscribeUserDoc.current();
            unsubscribeUserDoc.current = null;
        }

        setConnectionStatus('connecting');
        setConnectionError(null);
        retryCount.current = 0;

        unsubscribeUserDoc.current = listenToDocument(
            firebaseUser.uid, 
            "users", 
            (docResult) => {
                if (docResult.status && docResult.exists) {
                    const data: any = docResult.data || {};
                    const userType = data.type ? data.type : "user";

                    setUser({
                        id: firebaseUser.uid,
                        email: firebaseUser.email || data.email || "",
                        firstName: data.firstName || "",
                        lastName: data.lastName || "",
                        questions: data.questions || 0,
                        revenue: data.revenue || 0,
                        phoneNumber: data.phoneNumber || "",
                        dateOfBirth: data.dateOfBirth || "",
                        birthMonth: data.birthMonth || "",
                        birthDay: data.birthDay || "",
                        birthYear: data.birthYear || "",
                        workTitle: data.workTitle || "",
                        educationLevel: data.educationLevel || "",
                        tutorExperience: data.tutorExperience || "",
                        applicantQuestionAnswered: data.applicantQuestionAnswered || 0,
                        billingAddress: data.billingAddress || "",
                        certificateNumber: data.certificateNumber || "",
                        pendingWithdrawals: data.pendingWithdrawals || 0,
                        pendingWithdrawalAmount: data.pendingWithdrawalAmount || 0,
                        earnings: data.earnings || 0,
                        isAvailable: data.isAvailable === true || String(data.isAvailable).toLowerCase() === "true",
                        isApplicant: data.isApplicant === true || String(data.isApplicant).toLowerCase() === "true",
                        type: userType,
                        provider: firebaseUser.providerData[0]?.providerId || "",
                        examsCompleted: data.completed_exams || 0,
                        examsAttempted: data.attempted_exams || 0,
                        imgId: data.img_id || "",
                        imgUrl: data.profileImageUrl || data.imgUrl || "",
                        profileImageUrl: data.profileImageUrl || data.imgUrl || "",
                        country: data.country || "",
                        address1: data.address1 || "",
                        address2: data.address2 || "",
                        city: data.city || "",
                        state: data.state || "",
                        postalCode: data.postalCode || "",
                        statistics: data.statistics || {},
                        achievements: data.achievements || {},
                        seenAchievements: data.seenAchievements || {},
                        createdAt: data.createdAt || null,
                    });

                    setCookieUserType(userType);
                    setConnectionStatus('connected');
                    setConnectionError(null);
                    retryCount.current = 0;
                    
                } else if (!docResult.exists) {
                    console.warn(`user doc not found ${firebaseUser.uid}`);

                    const userType = "user";
                    setUser(prevUser => ({
                        ...defaultUser,
                        id: firebaseUser.uid,
                        email: firebaseUser.email || "",
                        provider: firebaseUser.providerData[0]?.providerId || "",
                        type: userType,
                    }));

                    setCookieUserType(userType);
                    setConnectionStatus('connected');
                    setConnectionError(null);
                    
                } else {
                    console.error("error fetching user doc:", docResult);
                    setConnectionError(docResult.error || 'Failed to load user data');
                    setConnectionStatus('disconnected');
                    
                    // Retry logic for connection failures
                    if (retryCount.current < maxRetries) {
                        retryCount.current++;
                        console.log(`Retrying user document connection (${retryCount.current}/${maxRetries})...`);
                        
                        if (retryTimeout.current) {
                            clearTimeout(retryTimeout.current);
                        }
                        
                        retryTimeout.current = setTimeout(() => {
                            setupUserListener(firebaseUser);
                        }, 2000 * retryCount.current);
                    }
                }

                setFetching(false);
                setInitialLoad(false);
                hasInitialized.current = true;
            },
            { 
                maxRetries: 1, // Let our custom retry logic handle it
                retryDelay: 1000,
                includeMetadataChanges: false 
            }
        );
    }, []);

    useEffect(() => {
        const authUnsubscribe = onAuthStateChangedListener(
            async (firebaseUser: User | null) => {
                if (!hasInitialized.current) {
                    setFetching(true);
                }

                // Clean up previous connections
                if (unsubscribeUserDoc.current) {
                    unsubscribeUserDoc.current();
                    unsubscribeUserDoc.current = null;
                }
                if (retryTimeout.current) {
                    clearTimeout(retryTimeout.current);
                    retryTimeout.current = null;
                }

                if (!firebaseUser) {
                    clearUser();
                    await signOutUser();
                    setFetching(false);
                    setInitialLoad(false);
                    setIsLoggingOut(false);
                    hasInitialized.current = true;
                } else {
                    setupUserListener(firebaseUser);
                }
            }
        );

        return () => {
            authUnsubscribe();
            if (unsubscribeUserDoc.current) {
                unsubscribeUserDoc.current();
                unsubscribeUserDoc.current = null;
            }
            if (retryTimeout.current) {
                clearTimeout(retryTimeout.current);
                retryTimeout.current = null;
            }
        };
    }, [setupUserListener, clearUser]);
 const logout = useCallback(async () => {
        if (isLoggingOut) return;

        setIsLoggingOut(true);

        try {
            clearUser();
            await signOutUser();
            router.push("/login");
        } catch (error) {
            console.error('logout error:', error);
        } finally {
            setTimeout(() => {
                setIsLoggingOut(false);
            }, 100);
        }
    }, [isLoggingOut, clearUser, router]);
    return (
        <LocalizationProvider dateAdapter={AdapterMoment}>
            <UserContext.Provider
                value={{
                    user,
                    setUser,
                    fetching,
                    setFetching,
                    initialLoad,
                    isLoggingOut,
                    setIsLoggingOut,
                    setLangSwitch,
                    langSwitch,
                     logout,
                }}
            >
                {children}
            </UserContext.Provider>
        </LocalizationProvider>
    );
};

export default UserProvider;