'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { usePathname } from 'next/navigation';
import { NavigationContextType } from '../Types';

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

const STUDENT_PATH_TO_TAB: Record<string, number> = {
  '/ask': 0,
  '/my-questions': 1,
  '/packs': 2,
  '/settings': 3,
  '/transactions': 4,
};

const TUTOR_PATH_TO_TAB: Record<string, number> = {
  '/questions': 0,
  '/history': 1,
  '/earnings': 2,
  '/tutor-settings': 3,
  '/registration': 4,
};

const NAVBAR_PATHS = [
  '/ask', '/my-questions', '/packs', '/settings', '/transactions',
  '/questions', '/history', '/earnings', '/tutor-settings', '/registration',
  '/profile', '/payout-method'
];

const EXCLUDED_PATHS = [
  '/login', '/signup', '/payment',
  '/admin/dashboard', '/admin/users', '/admin/applicant-questions',
  '/admin/user-questions', '/admin/settings', '/admin/history'
];

export const NavigationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState(0);
  const [userType, setUserType] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(null);

  const shouldShowNavbar =
      !EXCLUDED_PATHS.includes(pathname) &&
      (pathname === '/' || NAVBAR_PATHS.includes(pathname)) &&
      userId !== null;

  // Update active tab based on pathname and user type
  useEffect(() => {
    if (!userType || !shouldShowNavbar) return;

    let newActiveTab = 0;

    if (userType === 'tutor' && TUTOR_PATH_TO_TAB[pathname] !== undefined) {
      newActiveTab = TUTOR_PATH_TO_TAB[pathname];
    } else if (userType === 'user' && STUDENT_PATH_TO_TAB[pathname] !== undefined) {
      newActiveTab = STUDENT_PATH_TO_TAB[pathname];
    } else if (pathname === '/') {
      newActiveTab = userType === 'tutor' ? 0 : 0; 
    }

    setActiveTab(newActiveTab);
  }, [pathname, userType, shouldShowNavbar]);

  const updateUserInfo = (newUserId: string | null, newUserType: string | null) => {
    setUserId(newUserId);
    setUserType(newUserType);

    // Reset tab when user changes
    if (!newUserId) {
      setActiveTab(0);
    }
  };

  const value = {
    activeTab,
    setActiveTab,
    userType,
    userId,
    shouldShowNavbar,
    updateUserInfo,
  };

  return (
      <NavigationContext.Provider value={value}>
        {children}
      </NavigationContext.Provider>
  );
};