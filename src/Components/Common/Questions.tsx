"use client";
import React, { useCallback, useContext, useEffect, useLayoutEffect, useMemo, useState} from "react";
import {UserContext} from "@/src/Contexts/UserContext";
import styles from "@/src/styles/home.module.css";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import {useRouter, useSearchParams} from "next/navigation";
import Spinner from "@/src/Components/Widgets/Spinner";
import {Accordion, AccordionSummary, ToggleButton, ToggleButtonGroup,} from "@mui/material";
import { getAllDocs, queryData} from "@/src/Firebase/firebase.utils";
import {isArray, zeroPadId} from "../../Utils/helpers";
import SearchBox from "../Widgets/SearchBox";
import SelectDates from "../Widgets/SelectDates";
import NotificationModal from "@/src/Components/Modals/NotificationModal";
import UserProfile from "../Admin/UserProfile";
import TableCustomPagination from "@/src/Components/Table/TableCustomPagination";
import CancelButton from "@/src/Components/Buttons/CancelButton";
import moment from "moment";
import {useImageModal} from "@/src/Contexts/ImageModalContext";
import TextToggle from "@/src/Components/Widgets/TextToggle";
import { useTranslation } from "@/src/Utils/i18n";

interface Question {
    id: number;
    docId: string;
    userId: string;
    name: string;
    email: string;
    date: number;
    question: string | string[];
    imageUrl: string;
    isAnswered: boolean;
    answeredByUsername?: string;
    answeredByUsermame?: string;
    answeredUserId?: string;
}

const Questions = ({admin}: { admin?: boolean }) => {
    const {openImage} = useImageModal();
    const router = useRouter();
    const searchParams = useSearchParams();
    const queryQuesId = searchParams && searchParams.get("id") ? searchParams.get("id") : '';
    const queryTabId = searchParams && searchParams.get("tab") ? searchParams.get("tab") : '';

    const {user, langSwitch, setLangSwitch} = useContext(UserContext);
    const [open, setOpen] = useState(false);
    const [questionToDelete, ] = useState<any>(null);
    const [openDeleteQuesModal, setOpenDeleteQuesModal] = useState(false);
    const [questions, setQuestions] = useState<Question[]>([]);
    const [questionStatus, setQuestionStatus] = useState("answered");
    const [selectedUserId, setSelectedUserId] = useState("");
    const [searchResult, setSearchResult] = useState("");
    const [dateRange, setDateRange] = useState<any>({});
    const [fetchingAnswer, setFetchingAnswer] = useState(false);
    const [fetchingQuestions, setFetchingQuestions] = useState(true);
    const [answerImage, setAnswerImage] = useState<string | null>(null);
    const [expanded, setExpanded] = useState<string | null>(null);
    const [answer, setAnswer] = useState<string | string[]>("");
    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);
    const [rowsPerPage, setRowsPerPage] = useState(5);
    const [page, setPage] = React.useState(0);
    const {t} = useTranslation();

    useEffect(() => {
        if (user.id !== "") {
            getQuestions();
        }
    }, [user.id]);

    useEffect(() => {
        if (searchParams.has("id")) {
            window.scrollTo({top: 0, behavior: "smooth"});
        }
    }, [searchParams]);

    useEffect(() => {
        setTimeout(() => {
            if (queryTabId) {
                setQuestionStatus('unanswered');
            }
        }, 200);
    }, [queryTabId]);

    console.log('questionToDelete', questionToDelete)

    // const handleDeleteQuestion = async () => {
    //     console.log("Delete function triggered");
    //     if (!questionToDelete) {
    //         console.log("No question to delete");
    //         return;
    //     }

    //     try {
    //         const response = await deleteDocument("users_questions", questionToDelete.docId);
    //         if (response.status) {
    //             getQuestions();
    //             setOpenDeleteQuesModal(false);
    //             setQuestionToDelete(null);
    //         }
    //     } catch (error) {
    //         console.error("Error deleting question:", error);
    //     }
    // };

    const getQuestions = useCallback(async () => {
        setFetchingQuestions(true);
        let resp;

        if (admin) {
            resp = await getAllDocs("users_questions");
        } else {
            resp = await queryData("users_questions", "userId", user.id);
        }

        if (resp.status && resp.fullData.length > 0) {
            let fullData = resp.fullData.sort((a, b) => b.date - a.date);
           
                setQuestions(fullData);
            setFetchingQuestions(false);
      
        }
    }, [admin, user.id]);

    useLayoutEffect(() => {
        const defaultSwitchDone = localStorage.getItem("HMWK_DEFAULT_SWITCH_DONE");
        if (navigator.language === "es" && defaultSwitchDone !== "true") {
            localStorage.setItem("HMWK_DEFAULT_SWITCH_DONE", "true");
            router.push("/es");
        }
    }, [router]);

    const id = useMemo(() => {
        if (typeof window !== "undefined") {
            const storedId = localStorage.getItem("HMWK_LANGUAGE");
            if ((storedId === "" || !storedId || storedId === "en") && langSwitch) {
                setLangSwitch(false);
            }
            return storedId;
        }
        return "";
    }, [langSwitch, setLangSwitch]);

    const handleOpenAnswer = useCallback(async (question: Question) => {
        if (!question.isAnswered) {
            setAnswer('');
            setAnswerImage(null);
            return;
        }
        setFetchingAnswer(true);
        const resp: any = await queryData("tutor_answers", "questionId", question.docId);
        setFetchingAnswer(false);

        if (resp.status && resp.fullData.length > 0) {
            setAnswer(resp.fullData[0].answer);
            setAnswerImage(resp.fullData[0].imgUrl || null);
        }
    }, []);

    const filteredQuestions = useMemo(() => {
        let questionToShow = [...questions];

        if (questionStatus === "answered") {
            questionToShow = questionToShow.filter(
                (question: Question) => question.isAnswered
            );
        } else if (questionStatus === "unanswered") {
            questionToShow = questionToShow.filter(
                (question: Question) => !question.isAnswered
            );
        }

        if (searchResult !== "") {
            const search = searchResult.toLowerCase();
            questionToShow = questionToShow.filter((question: Question) => {
                const questionTextMatch = typeof question.question === 'string' ? question.question.toLowerCase().includes(search) : question.question.some(q => q.toLowerCase().includes(search));
                const idMatch = question.id !== undefined && question.id.toString().toLowerCase().includes(search);
                return questionTextMatch || idMatch;
            });
        }

        if (dateRange.startDate) {
            const start = dateRange.startDate;
            questionToShow = questionToShow.filter((question: Question) => {
                const timestamp = Number(question.date);
                if (isNaN(timestamp)) return false;

                const questionDate = new Date(timestamp);
                return (
                    questionDate.getFullYear() === start.getFullYear() &&
                    questionDate.getMonth() === start.getMonth() &&
                    questionDate.getDate() === start.getDate()
                );
            });
        }

        return questionToShow;
    }, [questions, questionStatus, searchResult, dateRange]);

    // const handleQuestionStatusChange = useCallback((event: any, newValue: string) => {
    //     if (newValue && newValue !== questionStatus) {
    //         setQuestionStatus(newValue);
    //         setExpanded(null);
    //         setPage(0);
    //     }
    // }, [questionStatus]);

  

    const handleExpandedChange = useCallback((questionDocId: string, question: Question) => {
        return (e: React.MouseEvent) => {
            e.stopPropagation();
            handleOpenAnswer(question);
            setExpanded((prev) => (prev === questionDocId ? null : questionDocId));
        };
    }, [handleOpenAnswer]);

    // Calculate the current set of questions
    const totalQuestions = filteredQuestions.length;
    const currentQuestions = useMemo(() => {
        return queryQuesId ? questions.filter(item => item.docId === queryQuesId) : filteredQuestions;
    }, [queryQuesId, questions, filteredQuestions]);

    const paginatedQuestions = useMemo(() => {
        return currentQuestions.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
    }, [currentQuestions, page, rowsPerPage]);

    // const ToggleButtons = useMemo(() => (
    //     <ToggleButtonGroup
    //         className={admin ? `${questionStyles.toggleButtonGroup} toggleButtonGroup` : questionStyles.toggleButtonGroup}
    //         color="primary"
    //         value={questionStatus}
    //         exclusive
    //         onChange={handleQuestionStatusChange}
    //         aria-label="Platform"
    //     >
    //         <ToggleButton
    //             value="answered"
    //             classes={{
    //                 root: questionStyles.toggleButton,
    //                 selected: questionStyles.toggleButtonSelected,
    //             }}
    //         >
    //             Answered
    //         </ToggleButton>
    //         <ToggleButton
    //             value="unanswered"
    //             classes={{
    //                 root: questionStyles.toggleButton,
    //                 selected: questionStyles.toggleButtonSelected,
    //             }}
    //         >
    //             Not Answered
    //         </ToggleButton>
    //     </ToggleButtonGroup>
    // ), [questionStatus, handleQuestionStatusChange, admin]);

    if (selectedUserId) {
        return (
            <UserProfile
                selectedUserId={selectedUserId}
                backToTitle="Back to Questions"
                handleGotoBack={() => {
                    setSelectedUserId("");
                }}
            />
        );
    }

    return (
        <div className={styles.page}>
            <div className={id === "" || !id || id === "en" ? `${styles.englishPage} ${styles.historyPage}` : ""}>
                <div className={questionStyles.addQuestionTitle}>
                    <h1>{admin ? t("admin_question.student_questions_title") : t("admin_question.history_title")}</h1>
                    {!admin ?
                        <div>{t('admin_question.help_description')}</div> : ''}
                </div>
                {langSwitch || fetchingQuestions ? (<Spinner/>) : questions.length > 0 ? (
                    <div className={questionStyles.addQuestionBox} style={{height: "auto"}}>
                        {!queryQuesId ?
                            <div
                                className={`${questionStyles.tabs} ${admin ? questionStyles.adminStudentFilters : questionStyles.studentFilters}`}>
                                {admin ? (
                                        <div className={`${questionStyles.filters} ${questionStyles.adminQuesFilters}`}
                                             style={{minWidth: 'inherit'}}>
                                            <SearchBox
                                                className={questionStyles.studentSearch}
                                                placeholder={admin ? t("admin_question.search_placeholder_admin")  : t("admin_question.search_placeholder_user")}
                                                onInputChange={(e) => setSearchResult(e)}
                                                style={{maxWidth: '285px'}}
                                            />
                                            <SelectDates
                                                singleDateMode={true}
                                                className={questionStyles.studentRangeFilter}
                                                onApplyDateRange={(e) => setDateRange(e)}
                                            />
                                            <ToggleButtonGroup
                                                className={`${questionStyles.toggleButtonGroup} toggleButtonGroup`}
                                                color="primary"
                                                value={questionStatus}
                                                exclusive
                                                onChange={(e, value) => {
                                                    setQuestionStatus(value);
                                                }}
                                                aria-label="Platform"
                                            >
                                                <ToggleButton
                                                    value="answered"
                                                    classes={{
                                                        root: questionStyles.toggleButton,
                                                        selected: questionStyles.toggleButtonSelected,
                                                    }}
                                                >
                                                   {t('admin_question.answered_button')}
                                                </ToggleButton>
                                                <ToggleButton
                                                    value="unanswered"
                                                    classes={{
                                                        root: questionStyles.toggleButton,
                                                        selected: questionStyles.toggleButtonSelected,
                                                    }}
                                                >
                                                   {t("admin_question.not_answered_button")}
                                                </ToggleButton>
                                            </ToggleButtonGroup>
                                        </div>
                                    ) :
                                    <ToggleButtonGroup
                                        className={questionStyles.toggleButtonGroup}
                                        color="primary"
                                        value={questionStatus}
                                        exclusive
                                        onChange={(e, value) => {
                                            setQuestionStatus(value);
                                        }}
                                        aria-label="Platform"
                                    >
                                        <ToggleButton
                                            value="answered"
                                            classes={{
                                                root: questionStyles.toggleButton,
                                                selected: questionStyles.toggleButtonSelected,
                                            }}
                                        >
                                           {t('admin_question.answered_button')}
                                        </ToggleButton>
                                        <ToggleButton
                                            value="unanswered"
                                            classes={{
                                                root: questionStyles.toggleButton,
                                                selected: questionStyles.toggleButtonSelected,
                                            }}
                                        >
                                            {t("admin_question.not_answered_button")}
                                        </ToggleButton>
                                    </ToggleButtonGroup>
                                }
                            </div> : ''
                        }

                        <div className={questionStyles.studentRoundBox}>
                            <div className={questionStyles.questions}>
                                {paginatedQuestions && paginatedQuestions.length ? paginatedQuestions.map((question: Question) => {
                                    let allowQues = false;

                                    if ((questionStatus === "answered" && question.isAnswered) || queryQuesId) {
                                        allowQues = true;
                                    } else if (
                                        questionStatus === "unanswered" &&
                                        !question.isAnswered
                                    ) {
                                        allowQues = true;
                                    }

                                    if (!allowQues) {
                                        return null;
                                    }

                                    let askedBy = question.name && (question.name !== "") ? question.name : question.email ? question.email : '-';
                                    let quesDate = question.date ? moment(Number(question.date)).format("MM/DD/YYYY") : '';
                                    let questionString = question.question && isArray(question.question) ? question.question : question.question;

                                    return (
                                        <Accordion
                                            key={question.docId}
                                            expanded={expanded === question.docId}
                                            sx={{
                                                maxWidth: "860px",
                                                width: "100%",
                                                border: "1px solid rgba(229, 229, 229, 1) !important",
                                                borderRadius: "16px !important",
                                                boxShadow: "none !important",
                                                "&::before": {
                                                    display: "none",
                                                },
                                            }}
                                        >
                                            <AccordionSummary
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                }}
                                                aria-controls="panel1-content"
                                                id="panel1-header"
                                                sx={{
                                                    padding: {xs: "10px", md: "20px"},
                                                    "& .MuiAccordionSummary-content": {
                                                        margin: "0 !important",
                                                    },
                                                }}
                                            >
                                                <div className={questionStyles.accordionContainer}>
                                                    <div className={questionStyles.imgQuestionContainer}>
                                                        <div className={questionStyles.accQuesDataBox}>
                                                            <img
                                                                alt={'No Image'}
                                                                className={questionStyles.accQuesImage}
                                                                src={question.imageUrl || "/icons/ques-no-image.svg"}
                                                                height={104}
                                                                width={104}
                                                                onClick={() => openImage(question.imageUrl || "/icons/ques-no-image.svg")}
                                                            />

                                                            <div className={questionStyles.accQuesHeader}>
                                                                <div className={questionStyles.accQuesTop}>
                                                                    <div className={questionStyles.accQuesTitle}>
                                                                        <img src={'/icons/comment-icon.svg'}
                                                                             alt={'comment icon'}/>
                                                                        <div className={questionStyles.accQuesText}>
                                                                            <TextToggle
                                                                                text={questionString}
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <div className={questionStyles.questionInfo}>
                                                                    {admin && (
                                                                        <div className={questionStyles.accQuesMetaRow}>
                                                                            <div
                                                                                className={questionStyles.accQuesId}>Question
                                                                                ID <span>#{question.id && zeroPadId(question.id)}</span>
                                                                            </div>

                                                                            <span
                                                                                className={questionStyles.questionMeta}>
                                                                                <div>{t('admin_question.asked_by_label')}</div>
                                                                                <div
                                                                                    className={questionStyles.linkName}>{askedBy}</div>
                                                                                <div></div>
                                                                                <div>{t('admin_question.date_asked_label')}</div>
                                                                                <div
                                                                                    className={questionStyles.linkName}>{quesDate}</div>
                                                                            </span>
                                                                        </div>
                                                                    )}
                                                                    {!admin && (
                                                                        <div className={questionStyles.accQuesMetaRow}>
                                                                            <div
                                                                                className={questionStyles.accQuesId}>Question
                                                                                ID <span>#{question.id && zeroPadId(question.id)}</span>
                                                                            </div>
                                                                            <span
                                                                                className={questionStyles.questionMeta}>
                                                                                <div>{user && user.type === 'user' ? 'Asked' : 'Answered'}</div>
                                                                                <div>{quesDate}</div>
                                                                                <div
                                                                                    className={questionStyles.questionFeedback}
                                                                                    onClick={handleOpen}>Feedback</div>
                                                                            </span>
                                                                        </div>
                                                                    )}
                                                                </div>

                                                                {questionStatus === "answered" || questionStatus === "unanswered" ?
                                                                    <div className={questionStyles.accQuesAnswerBox}>
                                                                        <div
                                                                            className={questionStyles.accQuesAnsRow}
                                                                            onClick={handleExpandedChange(question.docId, question)}
                                                                        >
                                                                            <img src={'/icons/right-check-icon.svg'}
                                                                                 alt={'right-check-icon'}/>
                                                                            <span>{t("admin_question.answer_label")}</span>
                                                                            <img
                                                                                className={`${expanded === question.docId ? questionStyles.accExpanded : ''}`}
                                                                                src={'/icons/answer-toggle.svg'}
                                                                                alt={'arrow-down'}/>
                                                                        </div>
                                                                        {expanded === question.docId ?
                                                                            <div>
                                                                                {fetchingAnswer ? (
                                                                                    <h4
                                                                                        style={{
                                                                                            fontWeight: 400,
                                                                                            margin: 0,
                                                                                            color: "#999",
                                                                                            fontSize: "15px",
                                                                                            marginTop: '10px',
                                                                                            paddingLeft: '26px',
                                                                                            paddingBottom: '5px'
                                                                                        }}
                                                                                    >
                                                                                        {t("admin_question.loading_text")}
                                                                                    </h4>
                                                                                ) : (
                                                                                    <>
                                                                                        <div
                                                                                            className={questionStyles.accAnswerContent}>
                                                                                            <div
                                                                                                className={questionStyles.accAnswerData}>
                                                                                                {answerImage && (
                                                                                                    <div
                                                                                                        className={questionStyles.accAnswerImage}>
                                                                                                        <img
                                                                                                            alt={'Answer Image'}
                                                                                                            src={answerImage}
                                                                                                            height={76}
                                                                                                            width={76}
                                                                                                            onClick={() => openImage(answerImage)}
                                                                                                        />
                                                                                                    </div>
                                                                                                )}
                                                                                                <div
                                                                                                    className={questionStyles.accAnswerText}>
                                                                                                    {answer && isArray(answer) ? (answer as string[]).map((p, index) => (
                                                                                                        <p key={index}>{p}</p>)) : answer ? answer : 'Not Answered'}
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                        {admin ? <div
                                                                                            className={questionStyles.accQuesDelete}>
                                                                                            <CancelButton
                                                                                                onClick={() => setOpenDeleteQuesModal(true)}
                                                                                                label={t("admin_question.delete_question_button")}/>
                                                                                        </div> : ''}
                                                                                    </>
                                                                                )}
                                                                            </div> : null
                                                                        }
                                                                    </div> : null
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </AccordionSummary>
                                        </Accordion>
                                    );
                                }) : queryQuesId ?
                                    <div className={questionStyles.addQuestionBox}><h3>{t("admin_question.no_question_found")}</h3>
                                    </div> : ''}
                            </div>
                            {!queryQuesId ?
                                <TableCustomPagination
                                    filteredRowsLength={totalQuestions}
                                    page={page}
                                    rowsPerPage={rowsPerPage}
                                    setPage={setPage}
                                    setRowsPerPage={setRowsPerPage}
                                /> : null
                            }
                        </div>
                    </div>
                ) : (
                    <div className={questionStyles.addQuestionBox}>
                        <h3>{t("admin_question.no_questions_submitted")}</h3>
                    </div>
                )}
            </div>

            <NotificationModal
                open={open}
                handleClose={handleClose}
                icon={"/icons/flag-icon.svg"}
                title={t("admin_question.help_email_title")}
                content={`<a href="mailto:<EMAIL>"><EMAIL></a>`}
            />

            <NotificationModal
                isDelete={true}
                open={openDeleteQuesModal}
                handleClose={() => setOpenDeleteQuesModal(false)}
                title={t("admin_question.delete_question_button")}
                content={t("admin_question.delete_confirm_content")}
                cancelLabel={t("admin_question.cancel_button")}
                actionLabel={t("admin_question.yes_delete_button")}
            />
        </div>
    );
};


export default Questions;