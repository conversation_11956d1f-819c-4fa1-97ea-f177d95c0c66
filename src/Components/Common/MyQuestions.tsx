import * as React from "react";
import {useTranslation} from "@/src/Utils/i18n";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TablePagination from "@mui/material/TablePagination";
import {getComparator, stableSort} from "@/src/Utils/helpers";
import EnhancedTableHead from "@/src/Components/Table/EnhancedTableHead";
import {UserContext} from "@/src/Contexts/UserContext";
import {queryData} from "@/src/Firebase/firebase.utils";
import {
    QuestionGroupedItem,
    QuestionOriginalItem,
    QuestionRowArrType,
    QuestionTableColumn
} from "@/src/Types";

// Move the Column interface above its first usage
interface Column {
    id: "date" | "questions";
    label: string;
    minWidth?: number;
    align?: "right";
    sorting?: boolean;
    format?: (value: number) => string;
}

function createData(date: string, questions: string) {
    return {date, questions};
}

export default function MyTests() {
    const {t} = useTranslation();
    const [page, setPage] = React.useState(0);
    const [rows, setRows] = React.useState<QuestionRowArrType[]>([]);
    const [first, setFirst] = React.useState(true);
    const [rowsPerPage, setRowsPerPage] = React.useState(10);
    const [order, setOrder] = React.useState("desc");
    const [orderBy, setOrderBy] = React.useState("date");
    const {user} = React.useContext(UserContext);

    const groupByDate = (arr: QuestionOriginalItem[]): QuestionGroupedItem[] => {
        const dateMap = arr.reduce<Record<string, QuestionGroupedItem>>((acc, item) => {
            const date = new Date(Number(item.date)).toDateString();

            if (!acc[date]) {
                acc[date] = {
                    date,
                    questions: 1,
                };
            } else {
                acc[date].questions++;
            }
            return acc;
        }, {});

        return Object.values(dateMap).sort(
            (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
        );
    };

    const getQuestions = React.useCallback(async () => {
        const resp: any = await queryData(
            "users_questions",
            "userId",
            user.id as string
        );

        if (resp.status && resp.fullData.length > 0) {
            const arr: any = [];
            const groupedArr = groupByDate(resp.fullData);

            groupedArr.forEach((item) => {
                const createdData = createData(item.date, item.questions.toString());

                arr.push(createdData);
            });
            setRows(arr);
        } else {
            setRows([]);
        }
    }, [user.id]);

    React.useEffect(() => {
        if (first && user.id !== "") {
            setFirst(false);
            getQuestions();
        }
    }, [first, user, getQuestions]);

    const handleRequestSort = (event, property) => {
        const isAsc = orderBy === property && order === "asc";
        setOrder(isAsc ? "desc" : "asc");
        setOrderBy(property);
    };

    const handleChangePage = (event: unknown, newPage: number) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        setRowsPerPage(+event.target.value);
        setPage(0);
    };

    const columns: readonly Column[] = [
        {id: "date", label: t('common.date'), minWidth: 170, sorting: true},
        {id: "questions", label: t('student.my_questions'), minWidth: 170, sorting: true},
    ];

    return (
        <div className={"tableWrapper"}>
            <Paper
                sx={{
                    width: "100%",
                    overflow: "auto",
                    borderRadius: "0",
                    boxShadow: "none",
                    border: "1px solid rgba(224, 224, 224, 1)",
                }}
            >
                <TableContainer className={"tableContainer"}>
                    <Table>
                        <EnhancedTableHead
                            columns={columns as QuestionTableColumn[]}
                            order={order}
                            orderBy={orderBy}
                            onRequestSort={handleRequestSort}
                        />
                        <TableBody>
                            {stableSort(rows, getComparator(order, orderBy))
                                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                .map((row) => {
                                    return (
                                        <TableRow
                                            hover
                                            role="checkbox"
                                            tabIndex={-1}
                                            key={row.code}
                                        >
                                            {columns.map((column) => {
                                                const value = row[column.id];
                                                return (
                                                    <TableCell key={column.id} align={column.align}>
                                                        {value}
                                                    </TableCell>
                                                );
                                            })}
                                        </TableRow>
                                    );
                                })}
                        </TableBody>
                    </Table>
                </TableContainer>
                <TablePagination
                    rowsPerPageOptions={[10, 20, 30]}
                    component="div"
                    count={rows.length}
                    rowsPerPage={rowsPerPage}
                    page={page}
                    onPageChange={handleChangePage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    labelRowsPerPage={t('common.showing')}
                />
            </Paper>
        </div>
    );
}