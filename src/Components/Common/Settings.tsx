"use client";
import React, {useContext, useEffect, useMemo, useState} from "react";
import {useTranslation} from "@/src/Utils/i18n";
import {UserContext} from "../../Contexts/UserContext";
import {AppNotify} from "../../Utils/AppNotify";
import {
    checkOldPassword,
    signInAuthUserWithEmailAndPassword,
    updateUserPassword,
    uploadImage,
} from "../../Firebase/firebase.utils";
import {validateEmail} from "../../Utils/helpers";
import Mui<PERSON><PERSON>Number from "mui-phone-number";
import axios from "axios";
import moment from "moment";
import countryList from "react-select-country-list";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import InputTextField from "@/src/Components/Form/InputTextField";
import PasswordField from "@/src/Components/Form/PasswordField";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import styles from "@/src/styles/settingsPage.module.css";
import loginStyles from "@/src/styles/login.module.css";
import userStyles from "@/src/styles/user.module.css";
import {defaultInputFieldStyle, highlightFieldStyle} from "@/src/Utils/styles";
import CustomSelectField from "@/src/Components/Form/CustomSelectField";
import {Notification} from "@/src/Components/Widgets/Notification";
import Spinner from "@/src/Components/Widgets/Spinner";
import {SettingsProps} from "@/src/Types";

const Settings: React.FC<SettingsProps> = ({
                                               isTutorReg,
                                               onProgressUpdate,
                                           }) => {
    const {t} = useTranslation();
    const {user, setUser, fetching} = useContext(UserContext);
    const [email, setEmail] = useState("");
    const [firstName, setFirstName] = useState("");
    const [lastName, setLastName] = useState("");
    const [certificateNumber, setCertificateNumber] = useState("");
    const [billingAddress, setBillingAddress] = useState("");
    const [phoneNumber, setPhoneNumber] = useState("");
    const [country, setCountry] = useState("");
    const [address1, setAddress1] = useState("");
    const [address2, setAddress2] = useState("");
    const [city, setCity] = useState("");
    const [state, setState] = useState("");
    const [postalCode, setPostalCode] = useState("");
    const [updating, setUpdating] = useState(false);
    const [newPassword, setNewPassword] = useState("");
    const [oldPassword, setOldPassword] = useState("");
    const [dateOfBirth, setDateOfBirth] = useState<any>(null);
    const [profileImage, setProfileImage] = useState<any>(null);
    const [isCertificateValid, setIsCertificateValid] = useState(true);
    const [isValidating, setValidate] = useState(false);
    const [editPressed, setEditPressed] = useState(true);

    const [birthMonth, setBirthMonth] = useState("");
    const [birthDay, setBirthDay] = useState("");
    const [birthYear, setBirthYear] = useState("");
    const [educationLevel, setEducationLevel] = useState("");
    const [tutorExperience, setTutorExperience] = useState("");
    const [workTitle, setWorkTitle] = useState("");
    const [formChanged, setFormChanged] = useState(false);
    const [onceLoaded, setOnceLoaded] = useState(false);

    const [initialFormState, setInitialFormState] = useState<any>({});

    const options: any = useMemo(() => countryList().getData(), []);

    const educationOptions = [
        {value: "current_student", label: t('setting.current_student')},
        {value: "high_school", label: t('setting.high_school')},
        {value: "bachelors", label: t('setting.bachelors_degree')},
        {value: "masters", label: t('setting.masters_degree')},
        {value: "phd", label: t('setting.phd')}
    ];

    const experienceOptions = [
        {value: "0", label: t('setting.0_years')},
        {value: "1", label: t('setting.1_year')},
        {value: "2", label: t('setting.2_years')},
        {value: "3", label: t('setting.3_years')},
        {value: "4", label: t('setting.4_years')},
        {value: "5+", label: t('setting.5_plus_years')}
    ];

    const monthOptions = [
        {value: "1", label: t('setting.january')},
        {value: "2", label: t('setting.february')},
        {value: "3", label: t('setting.march')},
        {value: "4", label: t('setting.april')},
        {value: "5", label: t('setting.may')},
        {value: "6", label: t('setting.june')},
        {value: "7", label: t('setting.july')},
        {value: "8", label: t('setting.august')},
        {value: "9", label: t('setting.september')},
        {value: "10", label: t('setting.october')},
        {value: "11", label: t('setting.november')},
        {value: "12", label: t('setting.december')}
    ];

    const dayOptions = Array.from({length: 31}, (_, i) => ({
        value: (i + 1).toString(),
        label: (i + 1).toString()
    }));

    const currentYear = new Date().getFullYear();
    const yearOptions = Array.from({length: 100}, (_, i) => ({
        value: (currentYear - i).toString(),
        label: (currentYear - i).toString()
    }));

    // Tutor registration progress
    const calculateProgress = () => {
        if (!isTutorReg || !onProgressUpdate) return;

        const requiredFields = [
            {key: 'firstName', value: firstName},
            {key: 'lastName', value: lastName},
            {key: 'phoneNumber', value: phoneNumber, validate: (v: string) => v.trim() && v !== "+"},
            {key: 'dateOfBirth', validate: () => birthMonth && birthDay && birthYear},
            {key: 'educationLevel', value: educationLevel},
            {key: 'tutorExperience', value: tutorExperience},
            {key: 'country', value: country},
            {
                key: 'certificateNumber',
                value: certificateNumber,
                validate: () => certificateNumber.trim() && isCertificateValid
            }
        ];

        const completedFields = requiredFields.reduce((count, field: any) => {
            const isValid = field.validate ? field.validate(field.value) : field.value && field.value.trim();
            return isValid ? count + 1 : count;
        }, 0);

        const totalFields = requiredFields.length;
        const progressPercentage = (completedFields / totalFields) * 100;

        const getMessage = (progress: number): string => {
            if (progress === 0) return t('setting.lets_get_started');
            if (progress < 25) return t('setting.keep_going');
            if (progress < 50) return t('setting.making_progress');
            if (progress < 75) return t('setting.almost_done');
            if (progress < 100) return t('setting.almost_done');
            return t('setting.all_set');
        };

        onProgressUpdate({
            value: Math.round(progressPercentage),
            message: getMessage(progressPercentage),
        });
    };

    const checkFormChanges = () => {
        if (!onceLoaded || !initialFormState) return;

        const currentFormState = {
            firstName,
            lastName,
            email,
            phoneNumber,
            birthMonth,
            birthDay,
            birthYear,
            educationLevel,
            tutorExperience,
            workTitle,
            country,
            certificateNumber,
            billingAddress,
            address1,
            address2,
            city,
            state,
            postalCode,
            newPassword,
            oldPassword
        };

        const changedKeys = Object.keys(initialFormState).filter(
            key => initialFormState[key as keyof typeof initialFormState] !== currentFormState[key as keyof typeof currentFormState]
        );

        const passwordValid = !newPassword || (Boolean(oldPassword) && newPassword.length >= 8);
        const onlyCertificateChanged = changedKeys.includes("certificateNumber");

        if (changedKeys.length === 0) {
            setFormChanged(false);
        } else if (onlyCertificateChanged && isCertificateValid && passwordValid) {
            setFormChanged(true);
        } else if (!onlyCertificateChanged && passwordValid || (changedKeys.length >= 2 && passwordValid)) {
            setFormChanged(true);
        } else {
            setFormChanged(false);
        }
    };

    // Progress bar update on form changes
    useEffect(() => {
        calculateProgress();
    }, [
        firstName, lastName, phoneNumber,
        birthMonth, birthDay, birthYear,
        educationLevel, tutorExperience,
        country, certificateNumber, isCertificateValid,
        isTutorReg
    ]);

    useEffect(() => {
        checkFormChanges();
    }, [
        firstName, lastName, email, phoneNumber,
        birthMonth, birthDay, birthYear,
        educationLevel, tutorExperience, workTitle,
        country, certificateNumber, billingAddress,
        address1, address2, city, state, postalCode,
        newPassword, oldPassword, isCertificateValid,
        onceLoaded, initialFormState
    ]);

    useEffect(() => {
        if (editPressed && (email !== initialFormState.email || firstName !== initialFormState.firstName || lastName !== initialFormState.lastName)) {
            setOldPassword("");
            setNewPassword("");
        }
    }, [email, firstName, lastName, initialFormState]);

    useEffect(() => {
        if (!fetching && user) {
            const initialState = {
                email: user.email || "",
                firstName: user.firstName || "",
                lastName: user.lastName || "",
                phoneNumber: user.phoneNumber || "",
                certificateNumber: user.certificateNumber || "",
                billingAddress: user.billingAddress || "",
                workTitle: user.workTitle || "",
                educationLevel: user.educationLevel || "",
                tutorExperience: user.tutorExperience || "",
                country: user.country || "",
                address1: user.address1 || "",
                address2: user.address2 || "",
                city: user.city || "",
                state: user.state || "",
                postalCode: user.postalCode || "",
                birthMonth: "",
                birthDay: "",
                birthYear: "",
                newPassword: "",
                oldPassword: ""
            };

            if (user.dateOfBirth) {
                const dob = moment(user.dateOfBirth);
                initialState.birthMonth = dob.format('M');
                initialState.birthDay = dob.format('D');
                initialState.birthYear = dob.format('YYYY');
            }

            // Set form values
            setEmail(initialState.email);
            setFirstName(initialState.firstName);
            setLastName(initialState.lastName);
            setPhoneNumber(initialState.phoneNumber);
            setCertificateNumber(initialState.certificateNumber);
            setProfileImage(user.imgUrl || "");
            setBillingAddress(initialState.billingAddress);
            setDateOfBirth(user.dateOfBirth ? moment(user.dateOfBirth) : null);
            setWorkTitle(initialState.workTitle);
            setEducationLevel(initialState.educationLevel);
            setTutorExperience(initialState.tutorExperience);
            setCountry(initialState.country);
            setAddress1(initialState.address1);
            setAddress2(initialState.address2);
            setCity(initialState.city);
            setState(initialState.state);
            setPostalCode(initialState.postalCode);
            setBirthMonth(initialState.birthMonth);
            setBirthDay(initialState.birthDay);
            setBirthYear(initialState.birthYear);

            // Store initial state for comparison
            setInitialFormState(initialState);
        }
    }, [fetching, user]);

    useEffect(() => {
        if (birthMonth && birthDay && birthYear) {
            const dateString = `${birthYear}-${birthMonth}-${birthDay}`;
            setDateOfBirth(moment(dateString, 'YYYY-M-D'));
        } else {
            setDateOfBirth(null);
        }
    }, [birthMonth, birthDay, birthYear]);

    const validateCertificate = async () => {
        if (!certificateNumber) return;
        try {
            setValidate(true);
            const response = await axios.post(
                process.env.NEXT_PUBLIC_VERIFY_CERTIFICATE as string,
                {
                    certificateNumber,
                    firstName,
                    lastName,
                }
            );
            setIsCertificateValid(response.data.status);
            if (!response.data.status) {
                AppNotify(t('setting_notify.invalid_certificate'), "error");
            }
            setValidate(false);
        } catch (error) {
            console.error("Error validating certificate:", error);
            setIsCertificateValid(false);
            AppNotify(t('setting_notify.failed_validate_certificate'), "error");
            setValidate(false);
        }
    };

    const handleSubmit = async () => {
        // Universal email validation
        if (!validateEmail(email)) {
            AppNotify(t('setting_notify.invalid_email'), "error");
            return;
        }

        // Password validation
        if (newPassword !== "" && newPassword.length < 8) {
            AppNotify(t('setting_notify.new_password_must_be_at_least_8_characters'), "error");
            return;
        }

        if (newPassword !== "" && oldPassword === "") {
            AppNotify(t('setting_notify.current_password_is_required'), "error");
            return;
        }

        // NEW: Check if user entered current password but forgot new password
        if (oldPassword !== "" && newPassword === "") {
            AppNotify(t('setting_notify.please_enter_a_new_password_or_clear_the_current_password_field'), "error");
            return;
        }

        if (oldPassword !== "" && newPassword !== "") {
            const resp = await checkOldPassword(user.email as string, oldPassword);
            if (!resp?.status) {
                AppNotify(t('setting_notify.the_current_password_you_entered_is_incorrect'), "error");
                return;
            }
        }

        // certificate validation hit endpoint on tutorcert
        if ((user?.type === "tutor" || isTutorReg) && certificateNumber && certificateNumber !== user?.certificateNumber) {
            try {
                const response = await axios.post(
                    process.env.NEXT_PUBLIC_VERIFY_CERTIFICATE as string,
                    {
                        certificateNumber,
                        firstName,
                        lastName,
                    }
                );
                setIsCertificateValid(response.data.status);

                if (!response.data.status) {
                    AppNotify(t('setting_notify.invalid_certificate'), "error");
                    return;
                }
            } catch (error) {
                console.error("Error validating certificate:", error);
                setIsCertificateValid(false);
                AppNotify(t('setting_notify.failed_validate_certificate'), "error");
                return;
            }
        }

        setUpdating(true);

        let dob = "";
        if (dateOfBirth) {
            dob = dateOfBirth.toDate().getTime();
        }

        let profileImageUrl = user.imgUrl;

        if (profileImage && profileImage !== "") {
            const resp = await uploadImage(profileImage, user?.id as string, true);
            if (resp.status) {
                profileImageUrl = resp.downloadUrl;
            }
        }

        const finalCertificateNumber = certificateNumber && isCertificateValid ? certificateNumber : "";

        try {
            const resp = await axios.post(
                process.env.NEXT_PUBLIC_UPDATE_USER as string,
                {
                    uid: user?.id,
                    firstName,
                    lastName,
                    phoneNumber: phoneNumber === "+" ? "" : phoneNumber,
                    dateOfBirth: dob,
                    billingAddress,
                    certificateNumber: finalCertificateNumber,
                    email,
                    country,
                    address1,
                    address2,
                    city,
                    state,
                    postalCode,
                    profileImageUrl,
                    educationLevel,
                    tutorExperience,
                    workTitle
                }
            );

            if (resp.data && resp.data.status) {
                if (newPassword !== "") {
                    await updateUserPassword(oldPassword, newPassword);
                    await signInAuthUserWithEmailAndPassword(email, newPassword, true);
                } else {
                    setUser({
                        achievements: {},
                        birthDay: birthDay || "",
                        birthMonth: birthMonth || "",
                        birthYear: birthYear || "",
                        statistics: {},
                        firstName,
                        lastName,
                        email,
                        dateOfBirth: dob,
                        id: user?.id as string,
                        revenue: user.revenue,
                        pendingWithdrawals: user.pendingWithdrawals,
                        applicantQuestionAnswered: user.applicantQuestionAnswered,
                        phoneNumber,
                        certificateNumber: finalCertificateNumber,
                        billingAddress,
                        earnings: user.earnings,
                        type: user.type || "user",
                        isApplicant: user.isApplicant,
                        isAvailable: user.isAvailable,
                        questions: user.questions || 0,
                        pendingWithdrawalAmount: user.pendingWithdrawalAmount,
                        provider: user.provider as string,
                        examsCompleted: user.examsCompleted,
                        examsAttempted: user.examsAttempted,
                        imgId: user.imgId,
                        imgUrl: profileImageUrl,
                        country,
                        address1: user.address1 || "",
                        address2: user.address2 || "",
                        city: user.city || "",
                        state: user.state || "",
                        postalCode: user.postalCode || "",
                        educationLevel,
                        tutorExperience,
                        workTitle
                    });
                }

                if (!isCertificateValid) {
                    setCertificateNumber("");
                }

                setIsCertificateValid(true);
                setFormChanged(false);
                setNewPassword("");
                setOldPassword("");

                setInitialFormState({
                    ...initialFormState,
                    firstName,
                    lastName,
                    email,
                    phoneNumber,
                    certificateNumber: finalCertificateNumber,
                    billingAddress,
                    workTitle,
                    educationLevel,
                    tutorExperience,
                    country,
                    birthMonth,
                    birthDay,
                    birthYear,
                    newPassword: "",
                    oldPassword: ""
                });
            } else {
                AppNotify(t('setting_notify.failed_update_profile'), "error");
            }
        } catch (error) {
            console.error("Error updating profile:", error);
            AppNotify(t('setting_notify.failed_update_profile'), "error");
        }

        setUpdating(false);
    };

    useEffect(() => {
        setEditPressed(true);
        setTimeout(() => {
            setOnceLoaded(true);
        }, 1000);

    }, []);

    const responsiveStyle = {
        '@media (max-width:659px)': {
            '& .MuiOutlinedInput-root': {
                padding: '12px',
                fontSize: '14px',
                height: '45px',
            },
        },
    };

    const [touchedFields, setTouchedFields] = useState({
        firstName: false,
        lastName: false,
        phoneNumber: false,
        birthMonth: false,
        birthDay: false,
        birthYear: false,
        educationLevel: false,
        tutorExperience: false,
        workTitle: false,
        country: false,
        certificateNumber: false,
    });

    useEffect(() => {
        if (!fetching && user) {
            setTouchedFields({
                firstName: Boolean(user.firstName),
                lastName: Boolean(user.lastName),
                phoneNumber: Boolean(user.phoneNumber),
                birthMonth: Boolean(user.birthMonth),
                birthDay: Boolean(user.birthDay),
                birthYear: Boolean(user.birthYear),
                educationLevel: Boolean(user.educationLevel),
                tutorExperience: Boolean(user.tutorExperience),
                workTitle: Boolean(user.workTitle),
                country: Boolean(user.country),
                certificateNumber: Boolean(user.certificateNumber),
            });
        }
    }, [fetching, user]);

    // Get button disabled state - unified logic
    const getButtonDisabledState = () => {
        if (!editPressed) return true;
        if (!formChanged) return true;

        // For students/users - require firstName and lastName
        if (user?.type === "user") {
            return !(firstName?.trim() && lastName?.trim());
        }

        return false;
    };

    // render for form for tutor reg
    if (isTutorReg) {
        return (
            <div className={userStyles.registerFormBox}>
                <div className={questionStyles.borderRoundBox} style={{margin: "30px auto"}}>
                    <div>
                        {updating ? (
                            <Spinner/>
                        ) : (
                            <div className={userStyles.personalBox} style={{padding: "0px"}}>
                                <div className={userStyles.personalForm}>
                                    <div className={userStyles.twoColsFields}>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.first_name')}</div>
                                            <InputTextField
                                                icon={'user-icon'}
                                                placeholder={t('setting.first_name_placeholder')}
                                                value={firstName}
                                                disabled={!editPressed}
                                                highlightField={!firstName || firstName.trim() === ''}
                                                onChange={(e) => {
                                                    if (!touchedFields.firstName) {
                                                        setTouchedFields(prev => ({...prev, firstName: true}));
                                                    }
                                                    setFirstName(e.target.value);
                                                }}
                                            />
                                        </div>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.last_name')}</div>
                                            <InputTextField
                                                icon={'user-icon'}
                                                placeholder={t('setting.last_name_placeholder')}
                                                value={lastName}
                                                disabled={!editPressed}
                                                highlightField={!lastName || lastName.trim() === ''}
                                                onChange={(e) => {
                                                    if (!touchedFields.lastName) {
                                                        setTouchedFields(prev => ({...prev, lastName: true}));
                                                    }
                                                    setLastName(e.target.value);
                                                }}
                                            />
                                        </div>
                                    </div>

                                    <div className={userStyles.twoColsFields}>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.phone_number')}</div>
                                            <MuiPhoneNumber
                                                className="flag-dropdown"
                                                defaultCountry={"us"}
                                                value={phoneNumber}
                                                onChange={(val) => {
                                                    if (!touchedFields.phoneNumber) {
                                                        setTouchedFields(prev => ({...prev, phoneNumber: true}));
                                                    }
                                                    setPhoneNumber(val);
                                                }}
                                                sx={{
                                                    ...defaultInputFieldStyle,
                                                    ...((!phoneNumber || phoneNumber === '+') ? highlightFieldStyle : {}),
                                                    ...responsiveStyle,
                                                }}
                                                disabled={!editPressed}
                                                variant="outlined"
                                            />
                                        </div>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.date_of_birth')}</div>
                                            <div style={{display: 'flex', gap: '8px'}}>
                                                <div style={{flex: 1}}>
                                                    <CustomSelectField
                                                        disabled={!editPressed}
                                                        options={monthOptions}
                                                        onSelect={(val) => {
                                                            if (!touchedFields.birthMonth) {
                                                                setTouchedFields(prev => ({...prev, birthMonth: true}));
                                                            }
                                                            setBirthMonth(val);
                                                        }}
                                                        defaultValue={birthMonth}
                                                        placeholder={t('setting.month_placeholder')}
                                                        highlightField={!birthMonth}
                                                    />
                                                </div>
                                                <div style={{flex: 1}}>
                                                    <CustomSelectField
                                                        disabled={!editPressed}
                                                        options={dayOptions}
                                                        onSelect={(val) => {
                                                            if (!touchedFields.birthDay) {
                                                                setTouchedFields(prev => ({...prev, birthDay: true}));
                                                            }
                                                            setBirthDay(val);
                                                        }}
                                                        defaultValue={birthDay}
                                                        placeholder={t('setting.day_placeholder')}
                                                        highlightField={!birthDay}
                                                    />
                                                </div>
                                                <div style={{flex: 1}}>
                                                    <CustomSelectField
                                                        disabled={!editPressed}
                                                        options={yearOptions}
                                                        onSelect={(val) => {
                                                            if (!touchedFields.birthYear) {
                                                                setTouchedFields(prev => ({...prev, birthYear: true}));
                                                            }
                                                            setBirthYear(val);
                                                        }}
                                                        defaultValue={birthYear}
                                                        placeholder={t('setting.year_placeholder')}
                                                        highlightField={!birthYear}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className={userStyles.twoColsFields}>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.education_level')}</div>
                                            <CustomSelectField
                                                icon={'/icons/education-icon.svg'}
                                                disabled={!editPressed}
                                                options={educationOptions}
                                                onSelect={(val) => {
                                                    if (!touchedFields.educationLevel) {
                                                        setTouchedFields(prev => ({...prev, educationLevel: true}));
                                                    }
                                                    setEducationLevel(val);
                                                }}
                                                defaultValue={educationLevel}
                                                placeholder={t('setting.education_level_placeholder')}
                                                highlightField={!touchedFields.educationLevel}
                                            />
                                        </div>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.tutor_experience')}</div>
                                            <CustomSelectField
                                                icon={'/icons/clock-icon.svg'}
                                                disabled={!editPressed}
                                                options={experienceOptions}
                                                onSelect={(val) => {
                                                    if (!touchedFields.tutorExperience) {
                                                        setTouchedFields(prev => ({...prev, tutorExperience: true}));
                                                    }
                                                    setTutorExperience(val);
                                                }}
                                                defaultValue={tutorExperience}
                                                placeholder={t('setting.tutor_experience_placeholder')}
                                                highlightField={!touchedFields.tutorExperience}
                                            />
                                        </div>
                                    </div>

                                    <div className={userStyles.twoColsFields}>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.current_work_title')} </div>
                                            <InputTextField
                                                icon={'work-icon'}
                                                placeholder={t('setting.work_title_placeholder')}
                                                value={workTitle}
                                                disabled={!editPressed}
                                                onChange={(e) => {
                                                    if (!touchedFields.workTitle) {
                                                        setTouchedFields(prev => ({...prev, workTitle: true}));
                                                    }
                                                    setWorkTitle(e.target.value);
                                                }}
                                            />
                                        </div>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.country_of_residence')}</div>
                                            <CustomSelectField
                                                disabled={!editPressed}
                                                options={options}
                                                onSelect={(val) => {
                                                    if (!touchedFields.country) {
                                                        setTouchedFields(prev => ({...prev, country: true}));
                                                    }
                                                    setCountry(val);
                                                }}
                                                defaultValue={country}
                                                placeholder={t('setting.country_placeholder')}
                                                highlightField={!touchedFields.country}
                                            />
                                        </div>
                                    </div>

                                    <div className={loginStyles.field}>
                                        <div className={loginStyles.label}>{t('setting.tutor_certification_number')}</div>
                                        <InputTextField
                                            highlightField={!touchedFields.certificateNumber}
                                            required={true}
                                            placeholder={""}
                                            value={certificateNumber}
                                            disabled={!editPressed}
                                            isLoading={isValidating}
                                            icon={"certificate-icon"}
                                            fullWidth
                                            error={false}
                                            helperText={""}
                                            onChange={(e) => {
                                                if (!touchedFields.certificateNumber) {
                                                    setTouchedFields(prev => ({...prev, certificateNumber: true}));
                                                }
                                                setCertificateNumber(e.target.value);
                                            }}
                                            onBlur={validateCertificate}
                                            style={!isCertificateValid ? {
                                                border: "1px solid red",
                                                borderRadius: "12px"
                                            } : undefined}
                                        />
                                        {!isCertificateValid && (
                                            <div style={{color: "red", marginTop: "8px", fontSize: "14px"}}>
                                                {t('setting_notify.invalid_certificate')}
                                            </div>
                                        )}
                                    </div>

                                    <Notification
                                        type="warning"
                                        title={t('setting.tutor_certificate')}
                                        message={
                                            <>
                                              {t('setting.tutor_certificate_notification_message')}
                                                <a
                                                    href="https://www.actp.com"
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    style={{color: "#007bff", textDecoration: "underline"}}
                                                >
                                                      www.actp.com
                                                </a>.
                                            </>
                                        }
                                    />

                                    <div className={userStyles.updateSubmitBox}>
                                        <div style={{display: "flex", justifyContent: "end", paddingTop: '20px'}}>
                                            <PrimaryButton
                                                disabled={getButtonDisabledState()}
                                                onClick={handleSubmit}
                                                label={t('setting.update')}
                                                size={'greenBtn'}
                                                style={{minWidth: '214px'}}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    if (!user.id) {
        return (
            <Spinner/>
        )
    }

    // Regular Settings Page
    return (
        <div className={styles.settingsContainer}>
            <div className={questionStyles.addQuestionTitle}>
                <h1>{user && user.type === 'admin' ? t('admin.settings') : t('common.settings')}</h1>
                {user && user.type === 'admin' ? '' :
                    <div>{t('student.ask_question_description')}</div>}
            </div>

            {user.type === "tutor" ? (
                // Tutor Settings - email and password fields
                <div className={questionStyles.borderRoundBox}>
                    <div>
                        {updating ? (
                            <Spinner/>
                        ) : (
                            <div className={userStyles.personalBox} style={{padding: "0px"}}>
                                <div className={userStyles.personalForm}>
                                    <div className={loginStyles.field}>
                                        <div className={loginStyles.label} style={{paddingTop: "20px"}}>
                                            {t('setting.email_address')}
                                        </div>
                                        <InputTextField
                                            icon={'envelope-icon'}
                                            placeholder={t('auth.enter_email')}
                                            value={email}
                                            disabled={!editPressed || user?.provider?.includes("google.com")}
                                            onChange={(e) => setEmail(e.target.value)}
                                        />
                                    </div>
                                    <div className={userStyles.twoColsFields}>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.current_password')}</div>
                                            <PasswordField
                                                showIcon={true}
                                                placeholder={t('setting.current_password_placeholder')}
                                                value={oldPassword}
                                                onChange={(e) => setOldPassword(e.target.value)}
                                                disabled={!editPressed || user?.provider?.includes("google.com")}
                                                fullWidth
                                            />
                                        </div>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.new_password')}</div>
                                            <PasswordField
                                                showIcon={true}
                                                placeholder={t('auth.enter_new_password')}
                                                value={newPassword}
                                                onChange={(e) => setNewPassword(e.target.value)}
                                                disabled={!editPressed || user?.provider?.includes("google.com")}
                                                fullWidth
                                            />
                                        </div>
                                    </div>
                                    <div className={userStyles.updateSubmitBox}>
                                        <div style={{display: "flex", justifyContent: "end", paddingBottom: '20px'}}>
                                            <PrimaryButton
                                                disabled={getButtonDisabledState()}
                                                onClick={handleSubmit}
                                                label={t('setting.update')}
                                                size={'greenBtn'}
                                                style={{width: '214px'}}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            ) : (
                // Student settings - basic profile fields
                <div className={questionStyles.borderRoundBox}>
                    <div>
                        {updating ? (
                            <Spinner/>
                        ) : (
                            <div className={userStyles.personalBox} style={{padding: "0px"}}>
                                <div className={userStyles.personalForm}>
                                    <div className={userStyles.twoColsFields}>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.first_name')}</div>
                                            <InputTextField
                                                icon={'user-icon'}
                                                placeholder={t('setting.first_name_placeholder')}
                                                value={firstName}
                                                disabled={!editPressed}
                                                onChange={(e) => setFirstName(e.target.value)}
                                            />
                                        </div>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.last_name')}</div>
                                            <InputTextField
                                                icon={'user-icon'}
                                                placeholder={t('setting.last_name_placeholder')}
                                                value={lastName}
                                                disabled={!editPressed}
                                                onChange={(e) => setLastName(e.target.value)}
                                            />
                                        </div>
                                    </div>

                                    <div className={userStyles.twoColsFields}>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.email_address')}</div>
                                            <InputTextField
                                                icon={'envelope-icon'}
                                                placeholder={t('auth.enter_email')}
                                                value={email}
                                                disabled={!editPressed || user?.provider?.includes("google.com")}
                                                onChange={(e) => setEmail(e.target.value)}
                                            />
                                        </div>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.phone_number')}</div>
                                            <MuiPhoneNumber
                                                className="flag-dropdown"
                                                defaultCountry={"us"}
                                                value={phoneNumber}
                                                onChange={(val) => setPhoneNumber(val)}
                                                sx={defaultInputFieldStyle}
                                                disabled={!editPressed}
                                                variant="outlined"
                                            />
                                        </div>
                                    </div>

                                    <div className={userStyles.twoColsFields}>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.current_password')}</div>
                                            <PasswordField
                                                showIcon={true}
                                                placeholder={t('setting.current_password_placeholder')}
                                                value={oldPassword}
                                                onChange={(e) => setOldPassword(e.target.value)}
                                                disabled={!editPressed || user?.provider?.includes("google.com")}
                                                fullWidth
                                            />
                                        </div>
                                        <div className={loginStyles.field}>
                                            <div className={loginStyles.label}>{t('setting.new_password')}</div>
                                            <PasswordField
                                                showIcon={true}
                                                placeholder={t('auth.enter_new_password')}
                                                value={newPassword}
                                                onChange={(e) => setNewPassword(e.target.value)}
                                                disabled={!editPressed || user?.provider?.includes("google.com")}
                                                fullWidth
                                                checkStrength={true}
                                            />
                                        </div>
                                    </div>

                                    <div className={userStyles.updateSubmitBox}>
                                        <div style={{display: "flex", justifyContent: "end"}}>
                                            <PrimaryButton
                                                disabled={getButtonDisabledState()}
                                                onClick={handleSubmit}
                                                label={t('setting.update')}
                                                size={'greenBtn'}
                                                style={{minWidth: '214px'}}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default Settings;