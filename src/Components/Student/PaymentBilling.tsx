"use client";
import React, {useContext, useEffect, useState} from "react";
import styles from "@/src/styles/paymentBilling.module.css";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import {UserContext} from "../../Contexts/UserContext";
import {queryData} from "../../Firebase/firebase.utils";
import Spinner from "../Widgets/Spinner";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import EnhancedTableHead from "@/src/Components/Table/EnhancedTableHead";
import quesStyles from "@/src/styles/questionsBank.module.css";
import {formatCurrency} from "@/src/Utils/helpers";
import { useTranslation } from "next-i18next";
import { PaymentBillingTableColumn, PaymentQuestionTableColumn } from "@/src/Types";

type Order = 'asc' | 'desc';

const purchaseColumns: readonly PaymentBillingTableColumn[] = [
    {id: "date", label: "Date", minWidth: 120, sorting: true},
    {id: "pack", label: "Pack", minWidth: 100, sorting: true},
    {id: "price", label: "Price", minWidth: 170, sorting: true},
];

type CombinedRow = {
    date: any;
    status: string;
    questions: any;
};

const questionColumns: readonly PaymentQuestionTableColumn[] = [
    {id: "date", label: "Date", minWidth: 120, sorting: true},
    {id: "status", label: "Status", minWidth: 100, sorting: true},
    {id: "questions", label: "Questions", minWidth: 170, sorting: true},
];

function descendingComparator<T>(a: T, b: T, orderBy: keyof T) {
    if (b[orderBy] < a[orderBy]) {
        return -1;
    }
    if (b[orderBy] > a[orderBy]) {
        return 1;
    }
    return 0;
}

function getComparator<Key extends keyof any>(
    order: Order,
    orderBy: Key,
): (a: Record<Key, number | string>, b: Record<Key, number | string>) => number {
    return order === 'desc'
        ? (a, b) => descendingComparator(a, b, orderBy)
        : (a, b) => -descendingComparator(a, b, orderBy);
}

const PaymentsBilling = () => {
    const { t } = useTranslation();
    const {user} = useContext(UserContext);
    const [first, setFirst] = useState(true);
    const [rows, setRows] = useState([] as any);
    const [fetching, setFetching] = useState(true);
    const [packRows, setPackRows] = useState([] as any);
    const [activeTab, setActiveTab] = useState(2);
    const [order, setOrder] = useState<Order>('desc');
    const [orderBy, setOrderBy] = useState<string>('date');

    useEffect(() => {
        if (first && user.id !== "") {
            setFirst(false);
            getQuestionsAndCredits();
            getTransactions();
        }
    }, [first, user]);

    const handleRequestSort = (event: React.MouseEvent<unknown>, property: string) => {
        const isAsc = orderBy === property && order === 'asc';
        setOrder(isAsc ? 'desc' : 'asc');
        setOrderBy(property);
    };

    const formatDate = (dateValue: string | number) => {
        const timestamp = typeof dateValue === 'string' ? dateValue : Number(dateValue);
        return new Date(Number(timestamp)).toDateString();
    };

    const groupByDate = (arr) => {
        const dateMap = arr.reduce((acc, item) => {
            const date = formatDate(item.date);

            if (!acc[date]) {
                acc[date] = {
                    date,
                    questions: 1,
                };
            } else {
                acc[date].questions++;
            }
            return acc;
        }, {});

        return Object.values(dateMap).sort(
            (a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime()
        );
    };

    const getTransactions = async () => {
        const resp: any = await queryData("transactions", "userId", user.id as string);
        const arr: any = [];
        if (resp.status && resp.fullData.length > 0) {
            const sortedArray = resp.fullData.sort(
                (a, b) => b.transactionDate - a.transactionDate
            );
            sortedArray.forEach((transaction: any) => {
                arr.push({
                    date: new Date(Number(transaction.transactionDate)).toDateString(),
                    pack: transaction.pack || "",
                    price: transaction.amount.toString() || "",
                    id: transaction.id || `trans-${transaction.transactionDate}` // Generate a unique ID for each row
                });
            });

            setPackRows(arr);
        } else {
            setPackRows([]);
        }
    };

    const getQuestionsAndCredits = async () => {
        // Fetch both regular questions and admin credits
        const [questionsResp, creditsResp]: [any, any] = await Promise.all([
            queryData("users_questions", "userId", user.id as string),
            queryData("admin_question_credits", "userId", user.id as string)
        ]);

        setFetching(false);

        let combinedRows: CombinedRow[] = [];

        // Process regular questions
        if (questionsResp.status && questionsResp.fullData.length > 0) {
            const groupedQuestions = groupByDate(questionsResp.fullData);
            combinedRows = [...combinedRows, ...groupedQuestions.map((item: any, index: number) => ({
                date: item.date,
                status: "Completed",
                questions: item.questions,
                id: `question-${index}` // Generate a unique ID for each row
            }))];
        }

        // Process admin credits
        if (creditsResp.status && creditsResp.fullData.length > 0) {
            const creditRows = creditsResp.fullData.map((credit, index) => ({
                date: formatDate(credit.date),
                status: credit.detail,
                questions: credit.operation === "remove" ? -credit.amount : credit.amount,
                id: credit.id || `credit-${index}` // Generate a unique ID for each row
            }));
            combinedRows = [...combinedRows, ...creditRows];
        }

        setRows(combinedRows);
    };

    // Apply sorting to the rows
    const sortedRows = React.useMemo(() => {
        if (!rows.length) return rows;
        return [...rows].sort(getComparator(order, orderBy));
    }, [rows, order, orderBy]);

    const sortedPackRows = React.useMemo(() => {
        if (!packRows.length) return packRows;
        return [...packRows].sort(getComparator(order, orderBy));
    }, [packRows, order, orderBy]);

    return (
        <div className={styles.container}>
            <div className={questionStyles.addQuestionTitle}>
                <h1>{t('student_payment_billing.title')}</h1>
                <div>{t('student_payment_billing.subtitle')}</div>
            </div>

            <div className={styles.innerContainer}>
                <div className={styles.rightSide}>
                    <div className={styles.questionsTable}>
                        <div className={styles.tabs}>
              <span
                  className={`${styles.tab}  ${activeTab === 1 && styles.activeTab}`}
                  onClick={() => {
                      setActiveTab(1);
                  }}
              >
                {t('student_payment_billing.tab_purchases')}
              </span>
                            <span
                                className={`${styles.tab} ${activeTab === 2 && styles.activeTab}`}
                                onClick={() => {
                                    setActiveTab(2);
                                }}
                            >
                {t('student_payment_billing.tab_questions')}
              </span>
                        </div>
                        <table className={styles.table}>
                            {activeTab === 1 ? (
                                <>
                                    {packRows.length > 0 ? (
                                        <div className={"tableWrapper multiColsTable"}>
                                            <TableContainer className={"tableContainer"}>
                                                <Table>
                                                    <EnhancedTableHead
                                                        columns={purchaseColumns as PaymentBillingTableColumn[]}
                                                        order={order}
                                                        orderBy={orderBy}
                                                        onRequestSort={handleRequestSort}
                                                    />
                                                    <TableBody>
                                                        {sortedPackRows && sortedPackRows.length ? (
                                                            sortedPackRows.map((row: any) => {
                                                                return (
                                                                    <TableRow
                                                                        hover
                                                                        role="checkbox"
                                                                        tabIndex={-1}
                                                                        key={row.id} // Using a unique ID from the row object
                                                                        className={
                                                                            row.isAdmin ? "blueColorRow" : ""
                                                                        }
                                                                    >
                                                                        {purchaseColumns.map((column) => {
                                                                            const value = row[column.id];
                                                                            return (
                                                                                <TableCell
                                                                                    key={column.id}
                                                                                    align={column.align}
                                                                                >
                                                                                    {column.id === "price"
                                                                                        ? `${formatCurrency(value)}`
                                                                                        : value}
                                                                                </TableCell>
                                                                            );
                                                                        })}
                                                                    </TableRow>
                                                                );
                                                            })
                                                        ) : (
                                                            <div className={quesStyles.searchLoader}>
                                                                {t('student_payment_billing.no_data_found')}
                                                            </div>
                                                        )}
                                                    </TableBody>
                                                </Table>
                                            </TableContainer>
                                        </div>
                                    ) : (
                                        <h3>{t('student_payment_billing.no_packs_purchased')}</h3>
                                    )}
                                </>
                            ) : (
                                <>
                                    {fetching ? (
                                        <Spinner/>
                                    ) : rows.length > 0 ? (
                                        <>
                                            <div className={"tableWrapper multiColsTable"}>
                                                <TableContainer className={"tableContainer"}>
                                                    <Table>
                                                        <EnhancedTableHead
                                                            columns={questionColumns as PaymentQuestionTableColumn[]}
                                                            order={order}
                                                            orderBy={orderBy}
                                                            onRequestSort={handleRequestSort}
                                                        />
                                                        <TableBody>
                                                            {sortedRows && sortedRows.length ? (
                                                                sortedRows.map((row: any) => {
                                                                    return (
                                                                        <TableRow
                                                                            hover
                                                                            role="checkbox"
                                                                            tabIndex={-1}
                                                                            key={row.id} // Using a unique ID from the row object instead of index
                                                                            className={
                                                                                row.isAdmin ? "blueColorRow" : ""
                                                                            }
                                                                        >
                                                                            {questionColumns.map((column) => {
                                                                                const value = row[column.id];
                                                                                return (
                                                                                    <TableCell
                                                                                        key={column.id}
                                                                                        align={column.align}
                                                                                    >
                                                                                        {column.id === "questions" && value < 0
                                                                                            ? value.toString() // The minus sign is already included since we made it negative
                                                                                            : value}
                                                                                    </TableCell>
                                                                                );
                                                                            })}
                                                                        </TableRow>
                                                                    );
                                                                })
                                                            ) : (
                                                                <div className={quesStyles.searchLoader}>
                                                                    {t('student_payment_billing.no_data_found')}
                                                                </div>
                                                            )}
                                                        </TableBody>
                                                    </Table>
                                                </TableContainer>
                                            </div>
                                        </>
                                    ) : (
                                        <h3>{t('student_payment_billing.no_questions_or_credits')}</h3>
                                    )}
                                </>
                            )}
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PaymentsBilling;