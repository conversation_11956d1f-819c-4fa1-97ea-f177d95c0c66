"use client";
import React, {useContext, useEffect, useLayoutEffect, useState} from "react";
import {UserContext} from "@/src/Contexts/UserContext";
import {useTranslation} from "@/src/Utils/i18n";
import styles from "@/src/styles/home.module.css";
import loginStyles from "@/src/styles/login.module.css";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import {useRouter} from "next/navigation";
import Spinner from "@/src/Components/Widgets/Spinner";
import {addDocInCollection, getDocument, updateCollection, uploadImage,} from "@/src/Firebase/firebase.utils";
import TextAreaField from "@/src/Components/Form/TextAreaField";
import SampleQuestionModal from "@/src/Components/Modals/SampleQuestionModal";
import FileInputWithPreview from "@/src/Components/Widgets/FileInputWithPreview";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import BorderButton from "@/src/Components/Buttons/BorderButton";


interface AskProps {
    setActiveTab?: (tabName: any) => void;
}

const AddQuestions: React.FC<AskProps> = ({
                                              setActiveTab
                                          }) => {
    const {t, locale} = useTranslation();
    const {user, langSwitch, setLangSwitch, setUser} = useContext(UserContext);
    const [adding, setAdding] = useState(false);
    // const [open, setOpen] = useState(false);
    const [open2, setOpen2] = useState(false);
    const [questionText, setQuestionText] = useState("");
    const [contentImg, setContentImg] = useState<any>(null);
    const [, setQuestionCounterId] = useState<any>(null);
    const [questionCounter, setQuestionCounter] = useState(0);
    const router = useRouter();
    // const handleClose = () => setOpen(false);
    const handleOpen2 = () => setOpen2(true);
    const handleClose2 = () => setOpen2(false);

    useEffect(() => {
        window.scrollTo({
            top: 0,
            behavior: "smooth",
        });
    }, []);

    useEffect(() => {
        getQuestionCounter();
    }, []);

    useLayoutEffect(() => {
        const defaultSwitchDone = localStorage.getItem("HMWK_DEFAULT_SWITCH_DONE");
        if (navigator.language === "es" && defaultSwitchDone !== "true") {
            localStorage.setItem("HMWK_DEFAULT_SWITCH_DONE", "true");
            router.push("/es");
        }
    });

    let id: any = "";
    if (typeof window !== "undefined") {
        id = localStorage.getItem("HMWK_LANGUAGE");
        if ((id === "" || !id || id === "en") && langSwitch) {
            setLangSwitch(false);
        }
    }

    const getQuestionCounter = async () => {
        const resp: any = await getDocument("user_question_counter", "counters");
        if (resp.status && resp.exists && resp.data) {
            setQuestionCounterId("user_question_counter");
            setQuestionCounter(resp.data.count);
        }
    };

    const handleAddQuestion = async () => {
        const paragraphs = questionText.split("\n").filter((p) => p.trim() !== ""); // Split by line break

        const data: any = {
            question: paragraphs,
            userId: user.id,
            exclusive: false,
            date: new Date().getTime().toString(),
            isAnswered: false,
            email: user.email,
            id: questionCounter + 1,
            name: user.firstName + " " + user.lastName,
            language: locale, // Add language field
        };

        setAdding(true);
        if (contentImg) {
            const imgResp = await uploadImage(contentImg);
            if (imgResp.status) {
                data.imageUrl = imgResp.downloadUrl;
            }
        }

        const resp = await addDocInCollection("users_questions", data, true);
        const resp2 = await updateCollection(
            "users",
            user.id as string,
            {
                questions: user.questions && user.questions - 1,
            },
            true
        );

        const resp3 = await updateCollection(
            "counters",
            "user_question_counter",
            {
                count: questionCounter + 1,
            },
            true
        );

        if (resp.status && resp2.status && resp3.status) {
            setUser({
                ...user,
                questions: user.questions - 1,
            });

            setQuestionCounter(questionCounter + 1);

            // clear form
            setQuestionText("");
            setContentImg(null);

            // to unanswered tab 
            router.push("/my-questions?tab=un");
            setActiveTab && setActiveTab(1);
        } else {
            setAdding(false);
        }
    };

    return (
        <div className={styles.page} style={{padding: '0 15px'}}>
            <div className={id === "" || !id || id === "en" ? styles.englishPage : ""}>
                {langSwitch || adding ? (
                    <Spinner/>
                ) : (
                    <div className={questionStyles.addQuestionBox}>
                        <div className={questionStyles.addQuestionTitle}>
                            <h1>{t('student.ask_question')}</h1>
                            <div>
                                {t('student.ask_question_description')}
                            </div>
                        </div>

                        {user.questions === 0 ? (
                            <h3 className={questionStyles.addQuestionBox}>
                                {t('student.purchase_pack_message')}
                            </h3>
                        ) : (
                            <div className={questionStyles.addQuestionModal}>
                                <div className={loginStyles.inputFieldWrap}>
                                    <div className={loginStyles.label}>
                                        <img src={'/icons/ask-form/comment-icon.svg'} alt={'comment icon'}/> {t('student.what_need_help_with')}
                                    </div>

                                    <TextAreaField
                                        showBorder={true}
                                        placeholder={t('student.question_placeholder')}
                                        rows={5}
                                        value={questionText}
                                        onChange={(e) => {
                                            if (questionText.length < 200) {
                                                setQuestionText(e.target.value);
                                            }
                                        }}
                                    />
                                </div>

                                <div className={loginStyles.inputFieldWrap}>
                                    <div className={loginStyles.label}>
                                        <img src={'/icons/ask-form/upload-icon.svg'} alt={'upload icon'}/> {t('student.upload_image')}
                                    </div>
                                    <FileInputWithPreview onChange={setContentImg} />
                                </div>

                                <div className={questionStyles.exampleQuestion}>
                                    {t('student.see_sample_question_see_an')}
                                   <a onClick={handleOpen2} style={{cursor: 'pointer'}}> {t('student.see_sample_question')}</a>
                                </div>

                                <div className={questionStyles.getAnswerBox}>
                                    <PrimaryButton
                                        size={'greenBtn'}
                                        label={t('student.submit_question')}
                                        onClick={handleAddQuestion}
                                        disabled={!questionText.trim() || adding}
                                        style={{minWidth: '248px'}}
                                        loading={adding}
                                    />
                                </div>

                                <div className={questionStyles.quesRemainBox}>
                                    <div className={questionStyles.quesRemainTotal}>
                                        <span>{user && user.questions}</span> {t('student.questions_remaining')}
                                    </div>
                                    <BorderButton
                                        label={t('student.buy_more')}
                                        onClick={() => router.push('/packs')}
                                        type={'dark'}
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                )}

                <SampleQuestionModal
                    open={open2}
                    title={t('student.sample_question')}
                    content={t('student.sample_question_description')}
                    handleClose={handleClose2}
                />
            </div>
        </div>
    );
};

export default AddQuestions;