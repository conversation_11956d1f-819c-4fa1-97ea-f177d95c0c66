"use client";
import React, { useContext, useEffect, useState } from "react";
import {useTranslation} from "@/src/Utils/i18n";
import styles from "@/src/styles/upgradePlan.module.css";
import homeStyles from "@/src/styles/home.module.css";
import PaymentForm from "@/src/Components/Payment/paymentForm";
import { Elements } from "@stripe/react-stripe-js";
import type { StripeElementsOptions } from '@stripe/stripe-js';
import { loadStripe } from "@stripe/stripe-js";
import { UserContext } from "../../Contexts/UserContext";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import Footer from "@/src/Components/Navigation/Footer";
import PricingSection from "@/src/Components/Homepage/sections/PricingSection";
import Spinner from "@/src/Components/Widgets/Spinner";
import {TabActivePrevProps} from "@/src/Types";

const options: StripeElementsOptions = {
  mode: 'payment',
  amount: 100,
  currency: 'usd',
  appearance: {
    variables: {
      colorText: "#3F3F3F",
    },
  },
};

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

const packDetails = {
  "10 question credits": { price: 50, credits: 10 },
  "50 question credits": { price: 75, credits: 15 },
  "150 question credits": { price: 100, credits: 20 },
};

const UpgradePlan = ({ setActiveTab }: TabActivePrevProps) =>
{
  const {t} = useTranslation();
  const { user } = useContext(UserContext);
  const [purchasing, setPurchasing] = useState(false);
  const [currentPrice, setCurrentPrice] = useState(50);
  const [currentCredits, setCurrentCredits] = useState(10);
  const [buyActive, setBuyActive] = useState(false);
  const [packInfo, setPackInfo] = useState({});
  const [buyClicked, setBuyClicked] = useState(false);
  const [packAvailable, setPackAvailable] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const pack = sessionStorage.getItem("pack");
      if (pack) {
        setPackAvailable(pack);
      }
    }
  }, []);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const pack = sessionStorage.getItem("pack");
      if (pack) {
        setPackAvailable(pack);
      }
    }
  }, []);

  useEffect(() => {
    if (packAvailable && user.id !== "") {
      const pack = packDetails[packAvailable];
      sessionStorage.removeItem("pack");
      setPackAvailable(null);
      setCurrentPrice(pack.price);
      setCurrentCredits(pack.credits);
      setPackInfo({
        name: packAvailable,
        credits: pack.credits,
      });
      setBuyActive(true);
    }
  }, [packAvailable, user]);

  if(!user.id){
    return (
        <Spinner />
    )
  }

  return (
    <div className={homeStyles.page}>
      <div className={styles.container} style={{ marginTop: buyActive ? "50px" : 0 }}>
        {buyActive ? (
          <>
            <div className={`${homeStyles.paymentContainer} containerWidth`}>
                <Elements stripe={stripePromise} options={options}>
                <PaymentForm
                  packPage={true}
                  packInfo={packInfo}
                  btnDisable={false}
                  setActiveTab={setActiveTab}
                  setBuyClicked={setBuyClicked}
                  buyClicked={buyClicked}
                  purchasing={purchasing}
                  setPurchasing={setPurchasing}
                  price={currentPrice}
                  handleClose={() => setBuyActive(false)}
                />
              </Elements>
              <div className={homeStyles.rightCards}>
                <div className={homeStyles.card1}>
                  <div className={homeStyles.row}>
                    <div className={homeStyles.title}>{t('student.question_pack')}</div>
                    <div className={homeStyles.price}>${currentPrice}</div>
                  </div>
                  <div className={homeStyles.divider}></div>
                  <div className={homeStyles.row}>
                    <div className={homeStyles.title}>{t('common.total')}</div>
                    <div className={homeStyles.price}>${currentPrice}</div>
                  </div>
                </div>
                <div className={homeStyles.card2}>
                  <h3>{t('homepage.features')}:</h3>
                  <ul>
                    <li>{t('student.get_question_credits', {count: currentCredits})}</li>
                    <li>{t('student.credits_never_expire')}</li>
                    <li>{t('student.satisfaction_guaranteed')}</li>
                  </ul>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            <div className={questionStyles.addQuestionTitle}>
              <h1>{user.id === "" ? t('navigation.pricing') : t('student.question_pack')}</h1>
              <div>
                {t('student.ask_question_description')}
              </div>
            </div>

            <PricingSection onlyPlans={true} t={t} />
          </>
        )}
      </div>
      {user.id === "" && <Footer id="en" t={t} />}
    </div>
  );
};

export default UpgradePlan;