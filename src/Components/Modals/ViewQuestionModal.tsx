"use client";
import React from "react";
import {Box, Modal} from "@mui/material";
import styles from "@/src/styles/modals.module.css";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import {askModalStyle} from "@/src/Utils/styles";
import {useTranslation} from "@/src/Utils/i18n";

const ViewQuestionModal = ({
                               open,
                               title,
                               selectedQuestion,
                               handleClose,
                           }: {
    open: boolean;
    title: string;
    selectedQuestion: any;
    handleClose: () => void;
}) => {
    const {t} = useTranslation();
    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="child-modal-title"
            aria-describedby="child-modal-description"
        >
            <Box sx={{...askModalStyle}}>
                <div className={styles.modalHead}>
                    <div className={styles.modalTitle}>{t('modal_view_question_modal.title')}</div>
                </div>

                <div className={styles.modalBody}>
                    <div className={styles.sampleQuesBox} style={{flexDirection: 'column', gap: '20px'}}>
                        <div className={styles.modalRow}>
                            <div className={styles.modalRowTitle}>{t('modal_view_question_modal.question')}</div>
                            <div className={styles.modalRowText}>{selectedQuestion && selectedQuestion.question}</div>
                        </div>
                        <div className={styles.modalRow}>
                            <div className={styles.modalRowTitle}>{t('modal_view_question_modal.applicants_answer')}</div>
                            <div className={styles.modalRowText}>{selectedQuestion && selectedQuestion.answer}</div>
                        </div>
                    </div>
                </div>

                <div className={styles.modalFooter}>
                    <PrimaryButton
                        onClick={handleClose}
                        label={t('modal_view_question_modal.close')}
                        size={'medium'}
                    />
                </div>
            </Box>
        </Modal>
    );
};

export default ViewQuestionModal;
