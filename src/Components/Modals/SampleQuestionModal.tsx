"use client";
import React from "react";
import { Box, Modal } from "@mui/material";
import styles from "@/src/styles/modals.module.css";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import {askModalStyle} from "@/src/Utils/styles";
import { useTranslation } from "@/src/Utils/i18n";

const SampleQuestionModal = ({
  open,
  title,
  content,
  handleClose,
}: {
  open: boolean;
  isLoading?: boolean;
  title: string;
  content: string;
  handleClose: () => void;
}) => {
  const { t } = useTranslation();
  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="child-modal-title"
      aria-describedby="child-modal-description"
    >
      <Box sx={{ ...askModalStyle }}>
        <img onClick={handleClose} className={styles.modalCloseIcon} src={'/icons/modal-close.svg'} alt={'close Icon'} />
        <div className={styles.modalHead}>
          <div className={styles.modalTitle}>{title}</div>
          <div className={styles.modalText}>{content}</div>
        </div>

        <div className={styles.modalBody}>
          <div className={styles.sampleQuesBox}>
            <img src={'/images/sample-ques.png'} alt={'sample'} />
            <div>{t('modal_sample_question_modal.sample_text')}</div>
          </div>
        </div>

        <div className={styles.modalFooter}>
          <PrimaryButton
              onClick={handleClose}
              label={t('modal_sample_question_modal.close')}
              size={'greenBtn'}
          />
        </div>
      </Box>
    </Modal>
  );
};

export default SampleQuestionModal;
