"use client";
import React, {useEffect, useState} from "react";
import {Box, Modal} from "@mui/material";
import {doc, updateDoc} from "firebase/firestore";
import {db} from "@/src/Firebase/firebase.utils";
import modalStyles from "@/src/styles/modals.module.css";
import SmallBorderButton from "../Buttons/SmallBorderButton";
import PrimaryButton from "../Buttons/PrimaryButton";
import CurrencyInputField from "@/src/Components/Form/CurrencyInputField";
import {AppNotify} from "@/src/Utils/AppNotify";
import {askModalStyle} from "@/src/Utils/styles";
import {useTranslation} from "@/src/Utils/i18n";

const SetPriceModal = ({
                           open,
                           title,
                           defaultPrice,
                           cancelLabel,
                           actionLabel,
                           handleClose,
                           onCancel,
                       }: {
    open: boolean;
    title: string;
    defaultPrice: string;
    actionLabel: string;
    handleClose: () => void;
    onCancel?: () => void;
    cancelLabel?: string;
}) => {
    const {t} = useTranslation();
    const [price, setPrice] = useState('');
    const [isLoading, setLoading] = useState(false);

    useEffect(() => {
        if (defaultPrice) {
            setPrice(defaultPrice);
        }
    }, [defaultPrice]);

    const handleSavePrice = async () => {
        if (!price) {
            AppNotify(t('modal_set_price_modal.error_no_price'), 'error');
            return;
        }

        if (price) {
            setLoading(true);

            // Reference to the specific document
            const adminConfigDocRef = doc(db, "admin_config", 'test_question_pay');

            // Update the object key-value pair
            await updateDoc(adminConfigDocRef as any, {amount: price} as any);

            AppNotify(t('modal_set_price_modal.success'), 'success');
            setLoading(false);
            handleClose();
        }
    }

    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="child-modal-title"
            aria-describedby="child-modal-description"
        >
            <Box sx={{...askModalStyle, maxWidth: '430px'}}>
                <div style={{padding: '0 20px 17px'}}>
                    <div className={modalStyles.modalHead}>
                        {title ? (
                            <h2 style={{margin: 0}}>{t('modal_set_price_modal.title')}</h2>
                        ) : (
                            ""
                        )}
                        <div className={modalStyles.modalContent}>{t('modal_set_price_modal.current_price', {price: defaultPrice})}</div>
                    </div>

                    <div className={modalStyles.modalFieldRow}>
                        <div className={modalStyles.inputLabel} style={{color: '#0E121B', fontWeight: '500'}}>
                            {t('modal_set_price_modal.new_price_label')}
                        </div>
                        <div className={modalStyles.inputField}>
                            <CurrencyInputField value={price} onChange={setPrice}/>
                        </div>
                    </div>
                </div>

                <div className={`${modalStyles.modalFooter} ${modalStyles.modalFooterCols}`} style={{marginTop: "0"}}>
                    <SmallBorderButton
                        className={modalStyles.modalCancelButton}
                        label={cancelLabel || t('modal_set_price_modal.cancel')}
                        onClick={onCancel ? onCancel : handleClose}
                    />

                    <PrimaryButton
                        onClick={handleSavePrice}
                        label={actionLabel || t('modal_set_price_modal.save')}
                        size={"medium"}
                        loading={isLoading}
                    />
                </div>
            </Box>
        </Modal>
    );
};

export default SetPriceModal;
