"use client";
import React from "react";
import {Box, Modal} from "@mui/material";
import parse from "html-react-parser";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import modalStyles from "@/src/styles/modals.module.css";
import {reminderModalStyle} from "@/src/Utils/styles";

const ReminderModal = ({
   open,
   isLoading,
   icon,
   title,
   content,
   actionLabel,
   onConfirm,
   handleClose,
}: {
    open: boolean;
    isLoading?: boolean;
    icon?: string;
    title?: string;
    content?: string;
    actionLabel?: string;
    onConfirm?: () => void;
    handleClose?: () => void;
}) => {
    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="child-modal-title"
            aria-describedby="child-modal-description"
        >
            <Box className={modalStyles.reminderModal} sx={{...reminderModalStyle}}>
                <img onClick={handleClose} className={modalStyles.modalCloseIcon} src={'/icons/modal-close-white.svg'} alt={'close Icon'} />
                <div className={modalStyles.modalHead}>
                    {icon ? <img src={icon} alt={"modal-icon"}/> : ''}
                    <div className={modalStyles.remModalInfo}>
                        {title ? (
                            <div className={modalStyles.modalTitle}>{title}</div>
                        ) : (
                            ""
                        )}
                        {content ? (
                            <div className={modalStyles.modalText}>{parse(content)}</div>
                        ) : (
                            ""
                        )}
                    </div>
                </div>

                <div className={modalStyles.modalFooter} style={{marginTop: "0"}}>
                    <PrimaryButton
                        onClick={onConfirm ? onConfirm : handleClose}
                        label={actionLabel ? actionLabel : "Close"}
                        size={"blueBtn"}
                        loading={isLoading}
                    />
                </div>
            </Box>
        </Modal>
    );
};

export default ReminderModal;
