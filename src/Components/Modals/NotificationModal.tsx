"use client";
import React from "react";
import {useTranslation} from "@/src/Utils/i18n";
import {Box, Modal} from "@mui/material";
import parse from "html-react-parser";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import modalStyles from "@/src/styles/modals.module.css";
import {deleteModalStyle, notificationModalStyle} from "@/src/Utils/styles";
import BorderButton from "@/src/Components/Buttons/BorderButton";

const NotificationModal = ({
   open,
   isDelete,
   isLoading,
   biggerButton,
   icon,
   title,
   content,
   actionLabel,
   cancelLabel,
   onConfirm,
   onCancel,
   handleClose,
}: {
    open: boolean;
    isDelete?: boolean;
    isLoading?: boolean;
    biggerButton?: boolean;
    icon?: string;
    title?: string;
    content?: string;
    actionLabel?: string;
    cancelLabel?: string;
    onConfirm?: () => void;
    onCancel?: () => void;
    handleClose?: () => void;
}) => {
    const {t} = useTranslation();
    
    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="child-modal-title"
            aria-describedby="child-modal-description"
        >
            <Box className={modalStyles.notificationModal} sx={isDelete ? {...deleteModalStyle} : {...notificationModalStyle}}>
                <img onClick={handleClose} className={modalStyles.modalCloseIcon} src={'/icons/modal-close.svg'} alt={'close Icon'} />

                <div className={modalStyles.modalHead}>
                    {icon ? <img src={icon} alt={"modal-icon"}/> : ''}
                    {title ? (
                        <div style={{marginTop: icon ? "20px" : '0'}} className={modalStyles.modalTitle}>{title}</div>
                    ) : (
                        ""
                    )}
                    {content ? (
                        <div className={modalStyles.modalText}>{parse(content)}</div>
                    ) : (
                        ""
                    )}
                </div>

                <div className={cancelLabel ? `${modalStyles.modalFooter} ${modalStyles.modalFooterCols}` : modalStyles.modalFooter} style={{marginTop: "0"}}>
                    {cancelLabel ? (
                        <BorderButton
                            type={'dark'}
                            label={cancelLabel}
                            onClick={onCancel ? onCancel : handleClose}
                        />
                    ) : (
                        ""
                    )}
                    <PrimaryButton
                        onClick={onConfirm ? onConfirm : handleClose}
                        label={actionLabel ? actionLabel : t('common.close')}
                        size={biggerButton ? "greenBtn" : "greenBtn"}
                        loading={isLoading}
                    />
                </div>
            </Box>
        </Modal>
    );
};

export default NotificationModal;
