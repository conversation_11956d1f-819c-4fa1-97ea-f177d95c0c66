"use client";
import React from "react";
import {useTranslation} from "@/src/Utils/i18n";
import {Box, Modal} from "@mui/material";
import modalStyles from "@/src/styles/modals.module.css";
import PrimaryButton from "../Buttons/PrimaryButton";
import {askModalStyle, deleteConfirmModalStyle} from "@/src/Utils/styles";
import BorderButton from "@/src/Components/Buttons/BorderButton";

const ConfirmModal = ({
                          open,
                          isLoading,
                          title,
                          content,
                          cancelLabel,
                          actionLabel,
                          handleClose,
                          onConfirm,
                          onCancel,
                          isDeleteConfirm
                      }: {
    open: boolean;
    isLoading?: boolean;
    isDeleteConfirm?: boolean;
    title: string;
    content: string;
    actionLabel: string;
    handleClose: () => void;
    onConfirm: () => void;
    onCancel?: () => void;
    cancelLabel?: string;
}) => {
    const {t} = useTranslation();
    
    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="child-modal-title"
            aria-describedby="child-modal-description"
        >
            <Box sx={isDeleteConfirm ? {...deleteConfirmModalStyle} : {...askModalStyle}} style={{padding: '30px'}}>
                <div className={modalStyles.modalHead}>
                    {title ? (
                        <h2 style={{margin: 0}}>{title}</h2>
                    ) : (
                        ""
                    )}
                    {content ? (
                        <div className={modalStyles.modalContent}>{content}</div>
                    ) : (
                        ""
                    )}
                </div>
                <div className={`${modalStyles.modalFooter} ${modalStyles.modalFooterCols}`} style={{marginTop: "0"}}>
                    <BorderButton
                        type={'dark'}
                        label={cancelLabel || t('common.cancel')}
                        onClick={onCancel ? onCancel : handleClose}
                    />

                    <PrimaryButton
                        size={'greenBtn'}
                        onClick={onConfirm}
                        label={actionLabel || t('common.delete')}
                        loading={isLoading}
                    />
                </div>
            </Box>
        </Modal>
    );
};

export default ConfirmModal;
