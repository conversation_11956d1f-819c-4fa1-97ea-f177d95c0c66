import React, {useEffect, useState} from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, TextField } from '@mui/material';
import { Add, Remove } from '@mui/icons-material';
import InputAdornment from "@mui/material/InputAdornment";
import styles from "@/src/styles/quantityStepper.module.css";
import {QuantityStepperProps} from "@/src/Types";

const QuantityStepper: React.FC<QuantityStepperProps> = ({ defaultValue = 0, min, max, suffix = '',  onChange }) =>
{
    const [quantity, setQuantity] = useState<number>(defaultValue || 0);

    useEffect(() => {
        // Clamp the defaultValue to ensure it stays within bounds
        const clampedValue = Math.min(Math.max(defaultValue || 0, min), max);
        setQuantity(clampedValue);
        onChange(clampedValue);
    }, [defaultValue, min, max, onChange]);

    const updateQuantity = (newQuantity: number) => {
        setQuantity(newQuantity);
        onChange(newQuantity);
    };

    const handleIncrement = () => {
        const newQuantity = Math.min(quantity + 1, max);
        updateQuantity(newQuantity);
    };

    const handleDecrement = () => {
        const newQuantity = Math.max(quantity - 1, min);
        updateQuantity(newQuantity);
    };

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = parseInt(event.target.value, 10);
        if (!isNaN(value)) {
            updateQuantity(Math.min(Math.max(value, min), max));
        } else {
            updateQuantity(min);
        }
    };

    return (
        <div className={styles.quantityWrapper}>
            <IconButton className={styles.iconButton} onClick={handleDecrement} color="primary" disabled={quantity <= min}>
                <Remove />
            </IconButton>
            <TextField
                value={quantity}
                onChange={handleInputChange}
                type="text"
                variant="outlined"
                size="small"
                inputProps={{
                    min,
                    max,
                    style: { textAlign: 'center', width: '50px', height: '26px', padding: 0, border:0, fontSize : '16px', color : 'rgba(75, 75, 75, 1)', fontWeight: '500' }
                }}
                InputProps={suffix ? {
                    endAdornment: (
                        <InputAdornment
                            position="end"
                            sx={{
                                "& .MuiTypography-root": {
                                    fontSize: "12px",
                                    fontFamily: 'inherit',
                                    marginLeft: '-4px',
                                    marginRight: '8px'
                                },
                            }}
                        >
                            {suffix}
                        </InputAdornment>
                    ),
                } : undefined}
            />
            <IconButton className={styles.iconButton} onClick={handleIncrement} color="primary" disabled={quantity >= max}>
                <Add />
            </IconButton>
        </div>
    );
};

export default QuantityStepper;
