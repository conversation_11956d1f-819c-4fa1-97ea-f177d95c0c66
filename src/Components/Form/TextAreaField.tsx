import React, {ChangeEvent, useEffect, useState} from 'react';
import TextField from '@mui/material/TextField';
import {defaultFilledTextareaFieldStyle, defaultTextareaFieldStyle} from "@/src/Utils/styles";
import {TextAreaFieldProps} from "@/src/Types";

const TextAreaField: React.FC<TextAreaFieldProps> = ({
                                                         label,
                                                         value = '',
                                                         placeholder = '',
                                                         onChange,
                                                         showBorder = false,
                                                         disabled = false,
                                                         fullWidth = false,
                                                         filledArea = false,
                                                         rows = 3,
                                                         maxRows,
                                                         maxLength,
                                                         ...props
                                                     }) => {
    const [inputValue, setInputValue] = useState<string>('');
    const [isFocused, setIsFocused] = useState(false);

    const handleFocus = () => {
        setIsFocused(true);
    };

    const handleBlur = () => {
        setIsFocused(false);
    };

    useEffect(() => {
        if (value) {
            setInputValue(value);
        }
    }, [value]);

    const handleChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
        let newValue = event.target.value;

        if (typeof maxLength === 'number' && newValue.length > maxLength) {
            newValue = newValue.slice(0, maxLength);
        }

        setInputValue(newValue);

        if (onChange) {
            const customEvent = {
                ...event,
                target: {
                    ...event.target,
                    value: newValue,
                },
            };
            onChange(customEvent as ChangeEvent<HTMLTextAreaElement>);
        }
    };

    return (
        <TextField
            label={label}
            variant="outlined"
            value={inputValue}
            onChange={handleChange}
            sx={{
                ...(filledArea ? defaultFilledTextareaFieldStyle : defaultTextareaFieldStyle),
                '@media (max-width: 399px)': {
                    padding: '0',
                    '& .MuiOutlinedInput-root': {
                        fontSize: '12px',
                        padding: '12px'
                    },
                },
            }}
            multiline
            rows={rows}
            maxRows={maxRows}
            fullWidth={fullWidth}
            disabled={disabled}
            placeholder={placeholder}
            onFocus={handleFocus}
            onBlur={handleBlur}
            InputProps={{
                classes: {
                    input: isFocused ? 'webkitScrollbar' : 'hiddenScrollbar',
                },
            }}
            style={{
                border: showBorder && disabled ? '2px solid rgba(0, 0, 0, 0.3)' : showBorder ? '2px solid rgba(229, 229, 229, 1)' : '',
                borderRadius: showBorder ? '16px' : '',
                overflowY: isFocused ? 'auto' : 'hidden',
                maxHeight: maxRows ? `${maxRows * 24}px` : undefined,
            }}
            {...props}
        />
    );
};

export default TextAreaField;