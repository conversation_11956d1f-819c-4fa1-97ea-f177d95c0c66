import React, {ChangeEvent, useEffect, useState} from "react";
import TextField from "@mui/material/TextField";
import {CircularProgress} from "@mui/material";
import InputAdornment from "@mui/material/InputAdornment";
import {defaultErrorInputFieldStyle, defaultInputFieldStyle, highlightFieldStyle} from "@/src/Utils/styles";
import {InputTextFieldProps} from "@/src/Types";

const InputTextField: React.FC<InputTextFieldProps> = ({
                                                           label,
                                                           icon,
                                                           value = "",
                                                           onChange,
                                                           isLoading = false,
                                                           disabled = false,
                                                           highlightField = false,
                                                           fullWidth = false,
                                                           ...props
                                                       }) => {
    const [inputValue, setInputValue] = useState<string>("");

    useEffect(() => {
        if (value) {
            setInputValue(value);
        } else {
            setInputValue("");
        }
    }, [value]);

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        setInputValue(event.target.value);
        if (onChange) {
            onChange(event);
        }
    };

    const baseStyle = props.error ? defaultErrorInputFieldStyle : defaultInputFieldStyle;

    const responsiveStyle = {
        '@media (max-width:659px)': {
            '& .MuiOutlinedInput-root': {
                padding: '12px',
                fontSize: '14px',
                height: '45px',
            },
        },
    };

    const highlightStyle = highlightField ? highlightFieldStyle : {};

    return (
        <TextField
            label={label}
            variant="outlined"
            value={inputValue}
            onChange={handleChange}
            sx={{
                ...baseStyle,
                ...responsiveStyle,
                ...highlightStyle
            }}
            InputProps={{
                startAdornment: icon ? (
                    <InputAdornment position="start">
                        <img className={'inputStartIcon'} src={`/icons/${icon}.svg`} alt={"left-icon"}/>
                    </InputAdornment>
                ) : (
                    ""
                ),
                endAdornment: isLoading ? (
                    <InputAdornment position="end">
                        <CircularProgress size={15} color="inherit"/>
                    </InputAdornment>
                ) : (
                    ""
                )
            }}
            fullWidth={fullWidth}
            disabled={disabled}
            {...props}
        />
    );
};

export default InputTextField;
