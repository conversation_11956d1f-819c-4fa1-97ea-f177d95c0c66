import React, {ChangeEvent, useState} from 'react';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import {defaultInputFieldStyle} from "@/src/Utils/styles";
import { useTranslation } from 'react-i18next';

interface PasswordFieldProps {
    label?: string;
    showIcon?: boolean;
    value?: string;
    onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
    fullWidth?: boolean;
    autoShrink?: boolean;
    checkStrength?: boolean;

    [key: string]: any;
}

const PasswordField: React.FC<PasswordFieldProps> = ({
                                                         label,
                                                         showIcon,
                                                         value = '',
                                                         onChange,
                                                         autoShrink,
                                                         checkStrength,
                                                         fullWidth = false,
                                                         ...props
                                                     }) => {
    const [inputValue, setInputValue] = useState<string>(value ?? '');
    const [showPassword, setShowPassword] = useState(false);
    const {t} = useTranslation();

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        setInputValue(event.target.value);
        if (onChange) {
            onChange(event);
        }
    };

    const handleMouseDownPassword = (
        event: React.MouseEvent<HTMLButtonElement>
    ) => {
        event.preventDefault();
    };

    const handleClickShowPassword = () => setShowPassword((show) => !show);
    let inputClasses = {};

    if (checkStrength) {
        if (inputValue && inputValue.length >= 8) {
            // inputClasses = {root: 'validPassField'};
        } else {
            // inputClasses = {root: 'errorPassField'};
        }
    }

    return (
        <TextField
            label={label}
            variant="outlined"
            value={inputValue}
            onChange={handleChange}
            classes={inputClasses}
            type={showPassword ? "text" : "password"}
            error={checkStrength && inputValue.length > 0 && inputValue.length < 8}
            helperText={checkStrength && inputValue.length > 0 && inputValue.length < 8 ? t('password_field.must_8_characters') : ''}
            sx={{
                ...(defaultInputFieldStyle),
                '@media (max-width:659px)': {
                    '& .MuiOutlinedInput-root': {
                        padding: '12px',
                        fontSize: '14px',
                        height: "45px",
                    },
                },
            }}
            InputProps={{
                startAdornment: showIcon ? (
                    <InputAdornment position="start">
                        <img className={'inputStartIcon'} src={`/icons/lock-icon.svg`} alt={'lock-icon'}/>
                    </InputAdornment>
                ) : '',
                endAdornment: (
                    <InputAdornment position="end">
                        <IconButton
                            aria-label="toggle password visibility"
                            onClick={handleClickShowPassword}
                            onMouseDown={handleMouseDownPassword}
                            edge="end"
                        >
                            <img
                                className={'inputEndIcon'}
                                style={{width: '24px', height: '24px'}}
                                src={showPassword ? '/icons/eye-strike-icon.svg' : '/icons/eye-icon.svg'}
                                alt={showPassword ? 'eye-strike-icon' : 'eye-icon'}
                            />
                        </IconButton>
                    </InputAdornment>
                ),
            }}
            FormHelperTextProps={{
                className: 'customHelperText'
            }}
            fullWidth={fullWidth}
            {...props}
        />
    );
};

export default PasswordField;
