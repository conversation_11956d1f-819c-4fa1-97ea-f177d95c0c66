import React, {useEffect, useState} from "react";
import TextField from "@mui/material/TextField";
import InputAdornment from "@mui/material/InputAdornment";
import {styled} from "@mui/material/styles";
import {defaultInputFieldStyle} from "@/src/Utils/styles";
import {CurrencyInputProps} from "@/src/Types";

const StyledEndAdornment = styled("div")(({theme}) => ({
    paddingLeft: theme.spacing(1.5),
    borderLeft: `1px solid #E1E4EA`,
    height: "40px",
    display: "flex",
    alignItems: "center",
    color: "#0E121B",
}));

const CurrencyInputField: React.FC<CurrencyInputProps> = ({
                                                              value,
                                                              onChange,
                                                              currencySymbol = "$",
                                                              currencyCode = "USD",
                                                              defaultValue = "0.00",
                                                          }) => {
    const [internalValue, setInternalValue] = useState<string>(defaultValue);

    useEffect(() => {
        if (value !== undefined) {
            setInternalValue(value);
        }
    }, [value]);

    const formatCurrency = (val: string) => {
        const cleaned = val.replace(/[^0-9.]/g, "");
        const num = parseFloat(cleaned) || 0;
        return num.toFixed(0);
    };

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const inputVal = event.target.value.replace(/[^0-9.]/g, "");
        setInternalValue(inputVal);
        onChange(formatCurrency(inputVal)); // Pass formatted value to parent
    };

    return (
        <TextField
            value={internalValue}
            onChange={handleChange}
            placeholder={'0.00'}
            fullWidth
            sx={defaultInputFieldStyle}
            InputProps={{
                startAdornment: <InputAdornment position="start"><span style={{
                    fontSize: '14px',
                    color: '#99A0AE',
                    fontWeight: '400'
                }}>{currencySymbol}</span></InputAdornment>,
                endAdornment: (
                    <InputAdornment position="end">
                        <StyledEndAdornment>{currencyCode}</StyledEndAdornment>
                    </InputAdornment>
                ),
            }}
        />
    );
};

export default CurrencyInputField;
