import React, {useEffect, useState} from "react";
import parse from "html-react-parser";
import {styled} from "@mui/material/styles";
import {CustomCheckboxProps} from "@/src/Types";

const CheckboxContainer = styled("div")({
    display: "flex",
    alignItems: "center",
});

const HiddenCheckbox = styled("input")({
    border: 0,
    clip: "rect(0 0 0 0)",
    clipPath: "inset(50%)",
    height: "1px",
    margin: "-1px",
    overflow: "hidden",
    padding: 0,
    position: "absolute",
    whiteSpace: "nowrap",
    width: "1px",
});

const StyledCheckbox = styled("div")<{ checked: boolean }>(({checked}) => ({
    width: "24px",
    height: "24px",
    borderRadius: "8px",
    transition: "all 150ms",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    border: "1px solid #ccc",
    borderColor: checked ? "#6bcc09" : "#ccc",
    svg: {
        visibility: checked ? "visible" : "hidden",
        fill: "#6bcc09",
    },
}));

const CustomLabel = styled("label")(({theme}) => ({
    fontSize: "16px",
    fontWeight: 500,
    color: "#777",
    cursor: "pointer",
    marginLeft: '10px',
}));

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
                                                           id,
                                                           defaultLabel,
                                                           defaultChecked = false,
                                                           onChange,
                                                       }) => {
    const [checked, setChecked] = useState(defaultChecked);
    const [label, setLabel] = useState(defaultLabel);

    useEffect(() => {
        setLabel(defaultLabel);
    }, [defaultLabel]);

    useEffect(() => {
        setChecked(defaultChecked);
    }, [defaultChecked]);

    const handleCheckboxClick = () => {
        const newChecked = !checked;
        setChecked(newChecked);
        onChange?.(newChecked);
    };

    return (
        <CheckboxContainer>
            <HiddenCheckbox
                id={id}
                type="checkbox"
                checked={checked}
                readOnly
            />
            <StyledCheckbox
                checked={checked}
                onClick={handleCheckboxClick}
            >
                <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" clipRule="evenodd"
                          d="M12.3133 1.18539C12.4651 1.33735 12.5503 1.54335 12.5503 1.75813C12.5503 1.9729 12.4651 2.1789 12.3133 2.33086L5.8295 8.81466C5.67754 8.96643 5.47154 9.05168 5.25677 9.05168C5.04199 9.05168 4.83599 8.96643 4.68403 8.81466L1.44213 5.57276C1.29897 5.41912 1.22103 5.21591 1.22474 5.00594C1.22844 4.79597 1.3135 4.59564 1.46199 4.44715C1.61048 4.29866 1.81082 4.2136 2.02079 4.20989C2.23075 4.20619 2.43396 4.28413 2.5876 4.42729L5.25677 7.09578L11.1678 1.18539C11.3198 1.03362 11.5258 0.948364 11.7406 0.948364C11.9553 0.948364 12.1613 1.03362 12.3133 1.18539Z"
                          fill="#6BCC09" stroke="#6BCC09"/>
                </svg>
            </StyledCheckbox>
            {label ? (
                <CustomLabel className="checkboxLabel" htmlFor={id} onClick={handleCheckboxClick}>
                    {parse(label)}
                </CustomLabel>
            ) : null}
        </CheckboxContainer>
    );
};

export default CustomCheckbox;
