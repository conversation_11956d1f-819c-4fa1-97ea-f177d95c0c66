import React, {useEffect, useState} from 'react';
import styles from "@/src/styles/widgets.module.css";
import {CustomSelectProps} from "@/src/Types";

const CustomSelect: React.FC<CustomSelectProps> = ({
                                                       options,
                                                       onSelect,
                                                       icon,
                                                       placeholder = "",
                                                       defaultValue = "",
                                                       type = "light",
                                                       width = "",
                                                       message = "",
                                                       highlightField = false,
                                                       fullWidth = false,
                                                       disabled = false,
                                                   }) => {
    const [selectedValue, setSelectedValue] = useState<string>('');

    useEffect(() => {
        setSelectedValue(defaultValue);
    }, [defaultValue]);

    const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const value = event.target.value;
        setSelectedValue(value);
        onSelect(value);
    };

    let containerClass = fullWidth ? `${styles.selectContainer} fullWidth` : `${styles.selectContainer}`;
    containerClass = disabled ? `${containerClass} ${styles.selectDisable} ` : `${containerClass}`;
    let selectClass = type && type === 'dark' ? `${styles.select} ${styles.selectDark}` : type && type === 'filled' ? `${styles.select} ${styles.selectFilled}` : `${styles.select}`;
    selectClass = icon ? `${selectClass} ${styles.selectHasIcon}` : selectClass;
    selectClass = highlightField ? `${selectClass} ${styles.selectHighlightField}` : selectClass;

    return (
        <div className={styles.customSelectWrapper}>
            <div className={containerClass}>
                {icon ? <img className={styles.customSelectIcon} src={icon} alt={'icon'}/> : ''}
                <select className={selectClass} onChange={handleChange} value={selectedValue} style={{width: width}}
                        disabled={disabled}>
                    {placeholder ? <option value="" disabled hidden>{placeholder}</option> : ''}
                    {options.map(option => (
                        <option key={option.value} value={option.value}>
                            {option.label}
                        </option>
                    ))}
                </select>
                <span className={styles.customArrow}>
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" clipRule="evenodd"
                            d="M7.28626 9.30937C7.47355 9.12232 7.72742 9.01725 7.99212 9.01725C8.25682 9.01725 8.5107 9.12232 8.69799 9.30937L11.9876 12.5981L15.2771 9.30937C15.3686 9.21123 15.4789 9.13252 15.6014 9.07793C15.7239 9.02333 15.8562 8.99398 15.9903 8.99161C16.1244 8.98924 16.2576 9.01392 16.382 9.06415C16.5064 9.11439 16.6194 9.18916 16.7142 9.28401C16.8091 9.37886 16.8839 9.49185 16.9341 9.61622C16.9843 9.7406 17.009 9.87382 17.0066 10.0079C17.0043 10.1421 16.9749 10.2743 16.9203 10.3968C16.8657 10.5194 16.787 10.6296 16.6889 10.7211L12.6934 14.7165C12.5061 14.9036 12.2523 15.0087 11.9876 15.0087C11.7229 15.0087 11.469 14.9036 11.2817 14.7165L7.28626 10.7211C7.09921 10.5338 6.99414 10.2799 6.99414 10.0152C6.99414 9.75053 7.09921 9.49666 7.28626 9.30937Z"
                            fill="#777777"/>
                  </svg>
                </span>
            </div>
            {message ? <div className={styles.customSelectMsg}>{message}</div> : ''}
        </div>
    );
};

export default CustomSelect;