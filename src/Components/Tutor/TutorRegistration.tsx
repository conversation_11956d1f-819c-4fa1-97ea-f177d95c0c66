"use client";
import React, {useCallback, useState} from "react";
import Settings from "@/src/Components/Common/Settings";
import styles from "@/src/styles/settingsPage.module.css";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import tpStyles from "@/src/styles/tutorProfile.module.css";
import ProgressBar from "../Widgets/ProgressBar";

const TutorRegistration = () => {
    const [progressData, setProgressData] = useState({
        value: 0,
        message: "Let's get started! 🚀"
    });

    const handleProgressUpdate = useCallback((progress: { value: number; message: string }) => {
        setProgressData(progress);
    }, []);

    return (
        <div className={styles.settingsContainer}>
            <div className={questionStyles.addQuestionTitle}>
                <h1>Tutor Registration</h1>
            </div>

            <div className={tpStyles.completeRegBox}>
                <div className={tpStyles.crBoxHead}>
                    <div className={tpStyles.crBoxTitle}>Complete Your Registration</div>
                    <div className={tpStyles.crBoxText}>
                        {progressData.value === 100 ? "Registration complete! You can now earn $3 for every question you answer." : "Once you complete your registration, you will be able to earn $3 for every question you answer."}
                    </div>
                </div>

                <div className={tpStyles.crBoxProgress}>
                    <ProgressBar
                        showTooltip={true}
                        value={progressData.value}
                        max={100}
                        tooltipText={`${progressData.value}% complete - ${progressData.message}`}
                    />
                    <img
                        src={'/icons/treasure-box.svg'}
                        alt={'treasure-box'}
                        style={{
                            filter: progressData.value === 100 ? 'brightness(1.2) saturate(1.3)' : 'none',
                            transform: progressData.value === 100 ? 'scale(1.05)' : 'scale(1)',
                            transition: 'all 0.3s ease'
                        }}
                    />
                </div>

                <img className={tpStyles.crBoxWaveImg} src={'/icons/wave-flow.svg'} alt={'wave-flow'}/>
            </div>

            <Settings
                isTutorReg={true}
                onProgressUpdate={handleProgressUpdate}
            />
        </div>
    );
}

export default TutorRegistration;