import React, {Suspense, useContext, useEffect, useRef, useState} from "react";
import {useRouter, useSearchParams} from "next/navigation";
import Slider from 'react-slick';
import axios from 'axios';
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import FileInputWithPreview from "@/src/Components/Widgets/FileInputWithPreview";
import styles from "@/src/styles/tutorProfile.module.css";
import homeStyles from "@/src/styles/home.module.css";
import ProgressBar from "@/src/Components/Widgets/ProgressBar";
import {listenToDocument, updateCollection, uploadImage} from "@/src/Firebase/firebase.utils";
import {UserContext} from "@/src/Contexts/UserContext";
import {calculateTutorRegProgress} from "@/src/Utils/helpers";
import NotificationModal from "@/src/Components/Modals/NotificationModal";
import {AppNotify} from "@/src/Utils/AppNotify";
import Spinner from "@/src/Components/Widgets/Spinner";
import {ACHIEVEMENTS, PROFILE_FIELDS, RANK_DEFINITIONS, RANK_SLIDER_SETTINGS} from "@/src/Constants/profileConstants";
import {DisplayRank, TabActivePros} from "@/src/Types";
import { useTranslation } from "@/src/Utils/i18n";

const TutorProfileContent: React.FC<TabActivePros> = ({
                                                          setActiveTab
                                                      }) => {
    const hasShown = useRef(false);
    const router = useRouter();
    const searchParams = useSearchParams();
    const {user: contextUser, isLoggingOut} = useContext(UserContext);
    const sliderRef = React.useRef<Slider | null>(null);
    const isInitialMount = useRef(true);
    const unsubscribeUserDoc = useRef<(() => void) | null>(null);
    const {t,locale} =useTranslation();

    const [statistics, setStatistics] = useState<Record<string, string>>({});
    const [userAchievements, setUserAchievements] = useState<Record<string, string>>({});
    // eslint-disable-next-line no-unused-vars
    const [previousAchievements, setPreviousAchievements] = useState<Record<string, string>>({});
    const [profileImage, setProfileImage] = useState<any>(null);
    const [viewAllAchievements, setViewAllAchievements] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [displayedRanks, setDisplayedRanks] = useState<DisplayRank[]>([]);
    const [activeMonthlyRank, setActiveMonthlyRank] = useState<DisplayRank | null>(null);
    const [currentMonthlyAnswers, setCurrentMonthlyAnswers] = useState<number>(0);
    const [registrationProgress, setRegistrationProgress] = useState<number>(0);
    const [seenAchievementsState, setSeenAchievementsState] = useState<Record<string, boolean>>({});
    const [showAchievementModal, setShowAchievementModal] = useState(false);
    const [unlockedAchievementDetails, setUnlockedAchievementDetails] = useState<{
        id: string,
        icon: string,
        title: string
    } | null>(null);

    const RankResetDate = () => {
        const now = new Date();
        const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        const formattedDate = lastDayOfMonth.toLocaleDateString('en-US', {month: 'long', day: 'numeric'});
        return <span className={styles.rankResets}>Resets: {formattedDate}</span>;
    };

    const handleGotoAnswer = () => {
        router.push(`/${locale}/questions`);
        // setActiveTab && setActiveTab(0);
    };
    const handleUpdateProfileImage = async (imgFile) => {
        if (!imgFile || isUploading || !contextUser?.id) return;

        try {
            setIsUploading(true);

            const localPreviewUrl = URL.createObjectURL(imgFile);
            setProfileImage(localPreviewUrl);

            // upload to firebase storage
            const uploadResp = await uploadImage(imgFile, contextUser.id, true);

            if (uploadResp.status && uploadResp.downloadUrl) {
                // clean local preview
                URL.revokeObjectURL(localPreviewUrl);

                // update image via function
                const response = await axios.post(
                    process.env.NEXT_PUBLIC_UPDATE_PROFILE_IMAGE as string,
                    {
                        userId: contextUser.id,
                        profileImageUrl: uploadResp.downloadUrl
                    }
                );

                if (response.data.status) {
                    console.log("profile image updated");
                } else {
                    console.error("Failed to update via cloud function");
                    setProfileImage(contextUser?.imgUrl || '/icons/profile/avatar.png');
                }
            } else {
                URL.revokeObjectURL(localPreviewUrl);
                setProfileImage(contextUser?.imgUrl || '/icons/profile/avatar.png');
                console.error("Failed to upload image:", uploadResp);
            }
        } catch (error) {
            console.error("Error updating profile image:", error);
            setProfileImage(contextUser?.imgUrl || '/icons/profile/avatar.png');
        } finally {
            setIsUploading(false);
        }
    };

    const formatJoinDate = () => {
        try {
            if (contextUser?.createdAt) {
                const timestamp = typeof contextUser.createdAt === 'object' && contextUser.createdAt.toDate ? contextUser.createdAt.toDate() : new Date(contextUser.createdAt);
                return `Joined ${timestamp.toLocaleDateString('en-US', {month: 'long', year: 'numeric'})}`;
            }
            const currentDate = new Date();
            return `Joined ${currentDate.toLocaleDateString('en-US', {month: 'long', year: 'numeric'})}`;
        } catch (error) {
            console.error('Error formatting join date:', error);
            const currentDate = new Date();
            return `Joined ${currentDate.toLocaleDateString('en-US', {month: 'long', year: 'numeric'})}`;
        }
    };

    const getOptionLabel = (parentKey: string, value: string) => {
        const field: any = PROFILE_FIELDS.find((f: any) => f.key === parentKey && f.options);
        if (!field) return null;

        const option = field.options.find(opt => opt.value === value);
        return option ? option.label : null;
    };

    const getRankDefinitionByValue = (parentKey: string, value: string) => {
        const label = getOptionLabel(parentKey, value);
        if (!label) return null;

        return RANK_DEFINITIONS.find(rank => rank.name === label);
    };

    const updateUserDerivedState = (userData: any) => {
        if (!userData) return;

        let monthlyAnswers: number;
        if (userData.isApplicant) {
            monthlyAnswers = Number(userData.applicantQuestionAnswered) || 0;
        } else {
            const tutorAnswers = Number(userData.answers) || 0;
            const applicantAnswers = Number(userData.applicantQuestionAnswered) || 0;
            monthlyAnswers = tutorAnswers + applicantAnswers;
        }
        setCurrentMonthlyAnswers(monthlyAnswers);

        // Update other states
        setStatistics(userData.statistics || {});
        setUserAchievements(userData.achievements || {});
        setSeenAchievementsState(userData.seenAchievements || {});
        setProfileImage(userData.profileImageUrl || userData.imgUrl || '/icons/profile/avatar.png');

        // Calculate registration progress
        const progress = calculateTutorRegProgress({
            firstName: userData.firstName || "",
            lastName: userData.lastName || "",
            phoneNumber: userData.phoneNumber || "",
            dateOfBirth: userData.dateOfBirth || "",
            educationLevel: userData.educationLevel || "",
            tutorExperience: userData.tutorExperience || "",
            country: userData.country || "",
            certificateNumber: userData.certificateNumber || "",
        });
        setRegistrationProgress(progress);

        // Calculate ranks
        let currentActiveRank: DisplayRank | null = null;
        const userMonthlyRankValue = userData.statistics?.monthlyRank;
        const hasSavedRank = !!userMonthlyRankValue && userMonthlyRankValue !== "0";

        const savedRankDef = hasSavedRank ?
            (getRankDefinitionByValue("monthlyRank", userMonthlyRankValue) as DisplayRank | undefined) :
            undefined;
        const savedRankIndex = savedRankDef ?
            RANK_DEFINITIONS.findIndex(r => r.name === savedRankDef.name) :
            -1;

        const newDisplayedRanks = RANK_DEFINITIONS.map((rankDef, idx) => {
            let isUnlocked: boolean;
            let isActive = false;

            if (hasSavedRank && savedRankDef) {
                isUnlocked = idx <= savedRankIndex;
                if (rankDef.name === savedRankDef.name) {
                    isActive = true;
                    currentActiveRank = {...rankDef, locked: false, active: true};
                }
            } else {
                isUnlocked = monthlyAnswers >= rankDef.minAnswers;
                if (
                    isUnlocked &&
                    monthlyAnswers < (rankDef.nextRankAnswers ?? Infinity) &&
                    (!currentActiveRank || rankDef.minAnswers > currentActiveRank.minAnswers)
                ) {
                    isActive = true;
                    currentActiveRank = {...rankDef, locked: false, active: true};
                }
            }

            return {...rankDef, locked: !isUnlocked, active: isActive};
        });

        setDisplayedRanks(newDisplayedRanks);
        setActiveMonthlyRank(currentActiveRank);
    };

    useEffect(() => {
        if (hasShown.current) return;
        const u = searchParams.get('u');
        if (u === 'valid' && !isLoggingOut) {
            AppNotify("Congratulations - you have been approved!", "success");
            hasShown.current = true;
        }
    }, [searchParams, isLoggingOut]);

    // realtime updates
    useEffect(() => {
        if (contextUser?.id) {
            if (unsubscribeUserDoc.current) {
                unsubscribeUserDoc.current();
            }
            unsubscribeUserDoc.current = listenToDocument(contextUser.id, "users", (docData) => {
                if (docData.status && docData.exists) {
                    const freshUserData = {id: contextUser.id, ...docData.data};
                    setPreviousAchievements(userAchievements);
                    updateUserDerivedState(freshUserData);
                } else if (!docData.exists) {
                    console.warn("User document not found for ID:", contextUser.id);
                } else if (!docData.status) {
                    console.error("Error listening to user document:", contextUser.id);
                }
            });
        }
        return () => {
            if (unsubscribeUserDoc.current) {
                unsubscribeUserDoc.current();
                unsubscribeUserDoc.current = null;
            }
        };
    }, [contextUser?.id]);

    // init states from user context
    useEffect(() => {
        if (contextUser) {
            if (isInitialMount.current && contextUser.imgUrl) {
                setProfileImage(contextUser.imgUrl);
            }
            if (isInitialMount.current) {
                isInitialMount.current = false;
            }

            const initialUserAchievements = contextUser.achievements || {};
            setPreviousAchievements(initialUserAchievements);
            updateUserDerivedState(contextUser);
        }
    }, [contextUser, isUploading]);

    // check for new achievements
    useEffect(() => {
        if (!contextUser?.id || Object.keys(userAchievements).length === 0) return;

        for (const achDef of ACHIEVEMENTS) {
            const achKey = achDef.id || `achievement${ACHIEVEMENTS.indexOf(achDef) + 1}`;
            const currentAdminStatus = userAchievements[achKey];

            let isNowEffectivelyUnlocked = false;
            if (achDef.isRegistration) {
                isNowEffectivelyUnlocked = registrationProgress === 100 || currentAdminStatus === 'Unlocked';
            } else {
                isNowEffectivelyUnlocked = currentAdminStatus === 'Unlocked';
            }

            if (isNowEffectivelyUnlocked && !seenAchievementsState[achKey]) {
                setUnlockedAchievementDetails({id: achKey, icon: achDef.icon, title: achDef.title});
                setShowAchievementModal(true);
                break;
            }
        }
    }, [userAchievements, registrationProgress, seenAchievementsState, contextUser?.id]);

    const markAchievementAsSeen = async (achievementId: string) => {
        if (!contextUser?.id || !achievementId) return;

        const updatedSeenAchievements = {
            ...(seenAchievementsState || {}),
            [achievementId]: true,
        };
        setSeenAchievementsState(updatedSeenAchievements);

        try {
            await updateCollection(
                "users",
                contextUser.id,
                {seenAchievements: updatedSeenAchievements},
                true
            );
        } catch (error) {
            console.error("error marking achievement as seen:", error);
        }
    };

    const handleModalConfirmOrClose = () => {
        setShowAchievementModal(false);
        if (unlockedAchievementDetails?.id) {
            markAchievementAsSeen(unlockedAchievementDetails.id);
        }
        setUnlockedAchievementDetails(null);
    };

    const monthlyRankProgressMax = activeMonthlyRank?.nextRankAnswers || activeMonthlyRank?.minAnswers || 500;

    if (!contextUser.id) {
        return (
            <Spinner/>
        )
    }

    return (
        <div className={styles.profilePage}>
            <div className={'mainContainer'}>
                <div className={styles.pageSections}>

                    <div className={styles.profileAvatarBox}>
                        <FileInputWithPreview
                            onChange={handleUpdateProfileImage}
                            profilePage={true}
                            userImg={profileImage || contextUser?.imgUrl || '/icons/profile/avatar.png'}
                            disabled={isUploading}
                        />
                        <div className={styles.profileInfo}>
                            <div className={styles.name}>
                                {contextUser?.firstName && contextUser?.lastName ? `${contextUser.firstName} ${contextUser.lastName}` : contextUser?.email || 'New Tutor'}
                            </div>
                            <div className={styles.joined}>{formatJoinDate()}</div>
                        </div>
                    </div>

                    <div className={styles.statsAndChallenges}>
                        <div className={styles.statisticsBox}>
                            <h2 className={styles.sectionTitle}>{t("common.statistics")}</h2>
                            <div className={styles.statistics}>
                                <div className={styles.statBox}>
                                    <img src={'/icons/profile/right-icon.svg'} alt={'right-icon'}/>
                                    <div className={styles.statBoxInfo}>
                                        <span>{currentMonthlyAnswers ? "100%" : '0%'}</span>
                                        <small>{t("common.acceptance_rate")}</small>
                                    </div>
                                </div>
                                <div className={styles.statBox}>
                                    <img src={'/icons/profile/user-icon.svg'} alt={'user-icon'}/>
                                    <div className={styles.statBoxInfo}>
                                        <span>{currentMonthlyAnswers || '0'}</span>
                                        <small>{t("common.students_helped")}</small>
                                    </div>
                                </div>
                                <div className={styles.statBox}>
                                    <img src={'/icons/profile/math-icon.svg'} alt={'math-icon'}/>
                                    <div className={styles.statBoxInfo}>
                                        <span>{currentMonthlyAnswers ? "Math" : 'N/A'}</span>
                                        <small>{t("common.top_subject")}</small>
                                    </div>
                                </div>
                                <div className={styles.statBox}>
                                    <img src={'/icons/profile/fire-icon.svg'} alt={'fire-icon'}/>
                                    <div className={styles.statBoxInfo}>
                                        <span>{statistics.dailyStreak || '0'}</span>
                                        <small>{t("common.daily_streak")}</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className={styles.statDivider}></div>

                        <div className={styles.challengesBox}>
                            <h2 className={styles.sectionTitle}>{t("common.daily_challenges")}</h2>
                            <div className={styles.dailyChallenges}>
                                <div className={styles.challengeBox}>
                                    <span>{t("common.login_to_your_account")}</span>
                                    <div className={styles.checkmark}><img src={'/icons/green-right-icon.svg'}
                                                                           alt={'payment-right'}/></div>
                                </div>
                                <div className={styles.challengeBox}>
                                    <span>{t("common.answer_50_questions")}</span>
                                    <PrimaryButton label={t('go')} onClick={handleGotoAnswer} size={'blueBtn'}/>
                                </div>
                            </div>
                        </div>
                    </div>

                    {activeMonthlyRank && (
                        <div className={styles.monthlyRank}>
                            <div className={styles.rankHeader}>
                                <h2 className={styles.sectionTitle}>{t("common.monthly_rank")}</h2>
                                <RankResetDate/>
                            </div>
                            <div className={styles.rankBody}>
                                <div className={styles.rankBodyImg}>
                                    <img src={activeMonthlyRank.icon} alt={`${activeMonthlyRank.name} Badge`}
                                         className={styles.rankBadge}/>
                                </div>
                                <div className={styles.rankBodyContent}>
                                    <div className={styles.rankBodyInfo}>
                                        <div className={styles.rankTitle}>
                                            
                                            {t('common.' + activeMonthlyRank.name)}
                                            
                                            </div>
                                        <div
                                            className={styles.rankSubtitle}>{t('common.' + activeMonthlyRank.name).replace('Fewer than', 'Reach').replace('this month', '')}</div>
                                    </div>
                                    <div className={styles.rankBodyProgress}>
                                        <ProgressBar
                                            value={currentMonthlyAnswers}
                                            max={monthlyRankProgressMax}
                                        />
                                        <span className={styles.progressText}>
    <span>{currentMonthlyAnswers || 0}</span> / {monthlyRankProgressMax} {t("common.questions_answered")}
</span>
                                    </div>
                                    <div className={styles.rankBodyFoot}>
                                        <PrimaryButton onClick={handleGotoAnswer} label={t('TutorQuestions.answer_questions_title')}
                                                       size={'greenBtn'}/>
                                        <div className={styles.rankNote}>{t('common.keep_answer')}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    <div className={styles.ranksSection}>
                        <h2 className={styles.sectionTitle}>{t('common.ranks')}</h2>
                        <div className={styles.ranks}>
                            <Slider ref={sliderRef} {...RANK_SLIDER_SETTINGS} className={`tutorCardSlider`}>
                                {displayedRanks.map((rank, index) => (
                                    <div key={index}
                                         className={`${styles.rankCard} ${rank.active ? styles.active : ''}`}>
                                        {rank.locked &&
                                            <div className={styles.locked}><img src={'/icons/profile/lock-icon.svg'}
                                                                                alt={'Lock Icon'}/></div>}
                                        {rank.icon && <img src={rank.icon} alt={rank.name}/>}
                                        <div className={styles.rankInfo}>
                                            <div className={styles.rankName}>{t('common.' + rank.name)}</div>
                                            <div className={styles.rankDescription}>{t('common.' + rank.description)}</div>
                                        </div>
                                        <div className={styles.rankRewards}>
                                            <div className={styles.rewardTitle}>{t('common.rewards')}</div>
                                            <div className={styles.rewardName}> {t('common.' + rank.rewards)}</div>
                                            {  rank.bonus && <div className={styles.rewardBonus}>{t('common.' + rank.bonus)}</div>}
                                        </div>
                                    </div>
                                ))}
                            </Slider>
                            <div className={`${homeStyles.tutorCardNavBottom} rankSliderNav`}>
                                <a onClick={() => (sliderRef.current as any)?.slickPrev()}
                                   className={homeStyles.tutorCardArrow}>
                                    <img src={'/icons/home/<USER>'} alt={'slide-prev-icon'}/>
                                </a>
                                <a onClick={() => (sliderRef.current as any)?.slickNext()}
                                   className={homeStyles.tutorCardArrow}>
                                    <img src={'/icons/home/<USER>'} alt={'slide-next-icon'}/>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div className={styles.achievementsSection}>
                        <h2 className={styles.sectionTitle}>{t("common.achievements")}</h2>
                        <div className={styles.achievements}>
                            {(viewAllAchievements ? ACHIEVEMENTS : ACHIEVEMENTS.slice(0, 6)).map((achievementDefinition) => {
                                const achievementKey = achievementDefinition.id || `achievement${ACHIEVEMENTS.indexOf(achievementDefinition) + 1}`;
                                const adminStatus = userAchievements[achievementKey];
                                let currentProgress = 0;
                                let isEffectivelyUnlocked = false;

                                if (achievementDefinition.isRegistration) {
                                    currentProgress = registrationProgress;
                                    isEffectivelyUnlocked = registrationProgress === 100 || adminStatus === 'Unlocked';
                                } else {
                                    isEffectivelyUnlocked = adminStatus === 'Unlocked';
                                    currentProgress = isEffectivelyUnlocked ? 100 : 0;
                                }

                                return (
                                    <div key={achievementKey}
                                         className={`${styles.statBox} ${styles.achievement} ${!isEffectivelyUnlocked ? styles.lockedAchievement : ''}`}>
                                        <img src={achievementDefinition.icon} alt={achievementDefinition.title}/>
                                        <div className={styles.statBoxInfo}>
                                            <span> {t('common.' + achievementDefinition.title)}</span>
                                          
                                          
                                           {  achievementDefinition.description && <small>{t('common.' + achievementDefinition.description)}</small>}
                                          
                                          
                                           
                                            <ProgressBar
                                                value={currentProgress}
                                                max={100}
                                            />
                                        </div>
                                        {!isEffectivelyUnlocked && <div className={styles.achievementLockIcon}><img
                                            src={'/icons/profile/lock-icon.svg'} alt={'Lock Icon'}/></div>}
                                    </div>
                                );
                            })}
                        </div>
                        {ACHIEVEMENTS.length > 6 && (
                            <div className={styles.viewAll}
                                 onClick={() => setViewAllAchievements(!viewAllAchievements)}>{viewAllAchievements ? t('common.view_less') : t('common.view_all')}</div>
                        )}
                    </div>
                </div>
            </div>

            {unlockedAchievementDetails && (
                <NotificationModal
                    open={showAchievementModal}
                    handleClose={handleModalConfirmOrClose}
                    icon={unlockedAchievementDetails.icon}
                    title={t(`common.congratulations`)}
                    content={`You unlocked: <strong>${t('common.' + unlockedAchievementDetails.title)}</strong><br/>You also get a new reward.`}
                    actionLabel="Ok"
                    onConfirm={handleModalConfirmOrClose}
                />
            )}
        </div>
    );
}

const TutorProfile: React.FC<TabActivePros> = (props) => {
    return (
        <Suspense fallback={<div>Loading...</div>}>
            <TutorProfileContent {...props} />
        </Suspense>
    );
}

export default TutorProfile;