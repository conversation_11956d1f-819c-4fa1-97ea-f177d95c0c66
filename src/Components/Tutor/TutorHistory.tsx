"use client";
import React, {useContext, useEffect, useLayoutEffect, useState, useCallback} from "react";
import moment from "moment";
import {UserContext} from "@/src/Contexts/UserContext";
import styles from "@/src/styles/home.module.css";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import {useRouter} from "next/navigation";
import Spinner from "@/src/Components/Widgets/Spinner";
import {Accordion, AccordionSummary} from "@mui/material";
import {getApplicantQuestionsFunc, getDocument, queryData} from "@/src/Firebase/firebase.utils";
import NotificationModal from "@/src/Components/Modals/NotificationModal";
import {isArray, zeroPadId} from "../../Utils/helpers";
import {useImageModal} from "@/src/Contexts/ImageModalContext";
import TableCustomPagination from "@/src/Components/Table/TableCustomPagination";
import { useTranslation } from "@/src/Utils/i18n";
import { TutorQuestion } from "@/src/Types";

const Questions = () => {
    const { openImage } = useImageModal();
    const router = useRouter();
    const { user, langSwitch, setLangSwitch } = useContext(UserContext);
    const { t } = useTranslation();

    // States
    const [questions, setQuestions] = useState<any[]>([]);
    const [fetchingQuestions, setFetchingQuestions] = useState(false);
    const [answerImage, setAnswerImage] = useState<any>(null);
    const [expanded, setExpanded] = useState<string | null>(null);
    const [fetchingAnswer, setFetchingAnswer] = useState(false);
    const [open, setOpen] = useState(false);
    const [rowsPerPage, setRowsPerPage] = useState(5);
    const [page, setPage] = React.useState(0);

    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    const fetchQuestions = useCallback(async () => {
        setFetchingQuestions(true);

        try {
            const collection = user.isApplicant ? "applicant_answers" : "tutor_answers";
            const resp: any = await queryData(collection, "answeredBy", user.id);
            let questions: any[] = [];

            if (resp.status && resp.fullData.length > 0) {
                let fullData = resp.fullData.sort((a, b) => b.date - a.date);

                // fetch img for each question 
                const questionsWithImages = await Promise.all(
                    fullData.map(async (question) => {
                        let questionImageUrl = null;

                        // Get question image
                        if (question.questionId) {
                            const userQuestionResp: any = await getDocument(question.questionId, "users_questions");
                            questionImageUrl = userQuestionResp?.data?.imageUrl || null;
                        }

                        // Get answer details from tutor_answers
                        let answerDate = question.date;
                        let answerImageUrl = null;

                        try {
                            const answerResp: any = await queryData("tutor_answers", "questionId", question.questionId);
                            if (answerResp.status && answerResp.fullData.length > 0) {
                                const answerData = answerResp.fullData[0];

                                if (answerData.date) {
                                    if (answerData.date.toDate) {
                                        answerDate = answerData.date.toDate().getTime();
                                    } else if (answerData.date.seconds) {
                                        answerDate = answerData.date.seconds * 1000;
                                    } else if (typeof answerData.date === 'number') {
                                        answerDate = answerData.date;
                                    }
                                }

                                answerImageUrl = answerData.imgUrl || null;
                            }
                        } catch (error) {
                            console.error("Error fetching answer details for question:", question.questionId, error);
                        }

                        return {
                            ...question,
                            imageUrl: questionImageUrl,
                            answerDate,
                            answerImageUrl,
                        };
                    })
                );

                questions = questionsWithImages;
            }

            // applicant questions
            if (user.applicantQuestionAnswered < 2) {
                const applicantResp: any = await getApplicantQuestionsFunc(user.id as string);
                if (applicantResp.status && applicantResp.fullData.length > 0) {
                    const filteredQuestions = questions.filter(q => q.answeredBy?.includes(user.id));
                    const remainingCount = 2 - user.applicantQuestionAnswered;
                    const availableQuestions = filteredQuestions.slice(0, remainingCount);
                    questions = [...questions, ...availableQuestions];
                }
            }

            setQuestions(questions);
        } catch (error) {
            console.error("Error fetching questions:", error);
        } finally {
            setFetchingQuestions(false);
        }
    }, [user]);

    useEffect(() => {
        if (user.id !== "") {
            fetchQuestions();
        }
    }, [user, fetchQuestions]);

    // calc current question set
    const totalQuestions = questions.length;

    useLayoutEffect(() => {
        const defaultSwitchDone = localStorage.getItem("HMWK_DEFAULT_SWITCH_DONE");
        if (navigator.language === "es" && defaultSwitchDone !== "true") {
            localStorage.setItem("HMWK_DEFAULT_SWITCH_DONE", "true");
            router.push("/es");
        }
    });

    let id: any = "";
    if (typeof window !== "undefined") {
        id = localStorage.getItem("HMWK_LANGUAGE");
        if ((id === "" || !id || id === "en") && langSwitch) {
            setLangSwitch(false);
        }
    }

    const handleOpenAnswer = async (question) => {
        if (!question.answer) {
            return;
        }

        // Use the pre-fetched answer image if available
        if (question.answerImageUrl) {
            setAnswerImage(question.answerImageUrl);
        } else {
            setFetchingAnswer(true);
            const resp: any = await queryData("tutor_answers", "questionId", question.questionId);
            setFetchingAnswer(false);

            if (resp.status && resp.fullData.length > 0) {
                setAnswerImage(resp.fullData[0].imgUrl || null);
            }
        }
    };

    return (
        <div className={id === "" || !id || id === "en" ? styles.englishPage : ""} style={{ padding: '0 15px' }}>
            <div className={questionStyles.addQuestionTitle}>
                <h1>{t('tutor_history.title')}</h1>
                <div>{t('tutor_history.subtitle')}</div>
            </div>

            {langSwitch || fetchingQuestions ? (<Spinner />) : questions.length > 0 ? (
                <>
                    <div className={questionStyles.addQuestionBox} style={{ height: "auto" }}>
                        <div className={`${questionStyles.studentRoundBox}`}>
                            <div className={questionStyles.questions}>
                                {questions && questions.length ? questions.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((question: TutorQuestion) => {

                                    let quesDate = '';
                                    const dateToUse = question.answerDate || question.date;

                                    if (dateToUse && !isNaN(Number(dateToUse))) {
                                        const timestamp = Number(dateToUse);
                                        // valid timestamps
                                        if (timestamp > 1000000000) {
                                            quesDate = moment(timestamp).format("MM/DD/YYYY");
                                        }
                                    }

                                    return (
                                        <Accordion
                                            key={question.docId}
                                            expanded={expanded === question.docId}
                                            sx={{
                                                maxWidth: "860px",
                                                width: "100%",
                                                border: "1px solid rgba(229, 229, 229, 1) !important",
                                                borderRadius: "16px !important",
                                                boxShadow: "none !important",
                                                "&::before": {
                                                    display: "none",
                                                },
                                            }}
                                        >
                                            <AccordionSummary
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                }}
                                                aria-controls="panel1-content"
                                                id="panel1-header"
                                                sx={{
                                                    padding: {xs: "10px", md: "20px"},
                                                    "& .MuiAccordionSummary-content": {
                                                        margin: "0 !important",
                                                    },
                                                }}
                                            >
                                                <div className={questionStyles.accordionContainer}>
                                                    <div className={questionStyles.imgQuestionContainer}>
                                                        <div className={questionStyles.accQuesDataBox}>
                                                            <img
                                                                alt={'No Image'}
                                                                className={questionStyles.accQuesImage}
                                                                src={question.imageUrl || "/icons/ques-no-image.svg"}
                                                                height={104}
                                                                width={104}
                                                                onClick={() => openImage(question.imageUrl || "/icons/ques-no-image.svg")}
                                                            />

                                                            <div className={questionStyles.accQuesHeader}>
                                                                <div className={questionStyles.accQuesTop}>
                                                                    <div className={questionStyles.accQuesTitle}>
                                                                        <img src={'/icons/comment-icon.svg'}
                                                                             alt={'comment icon'} />
                                                                        <div
                                                                            className={questionStyles.accQuesText}>{question.question && isArray(question.question) ? (question.question as string[]).map((p, index) => (
                                                                            <p key={index}>{p}</p>)) : question.question}</div>
                                                                    </div>

                                                                    <img
                                                                        width={32}
                                                                        src="/icons/ques-toggle-icon.svg"
                                                                        alt="Expand Icon"
                                                                        className={`${questionStyles.accExpandIcon} ${expanded === question.docId ? questionStyles.accExpanded : ''}`}
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            handleOpenAnswer(question);
                                                                            setExpanded((prev) => (prev === question.docId ? null : question.docId));
                                                                        }}
                                                                    />
                                                                </div>

                                                                <div className={questionStyles.questionInfo}>
                                                                    <div className={questionStyles.accQuesMetaRow}>
                                                                        <div
                                                                            className={questionStyles.accQuesId}>{t('tutor_history.question_id')} <a>#{question.questionCustomId && zeroPadId(question.questionCustomId)}</a>
                                                                        </div>
                                                                        <span className={questionStyles.questionMeta}>
                                                                            <div>{user && user.type === 'user' ? t('tutor_history.asked') : t('tutor_history.answered')}</div>
                                                                            <div>{quesDate}</div>
                                                                            <div
                                                                                className={questionStyles.questionFeedback}
                                                                                onClick={() => {
                                                                                    handleOpen()
                                                                                }}>{t('tutor_history.feedback')}</div>
                                                                        </span>
                                                                    </div>
                                                                </div>

                                                                <div className={questionStyles.accQuesAnswerBox}>
                                                                    <div
                                                                        className={questionStyles.accQuesAnsRow}
                                                                        onClick={(e) => {
                                                                            e.stopPropagation();
                                                                            handleOpenAnswer(question);
                                                                            setExpanded((prev) => (prev === question.docId ? null : question.docId));
                                                                        }}
                                                                    >
                                                                        <img src={'/icons/right-check-icon.svg'}
                                                                             alt={'right-check-icon'} />
                                                                        <span>{t('tutor_history.answer')}</span>
                                                                        <img
                                                                            className={`${expanded === question.docId ? questionStyles.accExpanded : ''}`}
                                                                            src={'/icons/answer-toggle.svg'}
                                                                            alt={'arrow-down'} />
                                                                    </div>
                                                                    {expanded === question.docId ?
                                                                        <div>
                                                                            {fetchingAnswer ? (
                                                                                <h4
                                                                                    style={{
                                                                                        fontWeight: 400,
                                                                                        margin: 0,
                                                                                        color: "#999",
                                                                                        fontSize: "15px",
                                                                                        marginTop: '10px',
                                                                                        paddingLeft: '26px',
                                                                                        paddingBottom: '5px'
                                                                                    }}
                                                                                >
                                                                                    {t('tutor_history.loading')}
                                                                                </h4>
                                                                            ) : (
                                                                                <>
                                                                                    <div
                                                                                        className={questionStyles.accAnswerContent}>
                                                                                        <div
                                                                                            className={questionStyles.accAnswerData}>
                                                                                            {(answerImage || question.answerImageUrl) && (
                                                                                                <div
                                                                                                    className={questionStyles.accAnswerImage}>
                                                                                                    <img
                                                                                                        alt={'Answer Image'}
                                                                                                        src={answerImage || question.answerImageUrl}
                                                                                                        height={76}
                                                                                                        width={76}
                                                                                                        onClick={() => openImage(answerImage || question.answerImageUrl)}
                                                                                                    />
                                                                                                </div>
                                                                                            )}
                                                                                            <div
                                                                                                className={questionStyles.accAnswerText}>
                                                                                                {question.answer && question.answer.length && isArray(question.answer) ? question.answer.map((p, index) => (
                                                                                                    <p key={index}>{p}</p>)) : question.answer}
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </>
                                                                            )}
                                                                        </div> : ''}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </AccordionSummary>
                                        </Accordion>
                                    );
                                }) : ''}
                            </div>

                            <TableCustomPagination
                                showFullBar={false}
                                filteredRowsLength={totalQuestions}
                                page={page}
                                rowsPerPage={rowsPerPage}
                                setPage={setPage}
                                setRowsPerPage={setRowsPerPage}
                            />
                        </div>
                    </div>
                </>
            ) : (
                <div className={questionStyles.addQuestionBox}>
                    <h2>{t('tutor_history.no_questions')}</h2>
                </div>
            )}

            <NotificationModal
                open={open}
                handleClose={handleClose}
                icon={"/icons/flag-icon.svg"}
                title={t('tutor_history.notify_title')}
                content={t('tutor_history.notify_content')}
            />
        </div>
    );
};

export default Questions;