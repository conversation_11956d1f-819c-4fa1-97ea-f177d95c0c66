"use client";
import React, {useContext, useEffect, useLayoutEffect, useState} from "react";
import {ToggleButton, ToggleButtonGroup} from "@mui/material";
import {UserContext} from "@/src/Contexts/UserContext";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import {useRouter} from "next/navigation";
import Spinner from "@/src/Components/Widgets/Spinner";
import InputTextField from "@/src/Components/Form/InputTextField";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import styles from "@/src/styles/tutorBalance.module.css";
import {AppNotify} from "@/src/Utils/AppNotify";
import {addDocInCollection, deleteDocument, queryData, updateCollection,} from "@/src/Firebase/firebase.utils";
import CustomSelectField from "@/src/Components/Form/CustomSelectField";
import BorderButton from "@/src/Components/Buttons/BorderButton";
import ConfirmModal from "../Modals/ConfirmModal";
import { useTranslation } from "next-i18next";

const TutorPayout = () => {
    const { t } = useTranslation();
    const {user, langSwitch, setLangSwitch} = useContext(UserContext);

    // States
    const [withdrawing, setWithdrawing] = useState(false);
    const [initialData, setInitialData] = useState<any>({});
    const [format, setFormat] = useState("iban");
    const [iban, setIban] = useState("");
    const [accountNumber, setAccountNumber] = useState("");
    const [routingNumber, setRoutingNumber] = useState("");
    const [accountType, setAccountType] = useState("CHECKING");
    const [state, setState] = useState("");
    const [sortCode, setSortCode] = useState("");
    const [city, setCity] = useState("");
    const [countryCode, setCountryCode] = useState("");
    const [postCode, setPostCode] = useState("");
    const [firstLine, setFirstLine] = useState("");
    const [disconnectStart, setDisconnectStart] = useState(false);
    const [withdrawalMethodExists, setWithdrawalMethodExists] = useState(false);
    const [questionStatus, setQuestionStatus] = useState("checking");
    const router = useRouter();

    useEffect(() => {
        getWithdrawalMethod();
    }, []);

    useLayoutEffect(() => {
        const defaultSwitchDone = localStorage.getItem("HMWK_DEFAULT_SWITCH_DONE");
        if (navigator.language === "es" && defaultSwitchDone !== "true") {
            localStorage.setItem("HMWK_DEFAULT_SWITCH_DONE", "true");
            router.push("/es");
        }
    });

    useEffect(() => {
        if (withdrawalMethodExists && initialData.format) {
            if (format !== initialData.format) {
                clearFields();
            } else {
                getWithdrawalMethod();
            }
        }
    }, [withdrawalMethodExists, format]);

    let id: any = "";
    if (typeof window !== "undefined") {
        id = localStorage.getItem("HMWK_LANGUAGE");
        if ((id === "" || !id || id === "en") && langSwitch) {
            setLangSwitch(false);
        }
    }

    const clearFields = () => {
        setIban("");
        setAccountNumber("");
        setRoutingNumber("");
        setAccountType("CHECKING");
        setState("");
        setSortCode("");
        setCity("");
        setCountryCode("");
        setPostCode("");
        setFirstLine("");
    };

    const getWithdrawalMethod = async () => {
        const response: any = await queryData("withdrawal_methods", "user", user.id);
        if (response.status && response.fullData.length > 0) {
            setWithdrawalMethodExists(true);
            const data: any = response.fullData[0];
            setInitialData(data);
            setIban(data.iban);
            setFormat(data.format);
            setAccountNumber(data.accountNumber);
            setRoutingNumber(data.routingNumber);
            setAccountType(data.accountType);
            setState(data.state);
            setSortCode(data.sortCode);
            setCity(data.city);
            setCountryCode(data.countryCode);
            setPostCode(data.postCode);
            setFirstLine(data.firstLine);
        }
    };

    const checkFields = () => {
        if (format === "iban" && iban === "") {
            AppNotify(t("tutor_payout.notify_iban_required"), "error");
            return false;
        }

        if ((format === "uk" || format === "us") && accountNumber === "") {
            AppNotify(t("tutor_payout.notify_account_number_required"), "error");
            return false;
        }

        if (format === "uk" && sortCode === "") {
            AppNotify(t("tutor_payout.notify_sort_code_required"), "error");
            return false;
        }

        if (format === "us" && routingNumber === "") {
            AppNotify(t("tutor_payout.notify_routing_number_required"), "error");
            return false;
        }

        if (format === "us" && accountType === "") {
            AppNotify(t("tutor_payout.notify_account_type_required"), "error");
            return false;
        }

        if (city === "") {
            AppNotify(t("tutor_payout.notify_city_required"), "error");
            return false;
        }

        if (countryCode === "") {
            AppNotify(t("tutor_payout.notify_country_code_required"), "error");
            return false;
        }

        if (format === "us" && state === "") {
            AppNotify(t("tutor_payout.notify_state_required"), "error");
            return false;
        }

        if (postCode === "") {
            AppNotify(t("tutor_payout.notify_postcode_required"), "error");
            return false;
        }

        if (firstLine === "") {
            AppNotify(t("tutor_payout.notify_first_line_required"), "error");
            return false;
        }

        return true;
    };

    const addWithdrawalMethod = async () => {
        if (checkFields()) {
            const data = {
                iban,
                format,
                accountNumber,
                routingNumber,
                accountType,
                sortCode,
                city,
                countryCode,
                state,
                postCode,
                firstLine,
                user: user.id,
            };

            setWithdrawing(true);

            if (withdrawalMethodExists) {
                const response = await updateCollection(
                    "withdrawal_methods",
                    initialData.docId,
                    data,
                    true
                );
                if (response.status) {
                    AppNotify(t("tutor_payout.notify_withdrawal_updated"), "success");
                }
            } else {
                const response = await addDocInCollection(
                    "withdrawal_methods",
                    data,
                    true
                );
                if (response.status) {
                    setWithdrawalMethodExists(true);
                    AppNotify(t("tutor_payout.notify_withdrawal_added"), "success");
                }
            }
            setWithdrawing(false);
            router.push("/revenue");
        }
    };

    const handleDisconnectStart = () => {
        setDisconnectStart(true);
    };

    const handleDisconnect = async () => {
        setWithdrawing(true);
        const resp = await deleteDocument(
            "withdrawal_methods",
            initialData.docId,
            true
        );
        if (resp.status) {
            setWithdrawing(false);
            AppNotify(t("tutor_payout.notify_withdrawal_disconnected"), "success");
            router.push("/revenue");
        }
    };

    return (
        <div className={styles.container}>
            <div>
                <div className={questionStyles.addQuestionTitle}>
                    <h1>{t("tutor_payout.title")}</h1>
                    <div>{t("tutor_payout.subtitle")}</div>
                </div>
                {withdrawing ? (
                    <Spinner/>
                ) : (
                    <div className={styles.payoutMethodBox}>
                        <div className={styles.amountWithdrawBox}>
                            <label className={styles.withdrawLabel}>{t("tutor_payout.billing_country")}</label>
                            <CustomSelectField
                                options={[
                                    {label: t("tutor_payout.iban"), value: "iban"},
                                    {label: "United Kingdom", value: "uk"},
                                    {label: "United States", value: "us"},
                                ]}
                                onSelect={setFormat}
                                defaultValue={format}
                                message={t("tutor_payout.billing_country_helper")}
                            />
                        </div>
                        <div className={styles.fieldsRoundBox}>
                            <div className={styles.connectBorderBox}>
                                <div className={styles.wireTransferBox}>
                                    <img src={"/icons/payment-right.svg"} alt={"payment-icon"}/>
                                    <div className={styles.bankDataBox}>
                                        <img src={"/icons/add-bank-icon.svg"} alt={"bank-icon"}/>
                                        <div className={styles.bankDetails}>
                                            <div className={styles.bankLabel}>{t("tutor_payout.wire_transfer")}</div>
                                            <ul className={styles.listTexts}>
                                                <li>{t("tutor_payout.wire_transfer_fee")}</li>
                                                <li>{t("tutor_payout.wire_transfer_time")}</li>
                                            </ul>
                                        </div>
                                    </div>
                                    {format === initialData.format && (
                                        <BorderButton
                                            type={'dark'}
                                            onClick={handleDisconnectStart}
                                            label={t("tutor_payout.disconnect")}
                                            style={{width: '100%', maxWidth: '168px', marginLeft: 'auto'}}/>
                                    )}
                                </div>
                                <div className={styles.connectFieldsBox}>
                                    <div className={styles.connectBoxHead}>
                                        <div className={styles.connectBoxTitle}>{t("tutor_payout.connect_bank")}</div>
                                        <div className={styles.connectBoxText}>{t("tutor_payout.connect_bank_sub")}</div>
                                    </div>
                                    <ToggleButtonGroup
                                        sx={{width: '100% !important'}}
                                        className={questionStyles.toggleButtonGroup}
                                        color="primary"
                                        value={questionStatus}
                                        exclusive
                                        onChange={(e, value) => {
                                            setQuestionStatus(value);
                                        }}
                                        aria-label="Platform"
                                    >
                                        <ToggleButton
                                            value="checking"
                                            classes={{
                                                root: questionStyles.toggleButton,
                                                selected: questionStyles.toggleButtonSelected,
                                            }}
                                        >
                                            {t("tutor_payout.checking")}
                                        </ToggleButton>
                                        <ToggleButton
                                            value="saving"
                                            classes={{
                                                root: questionStyles.toggleButton,
                                                selected: questionStyles.toggleButtonSelected,
                                            }}
                                        >
                                            {t("tutor_payout.saving")}
                                        </ToggleButton>
                                    </ToggleButtonGroup>
                                    {format === "iban" ? (
                                        <div className={styles.amountWithdrawBox}>
                                            <label className={styles.withdrawLabel}>{t("tutor_payout.iban")}</label>
                                            <InputTextField
                                                label=""
                                                onChange={(e) => setIban(e.target.value)}
                                                value={iban}
                                            />
                                            <div className={styles.fieldDescription}>{t("tutor_payout.iban_helper")}</div>
                                        </div>
                                    ) : (
                                        <>
                                            <div className={styles.amountWithdrawBox}>
                                                <label className={styles.withdrawLabel}>
                                                    {t("tutor_payout.account_number")}
                                                </label>
                                                <InputTextField
                                                    label=""
                                                    onChange={(e) => setAccountNumber(e.target.value)}
                                                    value={accountNumber}
                                                />
                                                <div className={styles.fieldDescription}>
                                                    {format === "uk"
                                                        ? t("tutor_payout.account_number_helper_uk")
                                                        : t("tutor_payout.account_number_helper_us")}
                                                </div>
                                            </div>
                                            {format === "uk" && (
                                                <div className={styles.amountWithdrawBox}>
                                                    <label className={styles.withdrawLabel}>
                                                        {t("tutor_payout.sort_code")}
                                                    </label>
                                                    <InputTextField
                                                        label=""
                                                        onChange={(e) => setSortCode(e.target.value)}
                                                        value={sortCode}
                                                    />
                                                </div>
                                            )}
                                            {format === "us" && (
                                                <div className={styles.amountWithdrawBox}>
                                                    <label className={styles.withdrawLabel}>
                                                        {t("tutor_payout.routing_number")}
                                                    </label>
                                                    <InputTextField
                                                        label=""
                                                        onChange={(e) => setRoutingNumber(e.target.value)}
                                                        value={routingNumber}
                                                    />
                                                </div>
                                            )}
                                        </>
                                    )}
                                    {format === "us" && (
                                        <div className={styles.amountWithdrawBox}>
                                            <label className={styles.withdrawLabel}>
                                                {t("tutor_payout.account_type")}
                                            </label>
                                            <CustomSelectField
                                                options={[
                                                    {label: t("tutor_payout.checking"), value: "CHECKING"},
                                                    {label: t("tutor_payout.saving"), value: "SAVINGS"},
                                                ]}
                                                onSelect={setAccountType}
                                                defaultValue={accountType}
                                            />
                                        </div>
                                    )}
                                    <div className={styles.amountWithdrawBox}>
                                        <label className={styles.withdrawLabel}>{t("tutor_payout.city")}</label>
                                        <InputTextField
                                            label=""
                                            onChange={(e) => setCity(e.target.value)}
                                            value={city}
                                        />
                                    </div>
                                    <div className={styles.amountWithdrawBox}>
                                        <label className={styles.withdrawLabel}>{t("tutor_payout.country_code")}</label>
                                        <InputTextField
                                            label=""
                                            onChange={(e) => setCountryCode(e.target.value)}
                                            value={countryCode}
                                        />
                                    </div>
                                    {format === "us" && (
                                        <div className={styles.amountWithdrawBox}>
                                            <label className={styles.withdrawLabel}>{t("tutor_payout.state")}</label>
                                            <InputTextField
                                                label=""
                                                onChange={(e) => setState(e.target.value)}
                                                value={state}
                                            />
                                        </div>
                                    )}
                                    <div className={styles.amountWithdrawBox}>
                                        <label className={styles.withdrawLabel}>
                                            {format === "iban"
                                                ? t("tutor_payout.postal_code")
                                                : format === "us"
                                                    ? t("tutor_payout.zipcode")
                                                    : t("tutor_payout.postcode")}
                                        </label>
                                        <InputTextField
                                            label=""
                                            id="outlined-basic"
                                            onChange={(e) => setPostCode(e.target.value)}
                                            value={postCode}
                                        />
                                    </div>
                                    <div className={styles.amountWithdrawBox}>
                                        <label className={styles.withdrawLabel}>
                                            {t("tutor_payout.first_line")}
                                        </label>
                                        <InputTextField
                                            label=""
                                            id="outlined-basic"
                                            onChange={(e) => setFirstLine(e.target.value)}
                                            value={firstLine}
                                        />
                                    </div>
                                    <div className={styles.fieldAgreeMsg}>
                                        {t("tutor_payout.agree_msg")}
                                    </div>
                                </div>
                                <div className={styles.btnContainer}>
                                    <BorderButton
                                        type={"dark"}
                                        label={t("tutor_payout.cancel")}
                                    />
                                    <PrimaryButton
                                        size={"greenBtn"}
                                        onClick={addWithdrawalMethod}
                                        label={
                                            format === initialData.format ? t("tutor_payout.update") : t("tutor_payout.agree_and_link")
                                        }
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
            <ConfirmModal
                open={disconnectStart}
                isLoading={withdrawing}
                title={t("tutor_payout.confirm_disconnect_title")}
                content={t("tutor_payout.confirm_disconnect_content")}
                actionLabel={t("tutor_payout.confirm_disconnect_action")}
                handleClose={() => setDisconnectStart(false)}
                onConfirm={handleDisconnect}
            />
        </div>
    );
};

export default TutorPayout;