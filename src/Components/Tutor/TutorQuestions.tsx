import React, { useCallback, useContext, useEffect, useRef, useState } from "react";
import { useRouter } from 'next/navigation';
import { UserContext } from "@/src/Contexts/UserContext";
import styles from "@/src/styles/tutorPage.module.css";
import loginStyles from "@/src/styles/login.module.css";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import { AppNotify } from "@/src/Utils/AppNotify";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import FileInputWithPreview from "../Widgets/FileInputWithPreview";
import TextAreaField from "../Form/TextAreaField";
import NotificationModal from "../Modals/NotificationModal";
import { useImageModal } from "@/src/Contexts/ImageModalContext";
import { getFunctions, httpsCallable } from "firebase/functions";

import { collection, doc, getDoc, onSnapshot, query, where, writeBatch } from "firebase/firestore";

import {
    addDocInCollection,
    app,
    db,
    getAllDocs,
    getApplicantQuestionsFunc,
    getUserAskedQuestions,
    listenToDocument,
    
    queryData,
    signOutUser,
    updateCollection,
    uploadImage,
} from "@/src/Firebase/firebase.utils";

import { calculateTutorRegProgress, isArray, zeroPadId } from "@/src/Utils/helpers";
import Spinner from "@/src/Components/Widgets/Spinner";
import BorderButton from "@/src/Components/Buttons/BorderButton";
import ReminderModal from "@/src/Components/Modals/ReminderModal";
import { useTranslation, locales as localesObj } from "@/src/Utils/i18n";

const functions = getFunctions(app);
const recordAnswerAndEarning = httpsCallable(functions, 'recordAnswerAndEarning');
const denyApplicant = httpsCallable(functions, 'denyApplicant');

interface TutorQuestionsProps {
    setActiveTab?: (tab: number | ((prev: number) => number)) => void;
}

const TutorQuestions = ({ setActiveTab }: TutorQuestionsProps) => {
    const { t, locale } = useTranslation();
    const router = useRouter();
    const { openImage } = useImageModal();
    const { user, setUser, isLoggingOut } = useContext(UserContext);
    const isRequestingRef = useRef(false);

    const [questions, setQuestions] = useState<any>([]);
    const [answer, setAnswer] = useState("");
    const [open, setOpen] = useState(false);
    const [openReminderModal, setOpenReminderModal] = useState(false);
    const [selectedAnswer, setSelectedAnswer] = useState<any>({});
    const [newQuestion, setNewQuestion] = useState<any>(null);
    const [noQuestions, setNoQuestions] = useState(false);
    const [applicantQuestionSubmitted, setApplicantQuestionSubmitted] = useState(false);
    const [answerImg, setAnswerImg] = useState<any>(null);
    const [showCompModal, setShowCompModal] = useState(false);
    const [isInitialLoad] = useState(true); // Only keep the state, remove the setter

    // eslint-disable-next-line no-unused-vars
    const [addToProfile, setAddToProfile] = useState(false);
    const [adminSettings, setAdminSettings] = useState<any>({});
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSkipping, setIsSkipping] = useState(false);
    const [openApplicantSkipModal, setOpenApplicantSkipModal] = useState(false);
    const [openTutorQuesSkipModal, setOpenTutorQuesSkipModal] = useState(false);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const [isDenying, setIsDenying] = useState(false);
    const [hasShownDenialMessage, setHasShownDenialMessage] = useState(false);

    const isApplicant = user.isApplicant;
    const handleOpen = useCallback(() => setOpen(true), []);
    const handleClose = useCallback(() => setOpen(false), []);

    const getAdminSettings = useCallback(async () => {
        await listenToDocument('test_question_pay', "admin_config", (settingsData) => {
            if (settingsData.status && settingsData.exists) {
                setAdminSettings(settingsData.data);
            } else {
                setAdminSettings({});
            }
        });
    }, []);

    const getApplicantQuestions = useCallback(async (isInitialFetch = false) => {
        if (isInitialFetch) {
            setIsTransitioning(true);
        } else {
            setIsTransitioning(true);
        }

        // Exit if not an applicant, user ID is missing, or user has already answered 2 questions
        if (!isApplicant || !user.id || (user.applicantQuestionAnswered || 0) >= 2) {
            setApplicantQuestionSubmitted((user.applicantQuestionAnswered || 0) >= 2);
            setQuestions([]);
            setIsTransitioning(false);
            return;
        }

        try {
            const resp: any = await getApplicantQuestionsFunc(user.id as string);

            if (resp.status && Array.isArray(resp.fullData) && resp.fullData.length > 0) {
                // fetch answers to avoid showing duplicates
                const collectionName = "applicant_answers";
                const resp2: any = await queryData(collectionName, "answeredBy", user.id);

                // qid's that user has already answered
                let excludedDocIds: string[] = [];
                if (resp2?.status && Array.isArray(resp2.fullData)) {
                    excludedDocIds = resp2.fullData.map(q => q.questionId);
                }

                // filter ques already answered
                const filteredQuestions = resp.fullData.filter(
                    question => question.docId && !excludedDocIds.includes(question.docId)
                );

                setQuestions(filteredQuestions);
                setCurrentQuestionIndex(0);
                setNoQuestions(filteredQuestions.length === 0);
                setApplicantQuestionSubmitted(false);
            } else {
                setNoQuestions(true);
                setQuestions([]);
                setApplicantQuestionSubmitted(false);
            }
        } catch (error) {
            console.error("Error fetching applicant questions:", error);
            AppNotify("Could not load applicant questions.", "error");
            setNoQuestions(true);
            setQuestions([]);
        } finally {
            setIsTransitioning(false);
        }
    }, [isApplicant, user.id, user.applicantQuestionAnswered]);

    const getTutorQuestions = useCallback(async () => {
        if (isRequestingRef.current) {
            return;
        }
        isRequestingRef.current = true;
        setIsSubmitting(true);

        const acceptQuestionLocal = async (question) => {
            if (!question || !question.docId) {
                return;
            }
            try {
                const currentQuestionRef: any = doc(db, "users_questions", question.docId);
                const currentQuestionSnap = await getDoc(currentQuestionRef);

                if (!currentQuestionSnap.exists()) {
                    AppNotify("Question is no longer available.", "warning");
                    getTutorQuestions();
                    return;
                }
                const questionData = currentQuestionSnap.data();
                // @ts-ignore
                if (questionData?.exclusive || (questionData as any)?.isAnswered) {
                    AppNotify("Question is no longer available.", "warning");
                    getTutorQuestions();
                    return;
                }
                const existingClaimResp: any = await queryData("tutor_exclusive_questions", "claimedBy", user.id);
                if (existingClaimResp.status && existingClaimResp.fullData.length > 0) {
                    AppNotify("You already have a question assigned.", "warning");
                    setNewQuestion(existingClaimResp.fullData[0]);
                    return;
                }
                const dataToClaim = { ...question };
                const originalQuestionId = dataToClaim.docId;
                delete dataToClaim.docId;
                dataToClaim.questionId = originalQuestionId;

                const batch = writeBatch(db);
                const exclusiveDocRef = doc(collection(db, "tutor_exclusive_questions"));
                batch.set(exclusiveDocRef, {
                    ...dataToClaim,
                    dateClaimed: new Date().getTime().toString(),
                    claimedBy: user.id,
                });
                batch.update(currentQuestionRef, { exclusive: true } as any);
                await batch.commit();
                const newClaimedDoc = await getDoc(exclusiveDocRef);
                if (newClaimedDoc.exists()) {
                    const claimedQuestionData = { ...newClaimedDoc.data(), docId: newClaimedDoc.id };
                    setNewQuestion(claimedQuestionData);
                    setAnswer("");
                    setAnswerImg(null);
                    setNoQuestions(false);
                } else {
                    throw new Error("Failed to retrieve claimed question data.");
                }
            } catch (error) {
                console.error("Error accepting question:", error);
                AppNotify("Failed to claim question.", "error");
                setNewQuestion(null);
                setNoQuestions(true);
            }
        };

        const getAvailableQuestionsLocal = async () => {
            try {
                const resp = await getUserAskedQuestions(user.id);
                if (resp?.status && resp.fullData.length > 0) {
                    // Strict filter: only show questions with language matching locale
                    const filtered = resp.fullData.filter(q => q.language === locale);
                    if (filtered.length > 0) {
                        await acceptQuestionLocal(filtered[0]);
                    } else {
                        setNoQuestions(true);
                        setNewQuestion(null);
                    }
                } else {
                    setNoQuestions(true);
                    setNewQuestion(null);
                }
            } catch (error) {
                console.error("Error getting available questions:", error);
                AppNotify("Error finding questions.", "error");
                setNewQuestion(null);
                setNoQuestions(true);
            }
        };

        try {
            const resp: any = await queryData("tutor_exclusive_questions", "claimedBy", user.id);
            if (resp.status && resp.fullData.length > 0) {
                const claimedQ = resp.fullData[0];
                if (claimedQ.language === locale) {
                    setNewQuestion(claimedQ);
                    setNoQuestions(false);
                } else {
                    // If claimed question language does not match, ignore and try to get a new one
                    setNewQuestion(null);
                    await getAvailableQuestionsLocal();
                }
            } else {
                setNewQuestion(null);
                await getAvailableQuestionsLocal();
            }
        } catch (error) {
            console.error("Error checking/fetching tutor questions:", error);
            setNewQuestion(null);
            setNoQuestions(true);
        } finally {
            isRequestingRef.current = false;
            setIsSubmitting(false);
        }
    }, [user.id, locale]);

    useEffect(() => {
        // block denied users
        if ((user as any).denied && !isLoggingOut) {
            if (!hasShownDenialMessage) {
                AppNotify("Access denied. You cannot use this service.", "error");
                setHasShownDenialMessage(true);
                setTimeout(() => {
                    handleSignOut();
                }, 2000);
            }
            return;
        }

        if (user.id !== "") {
            const {
                firstName,
                lastName,
                phoneNumber,
                dateOfBirth,
                educationLevel,
                tutorExperience,
                country,
                certificateNumber
            } = user;

            const progressPercentage = calculateTutorRegProgress({
                firstName,
                lastName,
                phoneNumber,
                dateOfBirth,
                educationLevel,
                tutorExperience,
                country,
                certificateNumber,
            });

            if (isApplicant) {
                if (isInitialLoad) {
                    setIsTransitioning(true);
                }
                setIsSubmitting(false);
                isRequestingRef.current = false;
                setAddToProfile(false);
                getApplicantQuestions(isInitialLoad);

                if (progressPercentage < 100) {
                    setShowCompModal(true);
                } else {
                    setShowCompModal(false);
                }
            } else if (user.isAvailable) {
                setIsTransitioning(false);
                let isInitialLoad = true;
                const q = query(
                    collection(db, "users_questions"),
                    where("exclusive", "==", false),
                    where("isAnswered", "==", false)
                );

                const unsubscribe = onSnapshot(q, (querySnapshot) => {
                    if (isInitialLoad) {
                        setTimeout(() => {
                            if (!isRequestingRef.current) {
                                getTutorQuestions();
                            }
                        }, 300);
                        isInitialLoad = false;
                        return;
                    }

                    if (!newQuestion && !isRequestingRef.current) {
                        querySnapshot.docChanges().forEach((change) => {
                            if (change.type === "added") {
                                setTimeout(() => {
                                    if (!isRequestingRef.current) {
                                        getTutorQuestions();
                                    }
                                }, 300);
                            }
                        });
                    }
                }, (error) => {
                    console.error("Error listening to questions:", error);
                });

                return () => {
                    unsubscribe();
                };
            } else {
                setIsTransitioning(false);
                setIsSubmitting(false);
                setIsSkipping(false);
                isRequestingRef.current = false;
            }
        } else {
            setIsTransitioning(false);
        }

        getAdminSettings();
    }, [
        (user as any).denied,
        user.id,
        isApplicant,
        user.isAvailable,
        user.dateOfBirth,
        user.certificateNumber,
        user.firstName,
        user.lastName,
        user.phoneNumber,
        getApplicantQuestions,
        getTutorQuestions,
        getAdminSettings,
        hasShownDenialMessage,
        isInitialLoad
    ]);

    useEffect(() => {
        const delay = 300;
        const timeoutId = setTimeout(() => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth',
            });
        }, delay);
        return () => clearTimeout(timeoutId);
    }, []);

    const handleSignOut = useCallback(async () => {
        try {
            await signOutUser();
            router.push("/");
        } catch (error) {
            console.error("Error signing out:", error);
        }
    }, [router]);

    const handleAutoApproveApplicant = useCallback(async () => {
        setIsSubmitting(true);
        const resp = await updateCollection(
            "users",
            user.id,
            {
                isApplicant: false,
            },
            true
        );

        setIsSubmitting(false);
        if (resp.status) {
            router.push('/profile?u=valid');
        }
        handleClose();
    }, [user.id, router, handleClose]);

    const handleAutoDenyApplicant = useCallback(async () => {
        if (isDenying || hasShownDenialMessage) return;

        setIsDenying(true);
        setHasShownDenialMessage(true);

        try {
            const result: any = await denyApplicant();

            if (result.data.status) {
                setUser((prevUser) => ({
                    ...prevUser,
                    denied: true,
                    isApplicant: false,
                    applicantQuestionAnswered: 0
                } as any));

                AppNotify("You're not able to become a Tutor at this time. Goodbye.", "error");

                // Sign out after delay
                setTimeout(() => {
                    handleSignOut();
                }, 5000);
            } else {
                throw new Error(result.data.message || "Denial process failed");
            }
        } catch (error) {
            console.error("Error denying applicant:", error);
            setHasShownDenialMessage(false); // Allow retry
        } finally {
            setIsDenying(false);
        }
    }, [isDenying, hasShownDenialMessage, handleSignOut, setUser]);

    const getApplicantTotalCorrectAnswers = async (): Promise<number | void> => {
        try {
            const userId = user.id;
            const collectionName = "applicant_answers";
            const resp: any = await queryData(collectionName, "answeredBy", userId);

            if (resp && resp.status && resp.fullData && resp.fullData.length > 0) {
                const questionsResp: any = await getAllDocs("applicant_questions");
                const questionsMap = new Map(
                    questionsResp.fullData.map((q: any) => [q.docId, q])
                );

                let totalCorrectAns = 0;

                resp.fullData.forEach((answer: any) => {
                    const ques: any = questionsMap.get(answer.questionId);
                    const correctKey = ques?.correctAnswer?.toLowerCase();
                    const correctAnswer = correctKey ? ques[correctKey] : '';

                    const isCorrect = answer.answer && answer.answer.includes(correctAnswer);
                    if (isCorrect) {
                        totalCorrectAns++;
                    }
                });

                return totalCorrectAns;
            }
        } catch (error) {
            console.error("Error fetching applicant answers:", error);
        }
    };

    const handleCheckApplicantStatus = useCallback(async () => {
        if ((user.applicantQuestionAnswered || 0) < 2 || hasShownDenialMessage) return;
        try {
            const totalCorrect = await getApplicantTotalCorrectAnswers();
            if (typeof totalCorrect === "number") {
                if (totalCorrect >= 2) {
                    handleAutoApproveApplicant();
                } else {
                    handleAutoDenyApplicant();
                }
            }
        } catch (error) {
            console.error("Error checking applicant status:", error);
        }
    }, [questions, user.id, user.applicantQuestionAnswered, hasShownDenialMessage, handleAutoApproveApplicant, handleAutoDenyApplicant]);

    useEffect(() => {
        if (user && (user.applicantQuestionAnswered || 0) >= 2 && isApplicant) {
            handleCheckApplicantStatus();
        }
    }, [user, isApplicant, handleCheckApplicantStatus]);

    const handleQuestionSkip = useCallback(async (
        exclusiveQuestionDocId: string,
        originalQuestionId: string
    ) => {
        if (isSkipping) return;
        setIsSkipping(true);
        try {
            await addDocInCollection(
                "users_skipped_questions",
                {
                    questionId: originalQuestionId,
                    userId: user.id,
                    skippedAt: new Date().getTime().toString(),
                },
                true
            );

            const batch = writeBatch(db);
            const exclusiveRef = doc(db, "tutor_exclusive_questions", exclusiveQuestionDocId);
            batch.delete(exclusiveRef);
            const questionRef = doc(db, "users_questions", originalQuestionId);
            batch.update(questionRef, { exclusive: false } as any);
            await batch.commit();

            setNewQuestion(null);
            setAnswer("");
            setAnswerImg(null);
            setOpenTutorQuesSkipModal(false);

        } catch (error) {
            console.error("Error skipping question:", error);
            AppNotify("Failed to skip question.", "error");
        } finally {
            setIsSkipping(false);
            isRequestingRef.current = false;
            setTimeout(() => {
                getTutorQuestions();
            }, 100);
        }
    }, [isSkipping, user.id, getTutorQuestions]);


    const handleAnswerSubmit = useCallback(async (ques) => {
        if (!answer || answer.trim() === "") {
            AppNotify(t('TutorQuestions.answer_cannot_be_empty'), "warning");
            return;
        }
        if (!ques || !ques.docId || !ques.questionId) {
            AppNotify(t('TutorQuestions.invalid_question_data'), "error");
            return;
        }
        if (isSubmitting) return;

        setIsSubmitting(true);
        const currentAnswer = answer;
        let finalAnswerImageUrl: string | null = null;

        try {
            if (answerImg) {
                const imgResp = await uploadImage(answerImg);
                if (imgResp.status && imgResp.downloadUrl) {
                    finalAnswerImageUrl = imgResp.downloadUrl;
                } else {
                    throw new Error("Failed to upload answer image.");
                }
            }

            const paragraphs = currentAnswer.split("\n").filter((p) => p.trim() !== "");
            if (paragraphs.length === 0) {
                throw new Error("Answer content is empty.");
            }

            const result: any = await recordAnswerAndEarning({
                questionDocId: ques.docId,
                originalQuestionId: ques.questionId,
                isApplicantQuestion: false,
                answerText: paragraphs,
                answerImageUrl: finalAnswerImageUrl,
            });

            if (result.data.status) {
                setNewQuestion(null);
                setAnswer('');
                setAnswerImg(null);
            } else {
                throw new Error(result.data.message || "Backend failed.");
            }
        } catch (error: any) {
            console.error("Error submitting answer:", error);
            setAnswer(currentAnswer);
        } finally {
            setIsSubmitting(false);
            isRequestingRef.current = false;
            setTimeout(() => {
                getTutorQuestions();
            }, 100);
        }
    }, [answer, answerImg, isSubmitting, getTutorQuestions ,t]);

    const handleApplicantAnswerSubmit = useCallback(async (currentQuestion) => {
        const selectedAns = selectedAnswer?.[currentQuestion.docId];

        if (!selectedAns) {
            AppNotify(t('TutorQuestions.please_select_answer'), "warning");
            return;
        }
        if (!currentQuestion || !currentQuestion.docId) {
            AppNotify(t('TutorQuestions.invalid_question_data'), "error");
            return;
        }
        if (isSubmitting) return;

        setIsSubmitting(true);
        const currentSelection = { ...selectedAnswer };
        setSelectedAnswer((prev) => ({ ...prev, [currentQuestion.docId]: undefined }));

        try {
            const result: any = await recordAnswerAndEarning({
                questionDocId: currentQuestion.docId,
                originalQuestionId: currentQuestion.docId,
                isApplicantQuestion: true,
                answerText: [selectedAns],
                answerImageUrl: null,
            });

            if (result.data.status) {
                const newApplicantCount = result.data.newApplicantCount || ((user.applicantQuestionAnswered || 0) + 1);

                setUser((prevUser) => ({
                    ...prevUser,
                    applicantQuestionAnswered: newApplicantCount
                }));

                if (newApplicantCount >= 2) {
                    setApplicantQuestionSubmitted(true);
                    setQuestions([]);
                } else {
                    // remove answered ques
                    const remainingQuestions = questions.filter((q) => q.docId !== currentQuestion.docId);
                    setQuestions(remainingQuestions);

                    // Move to next q
                    if (remainingQuestions.length > 0) {
                        const currentIndex = questions.findIndex((q: any) => q.docId === currentQuestion.docId);
                        const nextIndex = currentIndex >= remainingQuestions.length ? 0 : currentIndex;
                        setCurrentQuestionIndex(nextIndex);
                    } else {
                        await getApplicantQuestions(false);
                    }
                }
            } else {
                setSelectedAnswer(currentSelection);
                throw new Error(result.data.message || "Backend failed.");
            }
        } catch (error: any) {
            setSelectedAnswer(currentSelection);
            console.error("Error submitting applicant answer:", error);
            AppNotify(error.message || "Failed to submit answer.", "error");
        } finally {
            setIsSubmitting(false);
        }
    }, [selectedAnswer, isSubmitting, user.applicantQuestionAnswered, questions, setUser, getApplicantQuestions, t]);

    const handleCompleteRegistrationFromModal = useCallback(() => {
        router.push(`/${locale}/registration`);
        setActiveTab?.(4);
        setOpenReminderModal(false);

    }, [router, setActiveTab ,locale]);

    const skipApplicantQuestion = async (currentQuestion: any) => {
        if (!currentQuestion || !currentQuestion.docId || isSubmitting) return;
        setIsSubmitting(true);
        try {
            const newQuestions = questions.filter((q: any) => q.docId !== currentQuestion.docId);
            setQuestions(newQuestions);
            setSelectedAnswer((prev) => ({ ...prev, [currentQuestion.docId]: undefined }));
            setCurrentQuestionIndex(0);
            setOpenApplicantSkipModal(false);
            if (newQuestions.length === 0 && user.applicantQuestionAnswered < 2) {
                await getApplicantQuestions();
            }
        } catch (error) {
            console.error("Error skipping applicant question:", error);
            AppNotify("Failed to skip question.", "error");
        } finally {
            setIsSubmitting(false);
        }
    };

    // Debug: log all questions and their language
    console.log("Current locale:", locale);
    console.log("Questions in state:", questions.map(q => ({ id: q.docId, lang: q.language })));

    // Always filter questions by language for display
    const filteredQuestions = questions.filter(q => q.language === locale);
    let applicantCurrentQuestion = !isApplicant || filteredQuestions.length === 0 ? null : filteredQuestions[currentQuestionIndex];

    if (addToProfile) {
        return (
            <div className={styles.tutorPage}>
                <div className={styles.attentionBox}>
                    <div className={styles.attentionBoxList}>
                        {user.firstName === "" && <div className={styles.attentionListItem}>
                            <div className={styles.attentionItemWrap}>
                                <div className={styles.attentionItemTitle}>{t('TutorQuestions.first_name')}</div>
                                <img src={"/icons/blue-check-icon.svg"} alt="check icon" /></div>
                        </div>}
                        {user.lastName === "" && <div className={styles.attentionListItem}>
                            <div className={styles.attentionItemWrap}>
                                <div className={styles.attentionItemTitle}>{t('TutorQuestions.last_name')}</div>
                                <img src={"/icons/blue-check-icon.svg"} alt="check icon" /></div>
                        </div>}
                        {(user.phoneNumber === "" || user.phoneNumber === "+") &&
                            <div className={styles.attentionListItem}>
                                <div className={styles.attentionItemWrap}>
                                    <div className={styles.attentionItemTitle}>{t('TutorQuestions.phone_number')}</div>
                                    <img src={"/icons/blue-check-icon.svg"} alt="check icon" /></div>
                            </div>}
                        {user.dateOfBirth === "" && <div className={styles.attentionListItem}>
                            <div className={styles.attentionItemWrap}>
                                <div className={styles.attentionItemTitle}>{t('TutorQuestions.date_of_birth')}</div>
                                <img src={"/icons/blue-check-icon.svg"} alt="check icon" /></div>
                        </div>}
                        {user.certificateNumber === "" && (
                            <div className={styles.attentionListItem}>
                                <div className={styles.attentionItemWrap}>
                                    <div className={styles.attentionItemTitle}>
                                        {t('TutorQuestions.certificate_number')} <a href={"https://www.acpt.com"} target={"_blank"}>www.acpt.com</a>
                                    </div>
                                    <img src={"/icons/blue-check-icon.svg"} alt="check icon" />
                                </div>
                            </div>
                        )}
                    </div>

                    <PrimaryButton
                        label={t('TutorQuestions.complete_registration')}
                        size={"bigger"}
                        onClick={() => {
                            router.push("/settings");
                            setActiveTab?.(3);
                        }}
                    />
                    <div className={styles.attentionBoxText}>
                        {t('TutorQuestions.complete_registration_desc')}
                    </div>
                </div>
            </div>
        );
    }

    const earningAmount = adminSettings?.amount ? Number(adminSettings.amount).toFixed(0) : '3';

    return (
        <div className={`${styles.tutorPage} ${styles.tutorAddQuesPage}`}>
            <div className={styles.tutorContainer}>
                {noQuestions && !newQuestion && !isApplicant ? '' : (
                    <div className={styles.addQuestionTitle}>
                        <h1>{t('TutorQuestions.answer_questions_title')}</h1>
                        <div>{t('TutorQuestions.answer_questions_desc')}</div>
                    </div>
                )}

                {isSubmitting && !newQuestion && !noQuestions ? <Spinner /> : (
                    <div className={styles.outerBorder}>
                        <div className={styles.questionList}>
                            {noQuestions && !newQuestion && !isApplicant && (
                                <div className={questionStyles.emptyQuesMsgBox}>
                                    <div><img src={'/icons/empty-mail-box.svg'} alt={'Empty Mail'} /></div>
                                    <div className={questionStyles.eqmInfoBox}>
                                        <h2>{t('TutorQuestions.class_dismissed_title')}</h2>
                                        <div>{t('TutorQuestions.class_dismissed_desc')}</div>
                                    </div>
                                    <PrimaryButton
                                        label={t('TutorQuestions.my_profile')}
                                        size={'greenBtn'}
                                        onClick={() => {
                                            router.push("/profile");
                                            setActiveTab?.(5);
                                        }}
                                    />
                                </div>
                            )}

                            {isApplicant ? (
                                applicantCurrentQuestion ? (
                                    <>
                                        <div className={styles.applicantQuestionBox}>
                                            <div className={styles.question}>
                                                <div className={styles.questionWrap}>
                                                    <div className={styles.questionImage}>
                                                        <img
                                                            alt={''}
                                                            src={applicantCurrentQuestion.imageUrl || "/icons/ques-no-image.svg"}
                                                            onClick={() => openImage(applicantCurrentQuestion.imageUrl || "/icons/ques-no-image.svg")}
                                                            style={{
                                                                width: "104px",
                                                                height: "104px",
                                                                objectFit: "cover",
                                                                cursor: applicantCurrentQuestion.imageUrl ? 'pointer' : 'default'
                                                            }}
                                                        />
                                                    </div>
                                                    <div className={styles.questionInfoBox}>
                                                        <div className={questionStyles.accChoiceQues}>
                                                            <div className={questionStyles.accQuesTitle}>
                                                                <img className={questionStyles.accQuesAnsImg}
                                                                    src={'/icons/comment-icon.svg'}
                                                                    alt={'comment icon'} />
                                                                <div className={questionStyles.accQuesText}>{applicantCurrentQuestion.question && isArray(applicantCurrentQuestion.question) ? (applicantCurrentQuestion.question as string[]).map((p, index) => (
                                                                    <p key={index}>{p}</p>)) : applicantCurrentQuestion.question}</div>
                                                            </div>
                                                            <div className={questionStyles.accQuesChoices}>
                                                                <div>a. {applicantCurrentQuestion.answer1}</div>
                                                                <div>b. {applicantCurrentQuestion.answer2}</div>
                                                                <div>c. {applicantCurrentQuestion.answer3}</div>
                                                                <div>d. {applicantCurrentQuestion.answer4}</div>
                                                            </div>
                                                        </div>
                                                        <div className={loginStyles.inputFieldWrap} style={{ padding: 0, margin: 0 }}>
                                                            <div className={loginStyles.label}>
                                                                <img className={questionStyles.accQuesAnsImg}
                                                                    src={'/icons/right-check-icon.svg'}
                                                                    alt={'right icon'} />
                                                                <div className={questionStyles.accQuesAns}>{t('TutorQuestions.answer')}</div>
                                                            </div>
                                                            <div className={styles.applicantAnswerOptionsBox}>
                                                                {['A', 'B', 'C', 'D'].map((choice, index) => {
                                                                    const answerKey = `answer${index + 1}`;
                                                                    const answerValue = applicantCurrentQuestion[answerKey];
                                                                    const isSelected = selectedAnswer?.[applicantCurrentQuestion.docId] === answerValue;
                                                                    return (
                                                                        <div key={choice}
                                                                            className={`${styles.applicantAnswerOption} ${isSelected && styles.selectedAnswer}`}
                                                                            onClick={() => !isSubmitting && !isTransitioning && setSelectedAnswer({
                                                                                ...selectedAnswer,
                                                                                [applicantCurrentQuestion.docId]: answerValue
                                                                            })}>
                                                                            {choice}
                                                                        </div>
                                                                    );
                                                                })}
                                                            </div>
                                                        </div>
                                                        <div className={styles.acceptAnsBtnContainer}>
                                                            <BorderButton
                                                                type={'dark'}
                                                                label={isSubmitting ? t('TutorQuestions.skipping') : t('TutorQuestions.skip_question')}
                                                                disabled={isSubmitting}
                                                                onClick={() => {
                                                                    setOpenApplicantSkipModal(true)
                                                                }}
                                                            />
                                                            <PrimaryButton
                                                                label={isSubmitting ? t('TutorQuestions.submitting') : t('TutorQuestions.answer_for', { amount: earningAmount })}
                                                                onClick={() => {
                                                                    showCompModal ? setOpenReminderModal(true) : handleApplicantAnswerSubmit(applicantCurrentQuestion)
                                                                }}
                                                                disabled={isSubmitting || !selectedAnswer?.[applicantCurrentQuestion.docId]}
                                                                size={'greenBtn'}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className={styles.questionCost}>{t('TutorQuestions.question_cost', { amount: earningAmount })}</div>
                                            </div>
                                            <div className={styles.accQuesMetaRow}>
                                                <div className={questionStyles.accQuesId}>{t('TutorQuestions.question_id_number', { id: applicantCurrentQuestion.guid })}</div>
                                                <span className={questionStyles.questionMeta}>
                                                    <div className={questionStyles.questionFeedback} onClick={() => { handleOpen() }}>{t('TutorQuestions.feedback')}</div>
                                                </span>
                                            </div>
                                        </div>
                                    </>
                                ) : applicantQuestionSubmitted ? (
                                    <div className={questionStyles.addQuestionBox}><h2>{t('TutorQuestions.applicant_thank_you')}</h2></div>
                                ) : (
                                    <div className={questionStyles.addQuestionBox}><h2>{t('TutorQuestions.no_applicant_questions')}</h2>
                                        <div>{t('TutorQuestions.no_applicant_questions_desc')}</div>
                                    </div>
                                )
                            ) : (
                                newQuestion && (
                                    <div className={styles.applicantQuestionBox}>
                                        <div className={styles.question}>
                                            <div className={styles.questionWrap}>
                                                <div className={styles.questionImage}>
                                                    <img
                                                        alt={''}
                                                        src={newQuestion.imageUrl || "/icons/ques-no-image.svg"}
                                                        onClick={() => openImage(newQuestion.imageUrl || "/icons/ques-no-image.svg")}
                                                        style={{
                                                            width: "104px",
                                                            height: "104px",
                                                            objectFit: "cover",
                                                            cursor: newQuestion.imageUrl ? 'pointer' : 'default'
                                                        }}
                                                    />
                                                </div>
                                                <div className={styles.questionInfoBox}>
                                                    <div className={questionStyles.accQuesTitle}>
                                                        <img className={questionStyles.accQuesAnsImg}
                                                            src={'/icons/comment-icon.svg'} alt={'comment icon'} />
                                                        <div className={questionStyles.accQuesText}>{newQuestion.question && isArray(newQuestion.question) ? (newQuestion.question as string[]).map((p, index) => (
                                                            <p key={index}>{p}</p>)) : newQuestion.question}</div>
                                                    </div>
                                                    {/* Show question language */}
                                                    <div style={{ margin: '8px 0', color: '#335CFF', fontWeight: 500 }}>
                                                        {t('TutorQuestions.language_label')}: {newQuestion.language ? (localesObj[newQuestion.language] || newQuestion.language) : t('TutorQuestions.language_unknown')}
                                                    </div>
                                                    <div className={loginStyles.inputFieldWrap} style={{ padding: 0, margin: 0 }}>
                                                        <div className={loginStyles.label}>
                                                            <img className={questionStyles.accQuesAnsImg}
                                                                src={'/icons/right-check-icon.svg'}
                                                                alt={'right icon'} />
                                                            <div className={questionStyles.accQuesAns}>{t('TutorQuestions.answer')}</div>
                                                        </div>
                                                        <TextAreaField
                                                            showBorder={true}
                                                            placeholder={t('TutorQuestions.write_answer_placeholder')}
                                                            rows={5}
                                                            value={answer}
                                                            onChange={(e) => {
                                                                if (answer.length < 2000) {
                                                                    setAnswer(e.target.value)
                                                                }
                                                            }}
                                                            fullWidth
                                                            disabled={isSubmitting}
                                                        />
                                                        <div className={questionStyles.counter}>{t('TutorQuestions.counter', { count: answer.length })}</div>
                                                    </div>
                                                    <div className={loginStyles.inputFieldWrap} style={{ margin: 0, width: "100%" }}>
                                                        <div className={loginStyles.label}>
                                                            <img className={questionStyles.accQuesAnsImg}
                                                                src={'/icons/ask-form/upload-icon.svg'}
                                                                alt={'upload icon'} />
                                                            <div className={questionStyles.accQuesText}>{t('TutorQuestions.upload_answer_photo')}</div>
                                                        </div>
                                                        <FileInputWithPreview onChange={setAnswerImg} disabled={isSubmitting} />
                                                    </div>
                                                    <div className={styles.acceptAnsBtnContainer}>
                                                        <BorderButton
                                                            type={'dark'}
                                                            label={isSkipping ? t('TutorQuestions.skipping') : t('TutorQuestions.skip_question')}
                                                            disabled={isSkipping}
                                                            onClick={() => setOpenTutorQuesSkipModal(true)}
                                                        />
                                                        <PrimaryButton
                                                            label={isSubmitting ? t('TutorQuestions.submitting') : t('TutorQuestions.answer_for', { amount: earningAmount })}
                                                            onClick={() => handleAnswerSubmit(newQuestion)}
                                                            disabled={isSubmitting || !answer || answer.trim().length === 0}
                                                            size={'greenBtn'}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                            <div className={styles.questionCost}>{t('TutorQuestions.question_cost', { amount: earningAmount })}</div>
                                        </div>
                                        <div className={styles.accQuesMetaRow}>
                                            <div className={questionStyles.accQuesId}>{t('TutorQuestions.question_id_number', { id: newQuestion.id ? zeroPadId(newQuestion.id) : newQuestion.questionId })}</div>
                                            <span className={questionStyles.questionMeta}>
                                                <div className={questionStyles.questionFeedback} onClick={() => { handleOpen() }}>Feedback</div>
                                            </span>
                                        </div>
                                    </div>
                                )
                            )}
                        </div>
                    </div>
                )}
            </div>
            <NotificationModal
                open={open}
                handleClose={handleClose}
                icon={"/icons/flag-icon.svg"}
                title={t('TutorQuestions.if_you_need_help')}
                content={`<a href="mailto:${t('TutorQuestions.email_support')}">${t('TutorQuestions.email_support')}</a>`}
            />
            <ReminderModal
                handleClose={() => setOpenReminderModal(false)}
                onConfirm={handleCompleteRegistrationFromModal}
                open={openReminderModal}
                icon={'/icons/complete-reg-icon.svg'}
                actionLabel={t('TutorQuestions.complete_now')}
                title={t('TutorQuestions.complete_registration_modal_title')}
                content={t('TutorQuestions.complete_registration_modal_content')}
            />
            <NotificationModal
                icon={"/icons/flag-icon.svg"}
                open={openTutorQuesSkipModal}
                handleClose={() => {
                    if (!isSkipping) {
                        setOpenTutorQuesSkipModal(false);
                    }
                }}
                onCancel={() => {
                    if (!isSkipping) {
                        setOpenTutorQuesSkipModal(false);
                    }
                }}
                title={t('TutorQuestions.skip_question_modal_title')}
                cancelLabel={t('TutorQuestions.cancel')}
                actionLabel={isSkipping ? t('TutorQuestions.skipping') : t('TutorQuestions.skip_question_action')}
                onConfirm={() => {
                    if (!isSkipping && newQuestion) {
                        handleQuestionSkip(newQuestion.docId, newQuestion.questionId);
                    }
                }}
            />
            <NotificationModal
                icon={"/icons/flag-icon.svg"}
                open={openApplicantSkipModal}
                handleClose={() => setOpenApplicantSkipModal(false)}
                title={t('TutorQuestions.skip_question_modal_title')}
                actionLabel={t('TutorQuestions.skip_question_action')}
                cancelLabel={t('TutorQuestions.cancel')}
                onConfirm={() => skipApplicantQuestion(applicantCurrentQuestion)}
            />
        </div>
    );
};

export default TutorQuestions;