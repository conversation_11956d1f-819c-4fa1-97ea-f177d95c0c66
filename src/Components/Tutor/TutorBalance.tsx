// @ts-nocheck
"use client";

import React, {useContext, useEffect, useState} from "react";
import {useTranslation} from "@/src/Utils/i18n";
import styles from "@/src/styles/tutorBalance.module.css";
import {UserContext} from "../../Contexts/UserContext";
import {Paper} from "@mui/material";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import {AppNotify} from "../../Utils/AppNotify";
import {getFunctions, httpsCallable} from "firebase/functions";
import {addDocInCollection, queryData, updateCollection,} from "../../Firebase/firebase.utils";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import BorderButton from "../Buttons/BorderButton";
import EnhancedTableHead from "../Table/EnhancedTableHead";
import ConfirmModal from "../Modals/ConfirmModal";
import NotificationModal from "@/src/Components/Modals/NotificationModal";
import {formatCurrency, formatDate, maskAccountNumber,} from "../../Utils/helpers";
import Spinner from "@/src/Components/Widgets/Spinner";
import {ProcessedTransaction,  TutorBalanceTableColumn} from "@/src/Types";

const TutorBalance = () => {
    const {t} = useTranslation();
    // eslint-disable-next-line no-unused-vars
    const {user, setUser} = useContext(UserContext);
    const [checkPayoutModal, setCheckPayoutModal] = useState(false);
    const [openSuccessWithdrawModal, setOpenSuccessWithdrawModal] = useState(false);
    const [withdrawing, setWithdrawing] = useState(false);
    const [format, setFormat] = useState("iban");
    const [earningsLog, setEarningsLog] = useState([]);
    const [iban, setIban] = useState("");
    const [accountNumber, setAccountNumber] = useState("");
    const [withdrawStart, setWithdrawStart] = useState(false);
    const [withdrawalMethodExists, setWithdrawalMethodExists] = useState(false);
    const [first, setFirst] = useState(true);
    const balance = user.revenue;
    const totalEarnings = user.earnings;
    const [isLoading, setLoading] = useState(true);
    const [order, setOrder] = useState<'asc' | 'desc'>("desc");
    const [orderBy, setOrderBy] = useState<keyof ProcessedTransaction>("date");

    const processTransactions = (transactions) => {
        function getStartOfDay(timestamp) {
            let date;
            if (timestamp && typeof timestamp.toDate === 'function') {
                date = timestamp.toDate();
            } else if (timestamp && typeof timestamp.seconds !== 'undefined') {
                date = new Date(timestamp.seconds * 1000);
            } else if (typeof timestamp === 'number') {
                date = new Date(timestamp);
            } else if (timestamp instanceof Date) {
                date = timestamp;
            } else {
                date = new Date(timestamp);
            }

            date.setHours(0, 0, 0, 0);
            return date.getTime();
        }

        const rowsArr: any = [];

        transactions.forEach((transaction) => {
            const dayTimestamp = getStartOfDay(transaction.date);
            rowsArr.push({
                docId: transaction.docId,
                timestamp: dayTimestamp,
                date: formatDate(dayTimestamp),
                type: transaction.type,
                earnings: transaction.type === "Fund Withdrawal"
                    ? `-${formatCurrency(transaction.amount)}`
                    : `+${formatCurrency(transaction.amount)}`,
            });
        });

        const result = rowsArr.sort((a, b) => {
            const dateComparison = b.timestamp - a.timestamp;
            if (dateComparison !== 0) return dateComparison;
            return a.type.localeCompare(b.type);
        });

        return result.map(({timestamp, ...rest}) => rest);
    };

    const columns: readonly Column[] = [
        {id: "date", label: t('common.date'), minWidth: 120, sorting: true},
        {id: "type", label: t('common.type'), minWidth: 100, sorting: true},
        {id: "earnings", label: t('tutor.earnings'), minWidth: 170, sorting: true},
    ];

    useEffect(() => {
        if (first && user?.id) {
            setFirst(false);
            getTutorEarnings();
            getWithdrawalMethod();
        }
    }, [first, user]);

    const handleRequestSort = (event: React.MouseEvent<unknown>, property: keyof ProcessedTransaction) => {
        const isAscending = orderBy === property && order === "asc";
        const newOrder = isAscending ? "desc" : "asc";

        const sortedData = [...earningsLog].sort((a, b) => {
            let valueA: any = a[property];
            let valueB: any = b[property];

            const isValueABlank = valueA == null || valueA === "";
            const isValueBBlank = valueB == null || valueB === "";

            if (isValueABlank && isValueBBlank) return 0;
            if (isValueABlank) return 1;
            if (isValueBBlank) return -1;

            if (!isNaN(Number(valueA)) && !isNaN(Number(valueB))) {
                valueA = Number(valueA);
                valueB = Number(valueB);
            }

            if (typeof valueA === "number" && typeof valueB === "number") {
                return newOrder === "asc" ? valueA - valueB : valueB - valueA;
            }

            if (typeof valueA === "string" && typeof valueB === "string") {
                const comparison = valueA.toLowerCase().localeCompare(valueB.toLowerCase());
                return newOrder === "asc" ? comparison : -comparison;
            }

            return 0;
        });

        setOrder(newOrder);
        setOrderBy(property);
        setEarningsLog(sortedData);
    };

    const handleWithdraw = async () => {
        if (!user?.id) {
            AppNotify("User not found. Please refresh and try again.", "error");
            return;
        }

        setWithdrawing(true);
        const floatBalance = parseFloat(balance.toFixed(2));

        if (floatBalance <= 0) {
            AppNotify(t('tutor.no_balance_to_withdraw'), "info");
            setWithdrawing(false);
            setWithdrawStart(false);
            return;
        }

        try {
            // withdrawals more than 100 (limit set in admin TODO)
            if (floatBalance > 100) {
                const data = {
                    amount: floatBalance,
                    userId: user.id,
                    date: new Date().getTime(),
                    status: "Pending",
                    payoutMethodFormat: format,
                    payoutAccountNumber: format === "iban" ? iban : accountNumber,
                };

                const pendingResponseData = await addDocInCollection(
                    "pending_withdrawals",
                    data,
                    true
                );

                if (pendingResponseData.status) {
                    await updateCollection(
                        "users",
                        user.id,
                        {
                            revenue: 0,
                            pendingWithdrawals: (user.pendingWithdrawals || 0) + 1,
                            pendingWithdrawalAmount: (user.pendingWithdrawalAmount || 0) + floatBalance,
                        },
                        true
                    );
                    AppNotify(t('tutor.withdrawal_pending_approval'), "success");
                    setOpenSuccessWithdrawModal(true);
                    await getTutorEarnings();
                } else {
                    AppNotify(t('tutor.withdrawal_submit_failed'), "error");
                }
            }

}  catch (error) {
                console.error("Error creating pending withdrawal:", error);
                AppNotify(t('tutor.withdrawal_error'), "error");
            } finally {
                setWithdrawStart(false);
                setWithdrawing(false);
        }

        // withdrawal less than 100 - BE function via Wise
        try {
            const functions = getFunctions();
            const initiatePayout = httpsCallable(functions, 'initiateWisePayoutToTutor');
            const result = await initiatePayout({amount: floatBalance});

            // @ts-ignore
            if (result.data.status) {

                setOpenSuccessWithdrawModal(true);
                getTutorEarnings();
            } else {
                // @ts-ignore
                AppNotify(result.data.message || t('tutor.withdrawal_failed_wise'), "error");
            }
        } catch (error: any) {
            console.error("Error calling initiateWisePayoutToTutor:", error);
            if (error.code === 'functions/failed-precondition' && error.message.includes("first and last name")) {
                AppNotify(t('tutor.name_mismatch_error'), "error");
            } else {
                AppNotify(t('tutor.withdrawal_failed_support'), "error");
            }
        } finally {
            setWithdrawStart(false);
            setWithdrawing(false);
        }
    };

    const getWithdrawalMethod = async () => {
        if (!user?.id) return;

        try {
            const response: any = await queryData("withdrawal_methods", "user", user.id);
            if (response.status && response.fullData.length > 0) {
                setWithdrawalMethodExists(true);
                const data: any = response.fullData[0];
                setIban(data.iban || "");
                setFormat(data.format || "iban");
                setAccountNumber(data.accountNumber || "");
            } else {
                setWithdrawalMethodExists(false);
            }
        } catch (error) {
            console.error("Error fetching withdrawal method:", error);
            setWithdrawalMethodExists(false);
        }
    };

    const getTutorEarnings = async () => {
        if (!user?.id) return;
        setLoading(true);

        try {
            const response: any = await queryData("tutor_earnings_log", "userId", user.id);
            if (response.status && response.fullData.length > 0) {
                const data = processTransactions(response.fullData);
                setEarningsLog(data);
                setLoading(false);
            } else {
                setEarningsLog([]);
                setLoading(false);
            }
        } catch (error) {
            console.error("Error fetching tutor earnings:", error);
            setEarningsLog([]);
            setLoading(false);
        }
    };

    // Early return if user is not loaded
    if (!user?.id || isLoading) {
        return (
            <Spinner/>
        )
    }

    return (
        <div className={styles.container}>
            <div className={questionStyles.addQuestionTitle}>
                <h1>{t('tutor.earnings')}</h1>
                <div>{t('tutor.earnings_description')}</div>
            </div>
            <div className={styles.earningTwoCols}>
                <div className={styles.earningLeftBox}>
                    <div className={styles.balanceCard}>
                        <div className={styles.balanceLabel}>{t('tutor.available_balance')}</div>
                        <div className={styles.balanceAmount}>
                            {formatCurrency(balance)}
                        </div>
                        <PrimaryButton
                            size={"greenBtn"}
                            label={t('tutor.withdraw_funds')}
                            loading={withdrawing}
                            disabled={balance < 10 || (balance > 0 && balance < 0.5)}
                            onClick={() => {
                                !withdrawalMethodExists
                                    ? setCheckPayoutModal(true)
                                    : setWithdrawStart(true);
                            }}
                        />
                        <div className={styles.totalEarnBox}>
                            <div className={styles.totalEarnLabel}>{t('tutor.total_earnings')}</div>
                            <div className={styles.totalEarnAmount}>
                                {formatCurrency(totalEarnings)}
                            </div>
                        </div>
                        {(user.pendingWithdrawalAmount || 0) > 0 && (
                            <div className={styles.pendingWithdrawalInfo}>
                                {t('tutor.pending_withdrawals')}: {formatCurrency(user.pendingWithdrawalAmount)} ({user.pendingWithdrawals} {t('tutor.transaction', {count: user.pendingWithdrawals})})
                            </div>
                        )}
                    </div>

                    <div className={styles.withdrawCard}>
                        <div
                            className={styles.withdrawTitle}>{withdrawalMethodExists ? t('tutor.active_withdraw_method') : t('tutor.withdraw_methods')}</div>
                        {!withdrawalMethodExists ?
                            <div className={styles.withdrawText}>{t('tutor.setup_payout_method')}</div> : ''}
                        {withdrawalMethodExists && (
                            <div className={styles.linkedMethodBoxContainer}>
                                <img src={'/icons/payment-right.svg'} alt={'Right Icon'}/>
                                <img src={'/icons/add-bank-icon.svg'} alt={'Bank Icon'}/>
                                <div className={styles.linkedMethodBox}>
                                    <div className={styles.linkedMethodLabel}>
                                        {format === "iban" ? "IBAN" : format === "uk" ? t('tutor.uk_bank_account') : t('tutor.us_bank_account')}{" "}
                                    </div>
                                    <div className={styles.totalEarnLabel}>
                                        {maskAccountNumber(format === "iban" ? iban : accountNumber)}
                                    </div>
                                </div>
                            </div>
                        )}

                        <BorderButton
                            type={"dark"}
                            label={`${withdrawalMethodExists ? t('tutor.change_method') : t('tutor.add_withdraw_method')}`}
                            onClick={() => {
                                window.location.href = `/payout-method`
                            }}
                            style={{fontWeight: '600', borderColor: 'rgba(107, 204, 9, 1)'}}
                        />
                    </div>
                </div>
                <div className={styles.earningRightBox}>
                    <div className={"tableWrapper multiColsTable"}>
                        <Paper
                            sx={{
                                width: "100%",
                                boxShadow: "none",
                                borderRadius: '8px',
                                border: '1px solid rgba(229, 229, 229, 1)'
                            }}
                        >
                            <TableContainer className={"tableContainer"}>
                                <Table stickyHeader aria-label="sticky table">
                                    <EnhancedTableHead
                                        columns={columns as TutorBalanceTableColumn[]}
                                        order={order}
                                        orderBy={orderBy}
                                        onRequestSort={handleRequestSort}
                                        rowCount={earningsLog.length}
                                    />
                                    <TableBody>
                                        {earningsLog.length > 0 ? (
                                            earningsLog.map((row) => {
                                                return (
                                                    <TableRow
                                                        hover
                                                        role="checkbox"
                                                        tabIndex={-1}
                                                        key={row.docId}
                                                        className={row.isAdmin ? "blueColorRow" : ""}
                                                    >
                                                        {columns.map((column) => {
                                                            const value = row[column.id];
                                                            const transactionType = row.type;
                                                            let earnings = transactionType === "Fund Withdrawal"
                                                                ? `-${formatCurrency(value as number)}`
                                                                : `+${formatCurrency(value as number)}`;

                                                            return (
                                                                <TableCell key={column.id} align={column.align}>
                                                                    {column.id === 'earnings'
                                                                        ? earnings
                                                                        : column.id === 'date'
                                                                            ? formatDate(value as number)
                                                                            : value
                                                                    }
                                                                </TableCell>
                                                            );
                                                        })}
                                                    </TableRow>
                                                );
                                            })
                                        ) : (
                                            <TableRow>
                                                <TableCell colSpan={columns.length}
                                                           style={{textAlign: 'center', padding: '16px'}}>
                                                    {t('tutor.no_earnings_data')}
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </Paper>
                    </div>
                </div>
            </div>

            <ConfirmModal
                open={withdrawStart}
                isLoading={withdrawing}
                title={t('tutor.confirm_withdrawal')}
                content={t('tutor.confirm_withdrawal_amount', {amount: formatCurrency(balance)})}
                actionLabel={t('tutor.yes_withdraw')}
                handleClose={() => setWithdrawStart(false)}
                onConfirm={handleWithdraw}
            />

            <NotificationModal
                open={openSuccessWithdrawModal}
                icon={"/icons/check-round-icon.svg"}
                title={t('tutor.payment_sent')}
                content={t('tutor.payment_available_shortly')}
                handleClose={() => setOpenSuccessWithdrawModal(false)}
            />

            <NotificationModal
                open={checkPayoutModal}
                biggerButton={true}
                icon={"/icons/warning-icon.svg"}
                title={t('common.attention')}
                content={t('tutor.no_payout_method_added')}
                cancelLabel={t('common.close')}
                actionLabel={t('tutor.add_payout_method')}
                onCancel={() => setCheckPayoutModal(false)}
                onConfirm={() => {
                    window.location.href = `/payout-method`;
                }}
                handleClose={() => setCheckPayoutModal(false)}
            />
        </div>
    );
};

export default TutorBalance;