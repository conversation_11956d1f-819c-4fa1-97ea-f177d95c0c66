"use client";
import { FormControl, MenuItem, Select,SvgIcon } from "@mui/material";
import Tooltip from '@mui/material/Tooltip';
import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import axios from "axios";
import { useRouter } from "next/navigation";
import React, { useContext, useEffect, useMemo, useState } from "react";
import styles from "@/src/styles/paymentForm.module.css";
import countryList from "react-select-country-list";
import {
  addDocInCollection,
  updateCollection,
} from "@/src/Firebase/firebase.utils";
import { nativeLanguageNames } from "@/src/Utils/translationObject";
import {
  PaymentIntentResult,
  StripeCardNumberElement,
} from "@stripe/stripe-js";
import { AppNotify } from "@/src/Utils/AppNotify";
import { UserContext } from "@/src/Contexts/UserContext";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import InputTextField from "@/src/Components/Form/InputTextField";
import { useTranslation } from "@/src/Utils/i18n";

const ClientPaymentForm = (props) => {
  return <PaymentForm {...props} />;
};

function PaymentForm({
   price,
   btnDisable,
   buyClicked,
   setBuyClicked,
   packPage,
   purchasing,
   setPurchasing,
   langObj,
   handleClose,
   setBtnDisable,
   packInfo,
   setActiveTab,
 }: {
  price: number;
  btnDisable: boolean;
  purchasing: boolean;
  setPurchasing: React.Dispatch<React.SetStateAction<boolean>>;
  langObj: any;
  handleClose?: () => void;
  packInfo?: { id?: string; questions?: number; name?: string; credits?: number; [key: string]: any };
  packPage?: boolean;
  buyClicked?: boolean;
  setBuyClicked?: React.Dispatch<React.SetStateAction<boolean>>;
  setBtnDisable?: React.Dispatch<React.SetStateAction<boolean>>;
  setActiveTab?: any;
}) {
    const {user, setUser} = useContext(UserContext);
    const stripe = useStripe();
    const elements = useElements();
    const router = useRouter();
    const options = useMemo(() => countryList().getData(), []);
    const [countryOptions, setCountryOptions] = useState([]);
    const [country, setCountry] = useState("United States");
    const [name, setName] = useState("");
    const [cardNumber, setCardNumber] = useState("");
    const [expMonth, setExpMonth] = useState("");
    const [cvc, setCvc] = useState("");
    const { t ,locale } = useTranslation();

  useEffect(() => {
    if (options && options.length) {
      let specialCountries = [
        { value: "divider", label: "" },
        { value: "TR", label: "Turkey" },
        { value: "ES", label: "Spain" },
        { value: "US", label: "United States" },
      ];
      let removeCountries = ["TR", "ES", "US"];
      let newCountries = options.filter(
          (obj) => !removeCountries.includes(obj.value)
      );

      if (newCountries && newCountries.length) {
        for (let i = 0; i < specialCountries.length; i++) {
          let specialCountry = specialCountries[i];
          newCountries.unshift(specialCountry);
        }
      }
      setCountryOptions(newCountries);
    }
  }, [options]);


  const submitInfo = async (cardEl) => {
    try {
      if (!stripe || !elements) {
        return null;
      }
      const { error: submitError } = await elements.submit();
      if (submitError) return null;

      setPurchasing(true);

      const resp1 = await axios.post(
          process.env.NEXT_PUBLIC_PROCESS_PAYMENT as string,
          {
            amount: price,
            currency: "usd",
          }
      );

      const clientSecret = resp1.data.paymentIntent;

       const packCredits = packInfo?.questions || 0;
       const packName = packInfo?.questions ? `${packInfo.questions} Questions Pack` : (packInfo?.name || "Unknown");

      const resp: PaymentIntentResult = await stripe?.confirmCardPayment(
          `${clientSecret}`,
          {
            payment_method: {
              card: cardEl,
              billing_details: {
                name,
              },
            },
          }
      );

      if (resp.paymentIntent && resp.paymentIntent.status === "succeeded")
      {
        const langId = localStorage.getItem("HMWK_LANGUAGE") || "en";
        const language = nativeLanguageNames[langId as keyof typeof nativeLanguageNames];
          try {
          await addDocInCollection(
              "transactions",
              {
                  transactionId: resp.paymentIntent?.id || "",
                  userId: user?.id || "",
                  amount: price || 0,
                  pack: packName, 
                  credits: packCredits, 
                  language: language || "en",
                  transactionDate: new Date().getTime(),
                  month: new Date().getMonth() + 1,
              },
              true
          );
          } catch (transactionError) {
              console.error("Error adding transaction:", transactionError);
              // Still continue with other operations if possible
          }

                if (!user || !user.id) {
                    console.error("User object is missing or incomplete:", user);
                    AppNotify(t('user_object_problem'), "error");
                    setPurchasing(false);
                    return;
                }

        if (packPage) {
          await updateCollection(
              "users",
              user.id,
              {
                  questions: (user?.questions || 0) + packCredits
              },
              true
          );

          // @ts-ignore
            setUser({
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            revenue: user.revenue,
            id: user?.id as string,
            type: user.type || "user",
            phoneNumber: user.phoneNumber,
            isApplicant: user.isApplicant,
            applicantQuestionAnswered: user.applicantQuestionAnswered,
            pendingWithdrawals: user.pendingWithdrawals,
            billingAddress: user.billingAddress,
            earnings: user.earnings,
            certificateNumber: user.certificateNumber,
            dateOfBirth: user.dateOfBirth,
            isAvailable: user.isAvailable,
            questions: (user?.questions || 0) + packCredits,
            pendingWithdrawalAmount: user.pendingWithdrawalAmount,
            provider: user.provider as string,
            examsCompleted: user.examsCompleted,
            examsAttempted: user.examsAttempted,
            imgId: user.imgId,
            imgUrl: user.imgUrl,
            country: user.country || "",
            address1: user.address1 || "",
            address2: user.address2 || "",
            city: user.city || "",
            state: user.state || "",
            postalCode: user.postalCode || "",
          });

          setPurchasing(false);

                    router.push((`/${locale}/ask`));
                } else {
                    router.push((`/${locale}/ask`));
                }
            } else {
                handleClose && handleClose();
                setPurchasing(false);
                AppNotify(t('payment_failed'), "error");
            }
        } catch (error) {
            console.error("Payment processing error:", error);
            setPurchasing(false);
            AppNotify(t('payment_error_occurred'), "error");
        }
    };

  const generateStripeToken = async () => {
    if (!elements || !stripe) {
      return;
    }

    const cardNumberElement = elements.getElement(CardNumberElement);

    const { token, error } = await stripe.createToken(
        cardNumberElement as StripeCardNumberElement,
        {
          name,
        }
    );

    if (error || !token) {
      return;
    }

    await submitInfo(cardNumberElement);
    return token;
  };

  useEffect(() => {
    if (buyClicked) {
      setBuyClicked && setBuyClicked(false);
      generateStripeToken();
    }
  }, [buyClicked]);

  useEffect(() => {
    if (
        name !== "" &&
        cardNumber !== "" &&
        expMonth !== "" &&
        cvc !== "" &&
        country !== ""
    ) {
      setBtnDisable && setBtnDisable(false);
    } else {
      setBtnDisable && setBtnDisable(true);
    }
  }, [name, cardNumber, expMonth, cvc, country]);

  const ArrowComponent = () => (
      <span style={{marginRight:'10px', display: 'flex'}}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" clipRule="evenodd" d="M7.28626 9.30937C7.47355 9.12232 7.72742 9.01725 7.99212 9.01725C8.25682 9.01725 8.5107 9.12232 8.69799 9.30937L11.9876 12.5981L15.2771 9.30937C15.3686 9.21123 15.4789 9.13252 15.6014 9.07793C15.7239 9.02333 15.8562 8.99398 15.9903 8.99161C16.1244 8.98924 16.2576 9.01392 16.382 9.06415C16.5064 9.11439 16.6194 9.18916 16.7142 9.28401C16.8091 9.37886 16.8839 9.49185 16.9341 9.61622C16.9843 9.7406 17.009 9.87382 17.0066 10.0079C17.0043 10.1421 16.9749 10.2743 16.9203 10.3968C16.8657 10.5194 16.787 10.6296 16.6889 10.7211L12.6934 14.7165C12.5061 14.9036 12.2523 15.0087 11.9876 15.0087C11.7229 15.0087 11.469 14.9036 11.2817 14.7165L7.28626 10.7211C7.09921 10.5338 6.99414 10.2799 6.99414 10.0152C6.99414 9.75053 7.09921 9.49666 7.28626 9.30937Z" fill="#777777"/>
          </svg>
      </span>
  );

    const stripeElementStyle = {
        style: {
            base: {
                fontSize: "16px",
                fontWeight: "500",
                color: "#4b4b4b",
                fontFamily: "Arial, sans-serif",
                "::placeholder": {
                    color: "#aaa",
                },
                lineHeight: "26px",
            },
            invalid: {
                color: "#e5424d",
            },
        },
    };

    return (
        <div className={styles.paymentForm}>
            <>
                <div className={styles.formGroup}>
                    <label htmlFor="countryRegion">{t('payment.country_region')}</label>
                    <FormControl fullWidth>
                        <Select
                            labelId="demo-simple-select-label"
                            placeholder={t('payment.select_region')}
                            id="demo-simple-select"
                            size="small"
                            value={country}
                            onChange={(e) => setCountry(e.target.value)}
                            IconComponent={() => (
                                <SvgIcon component={ArrowComponent}/>
                            )}
                            sx={{
                                '&:hover .MuiOutlinedInput-notchedOutline': {
                                    border: '1px solid rgba(229, 229, 229, 1)',
                                },
                                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                    border: '1px solid rgba(229, 229, 229, 1)',
                                },
                                outline: "0 !important",
                                fontWeight: "500",
                                fontSize: "16px",
                                height: {xs: "40px", sm: "60px",},
                                borderColor: "rgba(229, 229, 229, 1)",
                                borderRadius: "12px",
                                background: "#fff",
                                color: 'rgba(75, 75, 75, 1)',
                                padding: '0 0 0 5px',
                            }}
                        >
                            {countryOptions.map(
                                (option: { label: string; value: string }) => (
                                    <MenuItem
                                        classes={{
                                            root:
                                                option.value === "divider" ? styles.menuDivider : "",
                                        }}
                                        key={option.value}
                                        value={option.label}
                                        disabled={option.value === "divider" ? true : false}
                                        divider={option.value === "divider" ? true : false}
                                    >
                                        {option.label}
                                    </MenuItem>
                                )
                            )}
                        </Select>
                        <div className={styles.helperText}>{t('payment.financial_account_helper')}</div>
                    </FormControl>
                </div>
                <div className={`${styles.formGroup} ${styles.ccInfo}`}>
                    <div className={styles.firstHeader}>
                        <h3><img src={'/icons/payment-right.svg'} alt={'payment-right'}/>{t('payment.credit_debit_card')}
                        </h3>
                        <div className={styles.cardIcons}>
                            <img src="/icons/visa-icon.svg" alt="Visa"/>
                            <img src="/icons/master-icon.svg" alt="master"/>
                        </div>
                    </div>

                    <div className={styles.innerContainer}>
                        <h3>{t('payment.payment_method')}</h3>
                        <div>
                            <label htmlFor="name">{t('payment.name_on_card')}</label>

                            <InputTextField
                                label={''}
                                placeholder={t('payment.name_on_card_placeholder')}
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                                fullWidth={true}
                            />
                        </div>
                        <div>
                            <label htmlFor="cardNumber">{t('payment.card_number')}</label>
                            <CardNumberElement
                                id="card-nr"
                                options={stripeElementStyle}
                                onChange={(e) => e.empty ? setCardNumber("") : setCardNumber("a")}
                                className={styles.cardInput}
                            />
                        </div>
                        <div className={styles.expiryCvc}>
                            <div className={styles.cvc}>
                                <label htmlFor="Expiry">{t('payment.expiration_date')}</label>
                                <CardExpiryElement
                                    id="card-expiry"
                                    options={stripeElementStyle}
                                    onChange={(e) => e.empty ? setExpMonth("") : setExpMonth("a")}
                                    className={styles.cardInput}
                                />
                            </div>
                            <div className={styles.cvc}>
                                <label htmlFor="securityCode">{t('cvc')} <Tooltip arrow={true}
                                                                                     title={t('cvc_tooltip')}
                                                                                     placement="top-start"><img
                                    src={'/icons/info-card-icon.svg'} alt={'info-card-icon'}/></Tooltip></label>
                                <div className={styles.cardInputWrap}>
                                    <CardCvcElement
                                        id="cvc"
                                        className={styles.cardInput}
                                        options={stripeElementStyle}
                                        onChange={(e) => (e.empty ? setCvc("") : setCvc("a"))}
                                    />
                                    <img src={'/icons/card-icon.svg'} alt={'card icon'}/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className={styles.btnContainer}>
                        <PrimaryButton
                            size={"greenBtn"}
                            disabled={btnDisable || purchasing}
                            onClick={() => generateStripeToken()}
                            label={purchasing ? t('common.processing') : t('payment.buy_now')}
                            style={{width: '290px'}}
                        />
                    </div>
                </div>
            </>
        </div>
    );
}

export default ClientPaymentForm;