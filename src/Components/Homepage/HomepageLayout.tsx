"use client";
import React, {useContext, useEffect, useState} from "react";
import dynamic from 'next/dynamic';
import HeroSection from "@/src/Components/Homepage/sections/HeroSection";
import Footer from "../Navigation/Footer";
import Navbar from "../Navigation/Navbar";
import {UserContext} from "@/src/Contexts/UserContext";
import {useTranslation} from "@/src/Utils/i18n";
import styles from "@/src/styles/navbar.module.css";
import {isStudentAuthUser} from "@/src/Firebase/firebase.utils";

const SubjectsSection = dynamic(() => import("@/src/Components/Homepage/sections/SubjectsSection"), {
    loading: () => <div className="section-loading"></div>
});

const HowItWorksSection = dynamic(() => import("@/src/Components/Homepage/sections/HowItWorksSection"), {
    loading: () => <div className="section-loading"></div>
});

const RecentQuestions = dynamic(() => import("@/src/Components/Homepage/sections/RecentQuestionsSection"), {
    loading: () => <div className="section-loading"></div>
});

const MeetTutorsSection = dynamic(() => import("@/src/Components/Homepage/sections/MeetTutorsSection"), {
    loading: () => <div className="section-loading"></div>
});

const BecomeTutorSection = dynamic(() => import("@/src/Components/Homepage/sections/BecomeTutorSection"), {
    loading: () => <div className="section-loading"></div>
});

const PricingSection = dynamic(() => import("@/src/Components/Homepage/sections/PricingSection"), {
    loading: () => <div className="section-loading"></div>
});

const TopGradeSection = dynamic(() => import("@/src/Components/Homepage/sections/TopGradeSection"), {
    loading: () => <div className="section-loading"></div>
});

const FAQSection = dynamic(() => import("@/src/Components/Homepage/sections/FAQSection"), {
    loading: () => <div className="section-loading"></div>
});

interface HomepageProps {
    id?: string;
}

const Homepage: React.FC<HomepageProps> = ({id}) =>
{
    const {user} = useContext(UserContext);
    const {t} = useTranslation();
    const [pageScrolled, setPageScrolled] = useState(false);
    const [activeTab, setActiveTab] = useState(0);
    const [isPaidUser, setIsPaidUser] = useState(false);

    useEffect(() => {
        if (user.id !== "" && user.type === 'user') {
            const checkPaidStatus = async () => {
                const value = await isStudentAuthUser(user.id);
                setIsPaidUser(value);
            };
            checkPaidStatus();
        }
    }, [user]);

    useEffect(() => {
        let ticking = false;

        const scrollFunc = () => {
            if (!ticking && !pageScrolled) {
                requestAnimationFrame(() => {
                    setPageScrolled(true);
                    ticking = false;
                });
                ticking = true;
            }
        };

        window.addEventListener("scroll", scrollFunc, {passive: true});

        return () => {
            window.removeEventListener("scroll", scrollFunc);
        };
    }, [pageScrolled]);

    return (
        <>
            {/* Add Navbar component */}
            <Navbar
                active={activeTab}
                setActiveTab={setActiveTab}
                isAdmin={user?.type === 'admin'}
                isPaidUser={isPaidUser}
                isPublicPage={false}
            />

            <div className={`${styles.homePageContent} ${styles.homeWidgets}`}>
                <HeroSection
                    title={t(`homepage.homework_help_for_grades_1-8`)}
                    subtitle={t('homepage.subtitle')}
                    image="/images/girl-desk.png"
                    buttonText={t('homepage.start_test')}
                    buttonColor="primary"
                    buttonPath={user?.id === "" ? "/signup?redirect_to=welcome" : "/welcome"}
                />

                <div id="subjects-section">
                    <SubjectsSection
                        title={t('homepage.features')}
                        text={t('homepage.subjects_description')}
                    />
                </div>

                <div id="how-it-works-section">
                    <HowItWorksSection
                        title={t('homepage.how_it_works')}
                        text={t('homepage.how_it_works_description')}
                    />
                </div>

                <RecentQuestions/>

                <MeetTutorsSection/>

                <BecomeTutorSection
                    title={t('homepage.become_a_homework_hero')}
                    text={t('homepage.become_tutor_description')}
                    t={t}
                />

                <div id="pricing-section">
                    <PricingSection t={t} />
                </div>

                <TopGradeSection t={t} />

                <FAQSection t={t}  />

                <Footer id={id} userType={user?.type || ''} t={t} />
            </div>
        </>
    );
};

export default Homepage;