import React, {FC} from "react";
import {useRouter} from "next/navigation";
import Container from "@mui/material/Container";
import styles from "@/src/styles/home.module.css";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import { useTranslation } from "@/src/Utils/i18n";

interface BecomeTutorSectionProps {
    title?: string;
    text?: string;
    t: (key: string, params?: Record<string, string | number>) => string
}

const BecomeTutorSection: FC<BecomeTutorSectionProps> = ({title, text ,t}) => {
    const router = useRouter();
    const {locale} = useTranslation();


    const redirectToSignup = () => {
        router.push(`/${locale}/signup`);
    }

    return (
        <div className={`${styles.curveBgWrapper} ${styles.becomeTutorBox}`}>
            <img src={'/images/top-edge-bg.png'} alt={'top-edge-bg'}/>
            <div className={styles.becomeTutorWrapper}>
                <Container maxWidth={false} className="mainContainer">
                    <div className={styles.becomeInfoBox}>
                        {title && <h2 className={styles.sectionTitle}>{title}</h2>}
                        {text && <div className={styles.sectionText}>{text}</div>}
                     
                            <a style={{textDecoration: 'none'}}>
                                <PrimaryButton onClick={redirectToSignup} label= {t('homepage.become_tutor')} />
                            </a>
                    </div>
                </Container>
                <img src={'/images/characters.png'} alt={'Characters'}  loading="lazy"/>
            </div>
            <img src={'/images/bottom-edge-bg.png'} alt={'top-edge-bg'}/>
        </div>
    );
};

export default BecomeTutorSection;