import React, {useState} from 'react';
import Container from "@mui/material/Container";
import styles from "@/src/styles/home.module.css";

type FaqItem = {
    question: string;
    answer: string;
};
interface FAQSectionProps {
    t: (key: string, params?: Record<string, string | number>) => string}

const FAQSection: React.FC <FAQSectionProps>= ({t}) => {
    const [openIndex, setOpenIndex] = useState<number | null>(null);

    const toggle = (index: number) => {
        setOpenIndex(openIndex === index ? null : index);
    };

    const faqData: FaqItem[] = [
        {
            question: t('faq_section.q1'),
            answer: t('faq_section.a1'),
        },
        {
            question: t('faq_section.q2'),
            answer: t('faq_section.a2'),
        },
        {
            question: t('faq_section.q3'),
            answer: t('faq_section.a3'),
        },
        {
            question: t('faq_section.q4'),
            answer: t('faq_section.a4'),
        },
        {
            question: t('faq_section.q5'),
            answer: t('faq_section.a5'),
        },
        {
            question: t('faq_section.q6'),
            answer: t('faq_section.a6'),
        },
    ];

    return (
        <div className={styles.faqQuestionsBox}>
            <Container maxWidth={false} className="mainContainer">
                <h2 className={styles.sectionTitle}>we get these questions a lot</h2>
                <div className={styles.faqListing}>
                    {faqData.map((item, index) => (
                        <div key={index} className={styles.faqItem}>
                            <div
                                className={`${styles.faqQuestion} ${openIndex === index ? styles.faqQuestionActive : ''}`}
                                onClick={() => toggle(index)}>
                                {item.question}
                                <img src={'/icons/faq-static-toggle-icon.svg'} alt={'faq-toggle-icon'}
                                     className={`${styles.faqChevron} ${openIndex === index ? '' : styles.faqChevronOpen}`}/>
                            </div>
                            {openIndex === index && item.answer && (
                                <div className={styles.faqAnswer}>{item.answer}</div>
                            )}
                        </div>
                    ))}
                </div>
            </Container>
        </div>
    );
}

export default FAQSection;