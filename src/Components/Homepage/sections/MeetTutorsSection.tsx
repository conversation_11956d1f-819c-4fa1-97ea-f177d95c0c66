// @ts-nocheck
import React from "react";
import Slider, { Setting<PERSON> } from 'react-slick';
import Container from "@mui/material/Container";
import { useTranslation } from "@/src/Utils/i18n";

import styles from "@/src/styles/home.module.css";

const tutors = [
    {
        name: '<PERSON>',
        rating: 4.9,
        answers: 3025,
        degree: `Master's Degree`,
        title: 'Teacher',
        location: 'United States',
        certification: 'ACPT certified tutor',
        experience: '5 years tutor experience',
        image: '/images/tutors/<PERSON>-<PERSON>.png',
    },
    {
        name: '<PERSON><PERSON>',
        rating: 4.8,
        answers: 1572,
        degree: 'High School',
        title: 'Student',
        location: 'Turkiye',
        certification: 'ACPT certified tutor',
        experience: '2 years tutor experience',
        image: '/images/tutors/Fatma-Koc.png',
    },
    {
        name: '<PERSON><PERSON>',
        rating: 4.9,
        answers: 2353,
        degree: `Bachelor's Degree`,
        title: 'Web Developer',
        location: 'India',
        certification: 'ACPT certified tutor',
        experience: '3 years tutor experience',
        image: '/images/tutors/<PERSON><PERSON>-<PERSON>.png',
    },
    {
        name: '<PERSON>',
        rating: 4.7,
        answers: 1629,
        degree: 'High School',
        title: 'Student',
        location: 'United States',
        certification: 'ACPT certified tutor',
        experience: '2 years tutor experience',
        image: '/images/tutors/Caleb-<PERSON>.png',
    },
    {
        name: 'Emre Yilmaz',
        rating: 4.6,
        answers: 1594,
        degree: 'High School',
        title: 'Student',
        location: 'Turkiye',
        certification: 'ACPT certified tutor',
        experience: '1 year tutor experience',
        image: '/images/tutors/Emre-Yilmaz.png',
    },
    {
        name: 'Juliana Gomes',
        rating: 4.8,
        answers: 2163,
        degree: `Master's Degree`,
        title: 'Teacher',
        location: 'Brazil',
        certification: 'ACPT certified tutor',
        experience: '3 years tutor experience',
        image: '/images/tutors/Juliana-Gomes.png',
    },
    {
        name: 'Kento Tanaka',
        rating: 4.8,
        answers: 2438,
        degree: `Bachelor's Degree`,
        title: 'Web Designer',
        location: 'Japan',
        certification: 'ACPT certified tutor',
        experience: '3 years tutor experience',
        image: '/images/tutors/Kento-Tanaka.png',
    },
    {
        name: 'Laxmi Bhatt',
        rating: 4.7,
        answers: 1797,
        degree: 'High School',
        title: 'Student',
        location: 'India',
        certification: 'ACPT certified tutor',
        experience: '1 year tutor experience',
        image: '/images/tutors/Laxmi-Bhatt.png',
    },
    {
        name: 'Mehmet Dogan',
        rating: 4.7,
        answers: 2620,
        degree: `Bachelor's Degree`,
        title: 'Teacher',
        location: 'Turkiye',
        certification: 'ACPT certified tutor',
        experience: '3 years tutor experience',
        image: '/images/tutors/Mehmet-Dogan.png',
    },
    {
        name: 'Ana Ferreira',
        rating: 4.6,
        answers: 1506,
        degree: 'High School',
        title: 'Student',
        location: 'Brazil',
        certification: 'ACPT certified tutor',
        experience: '1 year tutor experience',
        image: '/images/tutors/Ana-Ferreira.png',
    },
    {
        name: 'Kevin Morgan',
        rating: 4.9,
        answers: 2848,
        degree: `Master's Degree`,
        title: 'Teacher',
        location: 'United States',
        certification: 'ACPT certified tutor',
        experience: '4 years tutor experience',
        image: '/images/tutors/Kevin-Morgan.png',
    },
];

const TutorCard = ({ t, tutorInfo }: { t: any, tutorInfo: any }) => (
    <div className={styles.tutorCardCard}>
        <div className={styles.tutorCardHead}>
            <div className={styles.tutorCardAvatar}>
                {tutorInfo.image && (
                    <img src={tutorInfo.image} alt={tutorInfo.name} style={{ width: '100%', height: '100%', borderRadius: '18%', objectFit: 'cover' }} />
                )}
            </div>
            <div className={styles.tutorCardNameRow}>
                <span className={styles.tutorCardName}>{tutorInfo.name}</span>
                <div className={styles.tutorCardStars}>
                    {Array(5)
                        .fill(null)
                        .map((_, i) => (
                            <span key={i} className={styles.tutorCardStar}>
                                <img src={'/icons/home/<USER>'} alt={'star-icon'}/>
                            </span>
                        ))}
                    <span className={styles.tutorCardRating}>{tutorInfo.rating}</span>
                </div>
                <div className={styles.tutorCardAnswers}>
                    <span className={styles.tutorCardAnswerCount}>{tutorInfo.answers.toLocaleString()}</span>
                    <span className={styles.tutorCardAnswerOn}> {t('meet_tutors.answers_this_month')}</span>
                </div>
            </div>
        </div>
        <div className={styles.tutorCardDetails}>
            <div><img src={'/icons/home/<USER>'} alt={'mastercap-icon'}/> {t('meet_tutors.degree', { degree: tutorInfo.degree })}</div>
            <div><img src={'/icons/home/<USER>'} alt={'briefcase-icon'}/> {t('meet_tutors.title', { title: tutorInfo.title })}</div>
            <div><img src={'/icons/home/<USER>'} alt={'location-icon'}/> {t('meet_tutors.location', { location: tutorInfo.location })}</div>
            <div><img src={'/icons/home/<USER>'} alt={'certified-icon'}/> {t('meet_tutors.certification', { certification: tutorInfo.certification })}</div>
            <div><img src={'/icons/home/<USER>'} alt={'clock-icon'}/> {t('meet_tutors.experience', { experience: tutorInfo.experience })}</div>
        </div>
    </div>
);

const MeetTutorsSection = () => {
    const { t } = useTranslation();
    const sliderRef = React.useRef(null) as React.MutableRefObject<any>;

    // Optimized settings for mobile
    const settings: Settings = {
        dots: false,
        infinite: true,
        speed: 600, // Faster speed for snappier feel
        slidesToShow: 3,
        slidesToScroll: 1,
        arrows: false,
        autoplay: false,
        swipeToSlide: true, // Allow swiping to any slide
        touchThreshold: 10, // More sensitive touch
        swipe: true,
        accessibility: true,
        pauseOnHover: false,
        useCSS: true,
        useTransform: true,
        cssEase: 'cubic-bezier(0.25, 0.1, 0.25, 1)', // Smooth easing
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 2,
                    speed: 250,
                }
            },
            {
                breakpoint: 659,
                settings: {
                    slidesToShow: 1,
                    centerMode: true,
                    centerPadding: "20px", // Slightly more padding
                    speed: 200,
                    touchThreshold: 5, // Even more sensitive on small screens
                    edgeFriction: 0.15, // Smoother edge resistance
                }
            },
        ],
    };

    // @ts-ignore
    return (
        <div className={styles.meetTutorsBox}>
            <Container maxWidth={false} className="mainContainer">
                <div className={styles.sectionHead}>
                    <h2 className={styles.sectionTitle}>{t('meet_tutors.title_main')} <span
                        className={styles.tutorCardHighlight}>{t('meet_tutors.heroes')}</span></h2>
                    <div className={styles.sectionText}>{t('meet_tutors.intro_1')}<br/> {t('meet_tutors.intro_2')}</div>
                </div>

                <Slider ref={sliderRef} {...settings} className={`${styles.tutorCardSlider} tutorCardSlider`}>
                    {tutors.map((tutor, i) => (
                        <TutorCard key={i} t={t} tutorInfo={tutor} />
                    ))}
                </Slider>

                <div className={styles.tutorCardNavBottom}>
                    <button
                        onClick={() => sliderRef.current?.slickPrev()}
                        className={styles.tutorCardArrow}
                        aria-label={t('meet_tutors.prev_tutor')}
                    >
                        <img src={'/icons/home/<USER>'} alt={t('meet_tutors.prev')}/>
                    </button>
                    <button
                        onClick={() => sliderRef.current?.slickNext()}
                        className={styles.tutorCardArrow}
                        aria-label={t('meet_tutors.next_tutor')}
                    >
                        <img src={'/icons/home/<USER>'} alt={t('meet_tutors.next')}/>
                    </button>
                </div>
            </Container>
        </div>
    );
};

export default MeetTutorsSection;