import React from "react";
import Container from "@mui/material/Container";
import styles from "@/src/styles/home.module.css";
import {useTranslation} from "@/src/Utils/i18n";

const RecentQuestions = () => {
    const {t} = useTranslation();
    
    const questions = [
        {
            question: t('homepage.sample_questions.question_1'),
            answer: t('homepage.sample_questions.answer_1'),
            subject: t('homepage.sample_questions.subject_1'),
            subjectClass: styles.recentQuesMath,
        },
        {
            question: t('homepage.sample_questions.question_2'),
            answer: t('homepage.sample_questions.answer_2'),
            subject: t('homepage.sample_questions.subject_2'),
            subjectClass: styles.recentQuesScience,
        },
        {
            question: t('homepage.sample_questions.question_3'),
            answer: t('homepage.sample_questions.answer_3'),
            subject: t('homepage.sample_questions.subject_3'),
            subjectClass: styles.recentQuesLanguageArts,
        },
        {
            question: t('homepage.sample_questions.question_4'),
            answer: t('homepage.sample_questions.answer_4'),
            subject: t('homepage.sample_questions.subject_4'),
            subjectClass: styles.recentQuesSocialStudies,
        },
        {
            question: t('homepage.sample_questions.question_5'),
            answer: t('homepage.sample_questions.answer_5'),
            subject: t('homepage.sample_questions.subject_5'),
            subjectClass: styles.recentQuesMath,
        },
        {
            question: t('homepage.sample_questions.question_6'),
            answer: t('homepage.sample_questions.answer_6'),
            subject: t('homepage.sample_questions.subject_6'),
            subjectClass: styles.recentQuesScience,
        },
    ];

    return (
        <div className={`${styles.curveBgWrapper} ${styles.recentQuesBox}`}>
            <img src={'/images/top-edge-bg.png'} alt={'top-edge-bg'}/>
            <div className={styles.recentQuesWrapper}>
                <Container maxWidth={false} className="mainContainer">
                    <div className={styles.sectionHead}>
                        <h2 className={styles.sectionTitle}>{t('homepage.recent_questions_title')}</h2>
                        <div className={styles.sectionText}>{t('homepage.recent_questions_description')}</div>
                    </div>
                    <div className={styles.recentQuesGrid}>
                        {questions.map((q, index) => (
                            <div key={index} className={styles.recentQuesCard}>
                                <div className={styles.recentQuesCardInfo}>
                                    <div className={styles.recentQuesQSection}>
                                        <span className={styles.recentQuesQLabel}>Q</span>
                                        <div className={styles.recentQuesQTitle}>{q.question}</div>
                                    </div>
                                    <div className={styles.recentQuesASection}>
                                        <span className={styles.recentQuesALabel}>A</span>
                                        <p>{q.answer}</p>
                                    </div>
                                </div>
                                <div className={`${styles.recentQuesSubject} ${q.subjectClass}`}>
                                    {q.subject}
                                </div>
                            </div>
                        ))}
                    </div>
                </Container>
            </div>
            <img src={'/images/bottom-edge-bg.png'} alt={'top-edge-bg'}/>
        </div>
    );
};

export default RecentQuestions;
