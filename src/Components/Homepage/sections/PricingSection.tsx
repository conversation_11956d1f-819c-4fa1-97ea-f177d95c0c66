// @ts-nocheck
import React, {useState} from 'react';
import Slider, { Settings } from 'react-slick';
import {useRouter} from "next/navigation";
import Container from "@mui/material/Container";
import styles from "@/src/styles/home.module.css";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import {pricingPlans} from "@/src/Utils/helpers";
import {isUserLoggedIn} from "@/src/Firebase/firebase.utils";
import { useTranslation } from '@/src/Utils/i18n';

type SVGIconProps = {
    color?: string;
};

const CheckIcon: React.FC<SVGIconProps> = ({
   color = "#6BCC09",
}) => (
    <svg width="25" height="26" viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M24.2229 7.88269C23.9032 7.22117 23.3998 6.6657 22.7728 6.28269L14.6853 1.28269C14.0921 0.91517 13.4082 0.720459 12.7103 0.720459C12.0125 0.720459 11.3285 0.91517 10.7353 1.28269L2.64785 6.28269C2.02958 6.67379 1.53922 7.2372 1.23716 7.90351C0.935097 8.56982 0.834531 9.30994 0.947848 10.0327L3.03535 22.5327C3.18464 23.3983 3.63306 24.184 4.30239 24.7528C4.97172 25.3217 5.8195 25.6375 6.69785 25.6452H18.7103C19.6182 25.642 20.4986 25.3338 21.2103 24.7702C21.882 24.1985 22.3257 23.4044 22.4603 22.5327L24.5603 10.0327C24.6629 9.29842 24.5454 8.55024 24.2229 7.88269ZM18.4103 12.4827L12.9853 17.8952C12.6672 18.2201 12.2584 18.4417 11.8125 18.5309C11.3665 18.62 10.904 18.5728 10.4853 18.3952C10.2086 18.2794 9.95789 18.1094 9.74785 17.8952L7.06035 15.2077C6.92706 15.0963 6.81843 14.9584 6.74137 14.8027C6.66432 14.647 6.62052 14.477 6.61278 14.3034C6.60503 14.1299 6.63351 13.9566 6.69639 13.7947C6.75927 13.6328 6.85518 13.4857 6.97802 13.3629C7.10085 13.24 7.24791 13.1441 7.40985 13.0812C7.57178 13.0184 7.74503 12.9899 7.91857 12.9976C8.09211 13.0054 8.26214 13.0492 8.41782 13.1262C8.57351 13.2033 8.71145 13.3119 8.82285 13.4452L11.3228 15.9452L16.5853 10.6827C16.8207 10.4473 17.14 10.3151 17.4728 10.3151C17.8057 10.3151 18.125 10.4473 18.3603 10.6827C18.5957 10.9181 18.728 11.2373 18.728 11.5702C18.728 11.9031 18.5957 12.2223 18.3603 12.4577L18.4103 12.4827Z" fill={color}/>
    </svg>
);

const CalculateIcon: React.FC<SVGIconProps> = ({
    color = "#6BCC09",
}) => (
    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9.37326 0.682617H16.1245C17.5245 0.682617 18.6348 0.682617 19.5284 0.755694C20.4438 0.830053 21.2169 0.986463 21.9245 1.34544C23.0702 1.92985 24.0015 2.862 24.5848 4.00826C24.9451 4.71339 25.1015 5.48775 25.1758 6.40313C25.2489 7.29672 25.2489 8.40698 25.2489 9.80698V16.5583C25.2489 17.9583 25.2489 19.0685 25.1758 19.9621C25.1015 20.8775 24.9451 21.6506 24.5861 22.3583C24.002 23.5038 23.0703 24.435 21.9245 25.0185C21.2169 25.3788 20.4438 25.5352 19.5284 25.6095C18.6348 25.6826 17.5245 25.6826 16.1245 25.6826H9.37326C7.97326 25.6826 6.863 25.6826 5.96941 25.6095C5.05403 25.5352 4.28095 25.3788 3.57454 25.0198C2.42857 24.4359 1.49687 23.5042 0.913004 22.3583C0.552747 21.6506 0.396337 20.8775 0.321978 19.9621C0.248901 19.0685 0.248901 17.9583 0.248901 16.5583V9.80698C0.248901 8.40698 0.248901 7.29672 0.321978 6.40313C0.396337 5.48775 0.552747 4.71467 0.911722 4.00826C1.49592 2.86209 2.42809 1.93037 3.57454 1.34672C4.27967 0.986463 5.05403 0.830053 5.96941 0.755694C6.863 0.682617 7.97326 0.682617 9.37326 0.682617ZM9.54377 6.6121C9.54377 6.35709 9.44247 6.11252 9.26215 5.93219C9.08182 5.75187 8.83725 5.65057 8.58224 5.65057C8.32722 5.65057 8.08265 5.75187 7.90233 5.93219C7.722 6.11252 7.6207 6.35709 7.6207 6.6121V8.21467H6.01813C5.76312 8.21467 5.51855 8.31597 5.33822 8.4963C5.1579 8.67662 5.05659 8.92119 5.05659 9.17621C5.05659 9.43122 5.1579 9.67579 5.33822 9.85612C5.51855 10.0364 5.76312 10.1377 6.01813 10.1377H7.6207V11.7403C7.6207 11.9953 7.722 12.2399 7.90233 12.4202C8.08265 12.6005 8.32722 12.7018 8.58224 12.7018C8.83725 12.7018 9.08182 12.6005 9.26215 12.4202C9.44247 12.2399 9.54377 11.9953 9.54377 11.7403V10.1377H11.1463C11.4014 10.1377 11.6459 10.0364 11.8262 9.85612C12.0066 9.67579 12.1079 9.43122 12.1079 9.17621C12.1079 8.92119 12.0066 8.67662 11.8262 8.4963C11.6459 8.31597 11.4014 8.21467 11.1463 8.21467H9.54377V6.6121ZM14.9925 6.6121C14.7375 6.6121 14.4929 6.71341 14.3126 6.89373C14.1323 7.07406 14.031 7.31863 14.031 7.57364C14.031 7.82866 14.1323 8.07323 14.3126 8.25355C14.4929 8.43388 14.7375 8.53518 14.9925 8.53518H19.4797C19.7347 8.53518 19.9793 8.43388 20.1596 8.25355C20.3399 8.07323 20.4412 7.82866 20.4412 7.57364C20.4412 7.31863 20.3399 7.07406 20.1596 6.89373C19.9793 6.71341 19.7347 6.6121 19.4797 6.6121H14.9925ZM14.031 10.7788C14.031 11.3095 14.4617 11.7403 14.9925 11.7403H19.4797C19.7347 11.7403 19.9793 11.639 20.1596 11.4587C20.3399 11.2784 20.4412 11.0338 20.4412 10.7788C20.4412 10.5238 20.3399 10.2792 20.1596 10.0989C19.9793 9.91854 19.7347 9.81723 19.4797 9.81723H14.9925C14.7375 9.81723 14.4929 9.91854 14.3126 10.0989C14.1323 10.2792 14.031 10.5238 14.031 10.7788ZM16.1207 15.4467C16.0327 15.3523 15.9265 15.2765 15.8086 15.2239C15.6906 15.1714 15.5633 15.1431 15.4342 15.1408C15.3051 15.1386 15.1768 15.1623 15.0571 15.2107C14.9374 15.259 14.8286 15.331 14.7373 15.4223C14.646 15.5136 14.574 15.6224 14.5257 15.7421C14.4773 15.8618 14.4536 15.9901 14.4558 16.1192C14.4581 16.2483 14.4864 16.3756 14.5389 16.4936C14.5915 16.6115 14.6673 16.7177 14.7617 16.8057L15.8951 17.9403L14.7617 19.0736C14.6673 19.1617 14.5915 19.2678 14.5389 19.3858C14.4864 19.5037 14.4581 19.631 14.4558 19.7602C14.4536 19.8893 14.4773 20.0175 14.5257 20.1372C14.574 20.257 14.646 20.3657 14.7373 20.457C14.8286 20.5483 14.9374 20.6203 15.0571 20.6687C15.1768 20.717 15.3051 20.7408 15.4342 20.7385C15.5633 20.7362 15.6906 20.708 15.8086 20.6554C15.9265 20.6029 16.0327 20.5271 16.1207 20.4326L17.254 19.2993L18.3874 20.4326C18.5696 20.6025 18.8107 20.6949 19.0598 20.6905C19.3089 20.6861 19.5466 20.5852 19.7228 20.4091C19.8989 20.2329 19.9999 19.9952 20.0043 19.7461C20.0087 19.497 19.9162 19.2559 19.7463 19.0736L18.6143 17.9403L19.7476 16.807C19.9175 16.6247 20.0099 16.3836 20.0055 16.1345C20.0011 15.8854 19.9002 15.6477 19.7241 15.4716C19.5479 15.2954 19.3102 15.1945 19.0611 15.1901C18.812 15.1857 18.5709 15.2782 18.3886 15.448L17.254 16.5801L16.1207 15.4467ZM5.37711 17.8301C5.37711 18.3608 5.80788 18.7916 6.33864 18.7916H10.8258C11.0808 18.7916 11.3254 18.6903 11.5057 18.51C11.6861 18.3296 11.7874 18.0851 11.7874 17.8301C11.7874 17.575 11.6861 17.3305 11.5057 17.1501C11.3254 16.9698 11.0808 16.8685 10.8258 16.8685H6.33864C6.08363 16.8685 5.83906 16.9698 5.65873 17.1501C5.47841 17.3305 5.37711 17.575 5.37711 17.8301Z" fill={color}/>
    </svg>
);

const AtoZIcon: React.FC<SVGIconProps> = ({
    color = "#6BCC09",
}) => (
    <svg width="22" height="26" viewBox="0 0 22 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M0.481079 21.4415V4.70047C0.481079 3.63487 0.904388 2.61291 1.65788 1.85942C2.41138 1.10593 3.43333 0.682617 4.49894 0.682617L16.9989 0.682617C18.0645 0.682617 19.0865 1.10593 19.84 1.85942C20.5935 2.61291 21.0168 3.63487 21.0168 4.70047V18.5398C21.0168 18.895 20.8757 19.2356 20.6245 19.4868C20.3734 19.7379 20.0327 19.879 19.6775 19.879H19.2311V23.004H19.6775C20.0327 23.004 20.3734 23.1451 20.6245 23.3963C20.8757 23.6475 21.0168 23.9881 21.0168 24.3433C21.0168 24.6985 20.8757 25.0392 20.6245 25.2903C20.3734 25.5415 20.0327 25.6826 19.6775 25.6826H4.72215C3.59735 25.6826 2.51862 25.2358 1.72326 24.4404C0.927905 23.6451 0.481079 22.5663 0.481079 21.4415ZM16.5525 19.879H4.72215C4.30775 19.879 3.91032 20.0437 3.6173 20.3367C3.32427 20.6297 3.15965 21.0271 3.15965 21.4415C3.15965 21.8559 3.32427 22.2534 3.6173 22.5464C3.91032 22.8394 4.30775 23.004 4.72215 23.004H16.5525V19.879ZM17.5525 9.20405C17.4772 9.05874 17.3635 8.93691 17.2236 8.85188C17.0838 8.76685 16.9233 8.72189 16.7597 8.7219H12.6275C12.3907 8.7219 12.1636 8.81597 11.9962 8.98341C11.8287 9.15086 11.7347 9.37796 11.7347 9.61476C11.7347 9.85156 11.8287 10.0787 11.9962 10.2461C12.1636 10.4135 12.3907 10.5076 12.6275 10.5076H15.0347L11.64 15.3005C11.5454 15.4341 11.4893 15.5912 11.4779 15.7545C11.4665 15.9179 11.5003 16.0812 11.5755 16.2267C11.6507 16.3721 11.7645 16.4941 11.9044 16.5792C12.0442 16.6644 12.2048 16.7094 12.3686 16.7094H17.0186C17.2554 16.7094 17.4825 16.6153 17.6499 16.4479C17.8174 16.2804 17.9114 16.0533 17.9114 15.8165C17.9114 15.5797 17.8174 15.3526 17.6499 15.1852C17.4825 15.0178 17.2554 14.9237 17.0186 14.9237H14.0954L17.4882 10.1308C17.5832 9.99729 17.6397 9.84018 17.6513 9.67671C17.663 9.51324 17.6276 9.34971 17.5525 9.20405ZM8.63286 5.37905C8.52538 5.05925 8.32019 4.78128 8.04625 4.58435C7.77232 4.38742 7.44346 4.28148 7.10608 4.28148C6.7687 4.28148 6.43984 4.38742 6.1659 4.58435C5.89197 4.78128 5.68678 5.05925 5.57929 5.37905L4.37036 9.00405L4.35787 9.04333L3.67394 11.0898C3.63677 11.2011 3.6219 11.3188 3.63019 11.4359C3.63848 11.553 3.66975 11.6674 3.72224 11.7724C3.77472 11.8775 3.84738 11.9712 3.93606 12.0481C4.02475 12.1251 4.12773 12.1838 4.23912 12.221C4.46408 12.2961 4.70964 12.2787 4.92179 12.1727C5.02684 12.1202 5.12052 12.0476 5.19749 11.9589C5.27445 11.8702 5.3332 11.7672 5.37037 11.6558L5.85251 10.1987H8.35608L8.84179 11.6558C8.91686 11.8806 9.07812 12.0663 9.29011 12.1721C9.50209 12.2779 9.74743 12.2952 9.97215 12.2201C10.1969 12.1451 10.3826 11.9838 10.4884 11.7718C10.5942 11.5598 10.6115 11.3145 10.5364 11.0898L9.85429 9.04333L9.84001 9.00405L8.63286 5.37905ZM6.44537 8.41119L7.10251 6.44333L7.75786 8.41119H6.44537Z" fill={color}/>
    </svg>
);

const ScienceIcon: React.FC<SVGIconProps> = ({
    color = "#6BCC09",
}) => (
    <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M17.7992 1.80948C18.1264 0.314896 20.2556 0.305957 20.5971 1.79876L20.6114 1.86848L20.6436 2.00793C21.0369 3.67772 22.3884 4.95241 24.0797 5.2474C25.6386 5.51914 25.6386 7.75745 24.0797 8.02919C23.2484 8.17483 22.4785 8.56238 21.8664 9.14335C21.2542 9.72433 20.827 10.4729 20.6382 11.2955L20.5953 11.4778C20.2556 12.9706 18.1281 12.9617 17.7992 11.4671L17.7652 11.3098C17.5848 10.4836 17.1622 9.7299 16.5514 9.14494C15.9407 8.55999 15.1695 8.1703 14.3363 8.02562C12.7809 7.75566 12.7809 5.52093 14.3363 5.25097C15.1665 5.10689 15.9352 4.71956 16.545 4.13807C17.1548 3.55657 17.5783 2.80711 17.7617 1.98469L17.7867 1.86669L17.7992 1.80948ZM19.0989 14.5349C18.2474 14.5166 17.4339 14.1791 16.8195 13.5892C16.686 13.6797 16.5687 13.7921 16.4727 13.9217C15.6682 14.9873 14.726 16.076 13.6605 17.1398C12.8524 17.9478 12.0336 18.6826 11.2255 19.3387C10.4174 18.6826 9.59862 17.9478 8.79054 17.1398C8.01667 16.3666 7.28272 15.5545 6.59156 14.7066C7.24768 13.8967 7.98425 13.0797 8.79054 12.2716C9.79589 11.2599 10.8712 10.3201 12.0086 9.45942C12.1261 9.3732 12.2298 9.2695 12.3161 9.15192C11.9762 8.84205 11.7039 8.46539 11.5163 8.04549C11.3286 7.62559 11.2296 7.17149 11.2255 6.7116C9.47169 5.48339 7.73753 4.5734 6.18931 4.09964C4.51237 3.58654 2.5458 3.44888 1.25681 4.73609C0.421912 5.57277 0.194863 6.70981 0.259223 7.79142C0.323583 8.87482 0.686504 10.0655 1.23535 11.274C1.79345 12.4775 2.46197 13.6266 3.23231 14.7066C2.46216 15.7854 1.79365 16.9333 1.23535 18.1355C0.686504 19.3441 0.323583 20.5348 0.259223 21.6182C0.194863 22.7016 0.420124 23.8368 1.25681 24.6735C2.09349 25.5102 3.23052 25.7354 4.31213 25.6711C5.39375 25.6049 6.5862 25.2438 7.79474 24.6949C8.88529 24.1997 10.0456 23.5239 11.2273 22.698C12.4072 23.5239 13.5657 24.1997 14.6581 24.6949C15.8648 25.2438 17.0573 25.6067 18.1407 25.6711C19.2241 25.7354 20.3575 25.5084 21.1942 24.6717C22.4832 23.3845 22.3455 21.4179 21.8324 19.741C21.3426 18.1427 20.3897 16.346 19.0989 14.5349ZM5.40447 6.66332C6.43424 6.97797 7.66245 7.5751 8.99256 8.44038C7.54023 9.67238 6.1913 11.0213 4.95931 12.4736C4.47504 11.7364 4.04599 10.9644 3.67568 10.1638C3.20549 9.12689 2.97487 8.26876 2.93554 7.63052C2.898 6.9887 3.05532 6.73126 3.15365 6.63293C3.31455 6.47203 3.90809 6.20744 5.40447 6.66332ZM3.67568 19.2476C4.00464 18.5235 4.43549 17.7458 4.95931 16.9377C6.19187 18.3901 7.5414 19.7391 8.99435 20.971C8.25776 21.4558 7.48631 21.8854 6.68632 22.2564C5.6494 22.7266 4.79126 22.9572 4.15302 22.9965C3.50942 23.0341 3.25377 22.8768 3.15544 22.7784C3.05711 22.6801 2.89978 22.4209 2.93733 21.7808C2.97666 21.1426 3.2055 20.2845 3.67747 19.2476H3.67568ZM15.7665 22.2564C14.9671 21.8854 14.1963 21.4557 13.4602 20.971C14.9113 19.7388 16.2591 18.3899 17.4899 16.9377C18.3534 18.2696 18.9505 19.4978 19.2652 20.5276C19.7229 22.0222 19.4583 22.6175 19.2974 22.7784C19.1972 22.8768 18.9398 23.0341 18.2998 22.9948C17.6597 22.959 16.8034 22.7266 15.7665 22.2564ZM9.43772 14.7066C9.43772 14.2324 9.62607 13.7777 9.96135 13.4424C10.2966 13.1071 10.7514 12.9188 11.2255 12.9188C11.6997 12.9188 12.1544 13.1071 12.4897 13.4424C12.8249 13.7777 13.0133 14.2324 13.0133 14.7066C13.0133 15.1807 12.8249 15.6355 12.4897 15.9707C12.1544 16.306 11.6997 16.4944 11.2255 16.4944C10.7514 16.4944 10.2966 16.306 9.96135 15.9707C9.62607 15.6355 9.43772 15.1807 9.43772 14.7066Z" fill={color}/>
    </svg>
);

const GlobeIcon: React.FC<SVGIconProps> = ({
    color = "#6BCC09",
}) => (
    <svg width="26" height="29" viewBox="0 0 26 29" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M20.1061 1.77319C21.3705 2.96357 22.3776 4.40037 23.0653 5.99497C23.753 7.58956 24.1067 9.30814 24.1046 11.0447C24.1046 18.0851 18.397 23.7927 11.3566 23.7927C9.62003 23.7948 7.90145 23.4411 6.30686 22.7534C4.71226 22.0657 3.27546 21.0586 2.08508 19.7942" stroke={color} strokeWidth="2.27967" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M11.1518 23.7925V27.6783M7.26599 27.6783H15.0376" stroke={color} strokeWidth="2.27967" strokeLinecap="round" strokeLinejoin="round"/>
        <path fillRule="evenodd" clipRule="evenodd" d="M7.63048 3.38037C8.68071 2.8954 9.82386 2.64465 10.9807 2.64553C12.3822 2.64529 13.7591 3.01437 14.9727 3.71559C16.1863 4.4168 17.1936 5.4254 17.8934 6.63982H17.1575C16.5094 6.63982 15.984 6.88909 15.984 7.53719C15.984 7.86622 15.984 9.57122 13.9639 9.53932C11.9977 9.53932 11.9977 7.81238 11.9977 7.53719C11.9977 6.11236 11.213 5.89201 10.2508 5.61981C9.87789 5.51511 9.47906 5.40245 9.08821 5.21001C8.08714 4.71446 7.74415 4.03047 7.63048 3.38037ZM4.9982 2.64553C3.83976 3.5178 2.88337 4.62996 2.19442 5.90597C1.41486 7.35558 1.00781 8.97621 1.0099 10.6221C1.0099 15.8957 5.10389 20.213 10.2867 20.569L10.3405 20.573C10.724 20.5975 11.1085 20.5998 11.4922 20.5799H11.4942C11.9968 20.5546 12.4967 20.4913 12.9898 20.3905C13.7678 20.2305 14.524 19.9781 15.2422 19.6387C17.4954 18.5699 19.2686 16.6986 20.2146 14.3911C20.7046 13.1951 20.9549 11.9146 20.9514 10.6221C20.9522 9.78132 20.8467 8.94378 20.6373 8.12945C20.0846 5.98874 18.8361 4.09246 17.088 2.73878C15.34 1.3851 13.1916 0.650814 10.9807 0.651376C8.82281 0.648487 6.72274 1.34851 4.9982 2.64553ZM18.3311 13.726C18.0886 13.6525 17.8367 13.6148 17.5833 13.6144H17.3669C16.4667 13.6144 15.6033 13.9719 14.9666 14.6084C14.33 15.2449 13.9722 16.1082 13.9719 17.0084V18.0195C15.9362 17.2216 17.5039 15.6782 18.3311 13.726ZM11.0943 18.5988H10.9807C9.5755 18.5989 8.19524 18.2279 6.97955 17.5232C5.76387 16.8185 4.7559 15.8052 4.05766 14.5858C3.35941 13.3664 2.99568 11.9842 3.00326 10.5791C3.01084 9.17397 3.38947 7.79577 4.10083 6.58398C5.04805 7.1234 5.51768 8.11649 5.93047 8.99192C6.13886 9.43163 6.33229 9.84243 6.56561 10.1505C7.10403 10.8604 7.6255 11.1665 8.15195 11.4746C8.67043 11.7787 9.19589 12.0858 9.74727 12.7868C11.1831 14.6065 11.1621 17.14 11.0933 18.5978L11.0943 18.5988Z" fill={color}/>
    </svg>
);

interface PricingSectionProps {
    onlyPlans?: boolean;
    t: (key: string, params?: Record<string, string | number>) => string
}

const PricingSection: React.FC<PricingSectionProps> = ({onlyPlans ,t}) => {
    // Router
    const router = useRouter();
    const sliderRef = React.useRef<any>(null);
    const [loading, setLoader] = useState(false);
    const [selectedPlanId, setSelectedPlanId] = useState('');
const { locale } = useTranslation();
    const handleBuyNow = async (planId: string) =>
    {
        if (typeof window !== "undefined")
        {
            setSelectedPlanId(planId);
            setLoader(true);

            // Store the selected plan in localStorage
            localStorage.setItem("HMWK_SELECTED_PACK", planId);
            const loggedInId = await isUserLoggedIn();

            // Redirect to the signup page with the packs redirect parameter
         if (loggedInId) {
    router.push(`/${locale}/payment`);
} else {
    router.push(`/${locale}/signup?redirect_to=packs`);
}
        }
    };

    // Mobile-optimized settings
    const settings: Settings = {
        dots: false,
        infinite: false,
        speed: 300,
        slidesToShow: 3,
        slidesToScroll: 1,
        arrows: false,
        swipeToSlide: true,
        touchThreshold: 10,
        focusOnSelect: true,
        swipe: true,
        accessibility: true,
        pauseOnHover: false,
        useCSS: true,
        useTransform: true,
        cssEase: 'cubic-bezier(0.25, 0.1, 0.25, 1)',
        responsive: [
            {
                breakpoint: 959,
                settings: {
                    slidesToShow: 1,
                    centerMode: true,
                    centerPadding: "30px",
                    focusOnSelect: true,
                    speed: 250,
                    touchThreshold: 5,
                    swipeToSlide: true,
                    edgeFriction: 0.15,
                }
            },
            {
                breakpoint: 659,
                settings: {
                    slidesToShow: 1,
                    centerMode: true,
                    centerPadding: "20px",
                    speed: 200,
                    touchThreshold: 5,
                    edgeFriction: 0.15,
                }
            },
        ],
    };

    const renderPlans = () => {
        let bufferPlans: any = [];

        // eslint-disable-next-line array-callback-return
        pricingPlans.map((plan) => {
            bufferPlans.push(
                <div key={plan.id} className={`${styles.planCard}`} style={{borderColor: plan.color}}>
                    <div className={styles.planHeader} style={{backgroundColor: plan.color}}>
                        <h3 className={styles.planQuestions}>{plan.questions} {t('pricing_section.questions')}</h3>
                        {plan.popular && <span className={styles.planPopular}>{t('pricing_section.popular')}</span>}
                    </div>
                    <ul className={styles.planFeatures}>
                        {plan.features.map((feature, i) => (
                            <li key={i} className={styles.planFeature}><CheckIcon color={plan.color}/> {t(`${feature}`)}</li>
                        ))}
                    </ul>
                    <div style={{borderColor: plan.color}} className={styles.planDataDivider}></div>
                    <ul className={styles.planSubjects}>
                        <li className={styles.planSubject}>
                            <CalculateIcon color={plan.color}/> {t('pricing_section.math')}
                        </li>
                        <li className={styles.planSubject}>
                            <AtoZIcon color={plan.color}/> {t('pricing_section.language_arts')}
                        </li>
                        <li className={styles.planSubject}>
                            <ScienceIcon color={plan.color}/> {t('pricing_section.science')}
                        </li>
                        <li className={styles.planSubject}>
                            <GlobeIcon color={plan.color}/> {t('pricing_section.social_studies')}
                        </li>
                    </ul>
                    <div className={styles.planPrice}>${plan.price}</div>
                    <PrimaryButton
                        onClick={() => handleBuyNow(plan.id)}
                        label={t('pricing_section.buy_now')}
                        size={plan.button}
                        loading={selectedPlanId && selectedPlanId === plan.id ? loading : false}
                    />
                </div>
            )
        })

        return bufferPlans;
    }

    if (onlyPlans) {
        return (
            <div className={`${styles.planOnlyCards}`}>
                {renderPlans()}
            </div>
        )
    }

    return (
        <div className={styles.pricingSectionBox}>
            <Container maxWidth={false} className="mainContainer">
                <div className={styles.sectionHead}>
                    <h2 className={styles.sectionTitle}>{t("pricing_section.title")}</h2>
                    <div className={styles.sectionText}>{t('pricing_section.subtitle')}</div>
                </div>
                <Slider ref={sliderRef} {...settings} className={`${styles.planCards} pricingSlider tutorCardSlider`}>
                    {renderPlans()}
                </Slider>
            </Container>
        </div>
    );
};

export default PricingSection;