import React from 'react';
import {useRouter} from "next/navigation";
import Container from "@mui/material/Container";
import styles from "@/src/styles/home.module.css";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import { useTranslation } from '@/src/Utils/i18n';

interface TopGradeSectionProps {
    t: (key: string, params?: Record<string, string | number>) => string
}
 
const TopGradeSection: React.FC <TopGradeSectionProps>= ({t}) => {
    const router = useRouter();
    const {locale} = useTranslation();

    return (
        <div className={styles.topGradeBox}>
            <Container maxWidth={false} className="mainContainer">
                <div className={styles.topGradeWrapper}>
                    <div className={styles.topGradeImage}>
                        <img src={'/images/kid-selfie-building.png'} alt="Top Grade" loading="lazy" />
                    </div>
                    <div className={styles.topGradeContent}>
                        <div className={styles.sectionHead}>
                            <h2 className={styles.sectionTitle}>{t("top_grade_section.study_smarter_not_harder")}     
                            </h2>
                        </div>

                        <div className={styles.topGradePoints}>
                            <div className={styles.topGradeItem}>
                                <div className={styles.topGradeItemTitle}>{t("top_grade_section.ask_experts_not_robots")}</div>
                                <div className={styles.topGradeItemText}>{t("top_grade_section.ai_bots_unreliable_description")}
                                </div>
                            </div>

                            <div className={styles.topGradeItem}>
                                <div className={styles.topGradeItemTitle} style={{color: '#CE82FF'}}>{t("top_grade_section.blast_roadblocks")}
                                </div>
                                <div className={styles.topGradeItemText}>{t("top_grade_section.waste_hours_description")}
                                </div>
                            </div>

                            <div className={styles.topGradeItem}>
                                <div className={styles.topGradeItemTitle} style={{color: '#FF9600'}}>{t("top_grade_section.peak_time_power")}
                                </div>
                                <div className={styles.topGradeItemText}>{t("top_grade_section.brain_ready_description")}
                                </div>
                            </div>
                        </div>

                        <PrimaryButton label={t("top_grade_section.get_started_button")} size={'greenBtn'}
                                       onClick={() => router.push(`/${locale}/become-a-student`)}/>
                    </div>
                </div>
            </Container>
        </div>
    );
};

export default TopGradeSection;