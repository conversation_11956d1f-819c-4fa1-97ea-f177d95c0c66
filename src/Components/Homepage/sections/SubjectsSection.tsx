import React, {FC} from "react";
import Container from "@mui/material/Container";
import styles from "@/src/styles/home.module.css";
import {useTranslation} from "@/src/Utils/i18n";

interface SubjectsSectionProps {
    title?: string;
    text?: string;
}

const SubjectsSection: FC<SubjectsSectionProps> = ({title, text}) => {
    const {t} = useTranslation();
    
    return (
        <div className={styles.subjectsBox}>
            <Container maxWidth={false} className="mainContainer">
                <div className={styles.sectionHead}>
                    {title && <h2 className={styles.sectionTitle}>{title}</h2>}
                    {text && <div className={styles.sectionText}>{text}</div>}
                </div>

                <div className={styles.subjectsList}>
                    <div className={`${styles.subjectItem} ${styles.subjectPurple}`}>
                        <div className={styles.subjectItemIcon}><img src={'/icons/home/<USER>'}
                                                                     alt={'science-icon'}/></div>
                        <h2 className={styles.subjectItemTitle}>{t('subjects.science')}</h2>
                        <div className={styles.subjectItemGrade}>{t('subjects.grade_range')}</div>
                    </div>

                    <div className={`${styles.subjectItem} ${styles.subjectBlue}`}>
                        <div className={styles.subjectItemIcon}><img src={'/icons/home/<USER>'}
                                                                     alt={'math-icon'}/></div>
                        <h2 className={styles.subjectItemTitle}>{t('subjects.math')}</h2>
                        <div className={styles.subjectItemGrade}>{t('subjects.grade_range')}</div>
                    </div>

                    <div className={`${styles.subjectItem} ${styles.subjectOrange}`}>
                        <div className={styles.subjectItemIcon}><img src={'/icons/home/<USER>'}
                                                                     alt={'book-pen-icon'}/></div>
                        <h2 className={styles.subjectItemTitle}>{t('subjects.language_arts')}</h2>
                        <div className={styles.subjectItemGrade}>{t('subjects.grade_range')}</div>
                    </div>

                    <div className={`${styles.subjectItem} ${styles.subjectGreen}`}>
                        <div className={styles.subjectItemIcon}><img src={'/icons/home/<USER>'}
                                                                     alt={'globe-icon'}/></div>
                        <h2 className={styles.subjectItemTitle}>{t('subjects.social_studies')}</h2>
                        <div className={styles.subjectItemGrade}>{t('subjects.grade_range')}</div>
                    </div>
                </div>
            </Container>
        </div>
    );
};

export default SubjectsSection;
