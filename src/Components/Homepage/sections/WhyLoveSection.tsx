import React from 'react';
import Container from "@mui/material/Container";
import styles from "@/src/styles/home.module.css";

interface WhyLoveSectionProps {
      t: (key: string, params?: Record<string, string | number>) => string}

const WhyLoveSection : React.FC <WhyLoveSectionProps>= ({t}) => {
    const items = [
        {
            icon: '/icons/home/<USER>',
            title: t('WhyLoveSection.items.0.title'),
            borderClass: styles.whyLoveBoxBlue,
        },
        {
            icon: '/icons/home/<USER>',
            title: t('WhyLoveSection.items.1.title'),
            borderClass: styles.whyLoveBoxGreen,
        },
        {
            icon: '/icons/home/<USER>',
            title: t('WhyLoveSection.items.2.title'),
            borderClass: styles.whyLoveBoxPurple,
        },
        {
            icon: '/icons/home/<USER>',
            title: t('WhyLoveSection.items.3.title'),
            borderClass: styles.whyLoveBoxOrange,
        },
    ];

    return (
        <div className={styles.whyLoveBox}>
            <Container maxWidth={false} className="mainContainer">
                <h2 className={styles.sectionTitle}>
                    {t('WhyLoveSection.sectionTitle')}
                    <span className={styles.whyLoveHighlight}>{t('WhyLoveSection.sectionTitleHighlight')}</span>
                </h2>
                <div className={styles.whyLoveGrid}>
                    {items.map((item, index) => (
                        <div key={index} className={`${styles.whyLoveItem} ${item.borderClass}`}>
                            <div className={styles.whyLoveIcon}><img src={item.icon} alt={'icon'}/></div>
                            <p className={styles.whyLoveText}>{item.title}</p>
                        </div>
                    ))}
                </div>
            </Container>
        </div>
    );
};

export default WhyLoveSection;

