"use client";
import React, {useContext, useState} from "react";
import Container from "@mui/material/Container";
import Box from "@mui/material/Box";
import {useRouter} from "next/navigation";
import parse from "html-react-parser";
import Image from "next/image";
import {UserContext} from "@/src/Contexts/UserContext";
import {useTranslation} from "@/src/Utils/i18n";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import styles from "@/src/styles/home.module.css";
import SelectSubject from "@/src/Components/Widgets/SelectSubject";

function HeroSection(props: {
    title: any;
    subtitle: string;
    image: string;
    buttonPath: string;
    buttonText: string;
    buttonColor: "primary" | "secondary";
}) {
    const {user} = useContext(UserContext);
    const {t,locale} = useTranslation();
    const router = useRouter();
    const [placeholder, setPlaceholder] = useState('Ask a Tutor');

    const [preview, setPreview] = useState<string | null>(null);

    const handleFileChange = (e) => {
        const selectedFile = e.target.files[0];
        if (selectedFile && selectedFile.type.startsWith('image/')) {
            setPreview(URL.createObjectURL(selectedFile));
        }
    };

    const handleRemove = () => {
        setPreview(null);
    };

    return (
        <Box className={`${styles.pageContent} ${styles.bannerContent}`} sx={{py: 0}}>
            <Container maxWidth={false} className={"mainContainer"}>
                <div className={styles.bannerWrapper}>
                    <div className={styles.bannerLeftBox}>
                        {props.title ? <h2 className={styles.bannerTitle}>{parse(props.title)}</h2> : ''}
                        {props.subtitle ? <div className={styles.bannerText}>{props.subtitle}</div> : ''}

                        <div className={styles.askQuesForm}>
                            <div className={styles.askFormFields}>
                                <div className={styles.askBottomRow}>
                                    <div className={styles.askFormField}>
                                        <div className={styles.askFieldInput}>
                                            <textarea
                                                id="question"
                                                placeholder={placeholder}
                                                onFocus={() => setPlaceholder("")}
                                                onBlur={() => setPlaceholder(t('student.ask_question'))}
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className={styles.askTopRow}>
                                    <div className={`${styles.askFormField} ${styles.askFormFileInput} ${styles.homeFormFileInput}`}>
                                        {!preview ? (
                                            <div className={styles.askFieldInput}>
                                                <label><img src={'/icons/add-file-icon.svg'} alt={'add-file'}/><input onChange={handleFileChange} accept="image/*" type="file" id="file"/></label>
                                            </div>
                                        ) : (
                                            <div className={styles.askFieldInput}>
                                                <div className={styles.askFormPreviewWrapper}>
                                                    <img src={preview} alt="Preview" className={styles.askFormPreviewImage}/>
                                                    <img className={styles.askFormPreviewRemove} onClick={handleRemove} src="/icons/small-remove-icon.svg" alt="remove"/>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                    <div className={styles.askFormField}>
                                        <div className={styles.askFieldInput}>
                                            <SelectSubject/>
                                        </div>
                                    </div>
                                    <PrimaryButton
                                        icon={'plan-icon.svg'}
                                        size={'greenBtn'}
                                        label={t('student.ask_question')}
                                        onClick={() => router.push(`/${locale}/become-a-student`)}
                                        style={{ width: 'fit-content', maxWidth: '300px'}}
                                    />
                                </div>
                            </div>
                        </div>

                        {user.id !== "" && user.type !== "admin" && (
                            <PrimaryButton
                                label={t('student.ask_question')}
                                onClick={() => router.push(`/${locale}/ask-questions`)}
                                style={{marginTop: '20px'}}
                            />
                        )}
                    </div>

                    <div className={styles.bannerRightBox}>
                        <Image
                            src={props.image}
                            width={570}
                            height={570}
                            alt="Student at desk doing homework"
                            priority={true}
                            quality={90}
                            style={{
                                minWidth: 558,
                                maxWidth: 570,
                                display: "block",
                                height: "auto",
                                width: "100%",
                            }}
                            fetchPriority="high"
                            loading="eager"
                        />
                    </div>
                </div>
            </Container>
        </Box>
    )
}

export default HeroSection;