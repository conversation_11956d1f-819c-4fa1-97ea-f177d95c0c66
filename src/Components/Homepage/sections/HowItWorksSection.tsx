import React, {FC} from "react";
import {useRouter} from "next/navigation";
import Container from "@mui/material/Container";
import styles from "@/src/styles/home.module.css";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import {useTranslation} from "@/src/Utils/i18n";

interface HowItWorksSectionProps {
    title?: string;
    text?: string;
}

const HowItWorksSection: FC<HowItWorksSectionProps> = ({title, text}) =>
{
    const router = useRouter();
    const {t,locale} = useTranslation();

    const redirectToSignup = () => {
        router.push(`/${locale}/become-a-student`);
    }

    return (
        <div className={styles.howItWorksBox}>
            <Container maxWidth={false} className="mainContainer">
                <div className={styles.hiwWrapper}>
                    <div className={styles.hiwLeftBox}>
                        {title && <h2 className={styles.sectionTitle}>{title}</h2>}
                        {text && <div className={styles.sectionText}>{text}</div>}
                        <PrimaryButton onClick={redirectToSignup} label={t('homepage.get_started')} size={'greenBtn'}/>
                    </div>
                    <div className={styles.hiwRightBox}>
                        <div className={styles.hiwStepBox}>
                            <div className={styles.hiwStepNum}>1</div>
                            <div className={styles.hiwStepInfo}>
                                <div className={styles.hiwStepTitle}>{t('homepage.step_1_title')}</div>
                                <div className={styles.hiwStepText}>{t('homepage.step_1_description')}</div>
                            </div>
                        </div>

                        <div className={styles.hiwStepBox}>
                            <div className={styles.hiwStepNum}>2</div>
                            <div className={styles.hiwStepInfo}>
                                <div className={styles.hiwStepTitle}>{t('homepage.step_2_title')}</div>
                                <div className={styles.hiwStepText}>{t('homepage.step_2_description')}</div>
                            </div>
                        </div>

                        <div className={styles.hiwStepBox}>
                            <div className={styles.hiwStepNum}>3</div>
                            <div className={styles.hiwStepInfo}>
                                <div className={styles.hiwStepTitle}>{t('homepage.step_3_title')}</div>
                                <div className={styles.hiwStepText}>{t('homepage.step_3_description')}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        </div>
    );
};

export default HowItWorksSection;
