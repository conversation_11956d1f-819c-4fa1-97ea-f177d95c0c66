import * as React from "react";
import TableHead from "@mui/material/TableHead";
import TableSortLabel from "@mui/material/TableSortLabel";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import {visuallyHidden} from "@mui/utils";
import {EnhancedTableHeadProps} from "@/src/Types";

const SortingDefaultIcon = () => {
    return (
        <svg style={{marginLeft: '8px'}} width="13" height="20" viewBox="0 0 13 20" fill="none"
             xmlns="http://www.w3.org/2000/svg">
            <path
                d="M6.11577 3.98596L2.65664 7.56236C2.41121 7.81612 2.58503 8.25 2.93213 8.25L10.4014 8.25C10.7485 8.25 10.9223 7.81612 10.6769 7.56236L7.21772 3.98596C6.91343 3.67135 6.42007 3.67135 6.11577 3.98596Z"
                fill="#525866"/>
            <path
                d="M6.11577 16.014L2.65664 12.4376C2.41121 12.1839 2.58503 11.75 2.93213 11.75H10.4014C10.7485 11.75 10.9223 12.1839 10.6769 12.4376L7.21773 16.014C6.91343 16.3287 6.42007 16.3287 6.11577 16.014Z"
                fill="#525866"/>
        </svg>
    )
}

const SortingUpIcon = () => {
    return (
        <svg style={{marginLeft: '8px'}} width="13" height="20" viewBox="0 0 13 20" fill="none"
             xmlns="http://www.w3.org/2000/svg">
            <path
                d="M6.11577 3.98596L2.65664 7.56236C2.41121 7.81612 2.58503 8.25 2.93213 8.25L10.4014 8.25C10.7485 8.25 10.9223 7.81612 10.6769 7.56236L7.21772 3.98596C6.91343 3.67135 6.42007 3.67135 6.11577 3.98596Z"
                fill="#525866"/>
        </svg>
    )
}

const SortingDownIcon = () => {
    return (
        <svg style={{marginLeft: '8px'}} width="13" height="20" viewBox="0 0 13 20" fill="none"
             xmlns="http://www.w3.org/2000/svg">
            <path
                d="M6.11577 16.014L2.65664 12.4376C2.41121 12.1839 2.58503 11.75 2.93213 11.75H10.4014C10.7485 11.75 10.9223 12.1839 10.6769 12.4376L7.21773 16.014C6.91343 16.3287 6.42007 16.3287 6.11577 16.014Z"
                fill="#525866"/>
        </svg>
    )
}

const EnhancedTableHead: React.FC<EnhancedTableHeadProps> = ({
                                                                 order,
                                                                 orderBy,
                                                                 onRequestSort,
                                                                 columns,
                                                             }) => {
    const createSortHandler = React.useCallback(
        (property: string) => (event: React.MouseEvent<unknown>) => {
            if (onRequestSort) {
                onRequestSort(event, property);
            }
        },
        [onRequestSort]
    );

    const getSortIcon = (columnId: string) => {
        if (orderBy !== columnId) return <SortingDefaultIcon/>;
        return order === "asc" ? <SortingUpIcon/> : <SortingDownIcon/>;
    };

    return (
        <TableHead className="tableHead">
            <TableRow>
                {columns.map((headCell) => (
                    <TableCell
                        key={headCell.id}
                        sortDirection={orderBy === headCell.id && (order === 'asc' || order === 'desc') ? order : false}
                        align={headCell?.align}
                        className={`table-col-${headCell.id}`}
                    >
                        {headCell.sorting ? (
                            <TableSortLabel
                                active={orderBy === headCell.id}
                                direction={orderBy === headCell.id && (order === 'asc' || order === 'desc') ? order : "asc"}
                                onClick={createSortHandler(headCell.id)}
                                IconComponent={() => getSortIcon(headCell.id)}
                            >
                                {headCell.label}
                                {orderBy === headCell.id ? (<span style={visuallyHidden}>Sorted {order}</span>) : null}
                            </TableSortLabel>
                        ) : (
                            headCell.label
                        )}
                    </TableCell>
                ))}
            </TableRow>
        </TableHead>
    );
};

export default React.memo(EnhancedTableHead);
