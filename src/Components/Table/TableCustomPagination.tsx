import React from "react";
import {
    Box,
    MenuItem,
    Pagination,
    PaginationItem,
    Select,
    SvgIcon,
    Typography,
    useMediaQuery,
    useTheme
} from "@mui/material";
import styles from "@/src/styles/adminTabs.module.css";
import {TablePaginationControlsProps} from "@/src/Types";

const ArrowComponent = () => {
    return (
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="CaretDown">
                <path id="Vector"
                      d="M13.3535 6.35354L8.35354 11.3535C8.3071 11.4 8.25196 11.4369 8.19126 11.4621C8.13056 11.4872 8.0655 11.5002 7.99979 11.5002C7.93408 11.5002 7.86902 11.4872 7.80832 11.4621C7.74762 11.4369 7.69248 11.4 7.64604 11.3535L2.64604 6.35354C2.55222 6.25972 2.49951 6.13247 2.49951 5.99979C2.49951 5.86711 2.55222 5.73986 2.64604 5.64604C2.73986 5.55222 2.86711 5.49951 2.99979 5.49951C3.13247 5.49951 3.25972 5.55222 3.35354 5.64604L7.99979 10.2929L12.646 5.64604C12.6925 5.59958 12.7476 5.56273 12.8083 5.53759C12.869 5.51245 12.9341 5.49951 12.9998 5.49951C13.0655 5.49951 13.1305 5.51245 13.1912 5.53759C13.2519 5.56273 13.3071 5.59958 13.3535 5.64604C13.4 5.69249 13.4368 5.74764 13.462 5.80834C13.4871 5.86904 13.5001 5.93409 13.5001 5.99979C13.5001 6.06549 13.4871 6.13054 13.462 6.19124C13.4368 6.25193 13.4 6.30708 13.3535 6.35354Z"
                      fill="#99A0AE"/>
            </g>
        </svg>
    )
}

const ArrowBackNewIcon = () => {
    return (
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd"
                  d="M14.6909 16.7523C14.8779 16.565 14.983 16.3112 14.983 16.0465C14.983 15.7818 14.8779 15.5279 14.6909 15.3406L11.4021 12.051L14.6909 8.76143C14.789 8.66998 14.8677 8.5597 14.9223 8.43718C14.9769 8.31465 15.0063 8.18239 15.0086 8.04827C15.011 7.91415 14.9863 7.78093 14.9361 7.65655C14.8859 7.53218 14.8111 7.4192 14.7162 7.32435C14.6214 7.2295 14.5084 7.15472 14.384 7.10448C14.2596 7.05425 14.1264 7.02957 13.9923 7.03194C13.8582 7.03431 13.7259 7.06366 13.6034 7.11826C13.4809 7.17285 13.3706 7.25156 13.2792 7.3497L9.28371 11.3451C9.09665 11.5324 8.99159 11.7863 8.99159 12.051C8.99159 12.3157 9.09665 12.5696 9.28371 12.7569L13.2792 16.7523C13.4664 16.9394 13.7203 17.0444 13.985 17.0444C14.2497 17.0444 14.5036 16.9394 14.6909 16.7523Z"
                  fill="#777777"/>
        </svg>
    )
}

const ArrowForwardIcon = () => {
    return (
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd"
                  d="M9.30913 7.32471C9.12207 7.512 9.01701 7.76588 9.01701 8.03057C9.01701 8.29527 9.12207 8.54915 9.30913 8.73644L12.5979 12.026L9.30913 15.3156C9.21099 15.407 9.13228 15.5173 9.07768 15.6398C9.02309 15.7624 8.99373 15.8946 8.99137 16.0288C8.989 16.1629 9.01367 16.2961 9.06391 16.4205C9.11415 16.5448 9.18892 16.6578 9.28377 16.7527C9.37862 16.8475 9.4916 16.9223 9.61598 16.9725C9.74035 17.0228 9.87358 17.0475 10.0077 17.0451C10.1418 17.0427 10.2741 17.0134 10.3966 16.9588C10.5191 16.9042 10.6294 16.8255 10.7208 16.7273L14.7163 12.7319C14.9033 12.5446 15.0084 12.2907 15.0084 12.026C15.0084 11.7613 14.9033 11.5074 14.7163 11.3202L10.7208 7.32471C10.5336 7.13766 10.2797 7.03259 10.015 7.03259C9.75029 7.03259 9.49641 7.13766 9.30913 7.32471Z"
                  fill="#777777"/>
        </svg>
    )
}

const FirstPageIcon = () => {
    return (
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd"
                  d="M12.0781 16.7987C12.2651 16.6114 12.3702 16.3575 12.3702 16.0928C12.3702 15.8281 12.2651 15.5743 12.0781 15.387L8.78933 12.0974L12.0781 8.80781C12.1762 8.71637 12.2549 8.60609 12.3095 8.48356C12.3641 8.36104 12.3935 8.22877 12.3958 8.09466C12.3982 7.96054 12.3735 7.82732 12.3233 7.70294C12.2731 7.57857 12.1983 7.46558 12.1034 7.37073C12.0086 7.27588 11.8956 7.20111 11.7712 7.15087C11.6469 7.10063 11.5136 7.07596 11.3795 7.07833C11.2454 7.08069 11.1131 7.11005 10.9906 7.16464C10.8681 7.21924 10.7578 7.29795 10.6664 7.39609L6.67091 11.3915C6.48386 11.5788 6.37879 11.8327 6.37879 12.0974C6.37879 12.3621 6.48386 12.616 6.67091 12.8033L10.6664 16.7987C10.8536 16.9858 11.1075 17.0908 11.3722 17.0908C11.6369 17.0908 11.8908 16.9858 12.0781 16.7987Z"
                  fill="#777777"/>
            <path fillRule="evenodd" clipRule="evenodd"
                  d="M17.6679 16.7987C17.855 16.6114 17.96 16.3575 17.96 16.0928C17.96 15.8281 17.855 15.5743 17.6679 15.387L14.3792 12.0974L17.6679 8.80781C17.7661 8.71637 17.8448 8.60609 17.8994 8.48356C17.954 8.36104 17.9833 8.22877 17.9857 8.09466C17.9881 7.96054 17.9634 7.82732 17.9131 7.70294C17.8629 7.57857 17.7881 7.46558 17.6933 7.37073C17.5984 7.27588 17.4854 7.20111 17.3611 7.15087C17.2367 7.10063 17.1035 7.07596 16.9694 7.07833C16.8352 7.08069 16.703 7.11005 16.5804 7.16464C16.4579 7.21924 16.3476 7.29795 16.2562 7.39609L12.2608 11.3915C12.0737 11.5788 11.9686 11.8327 11.9686 12.0974C11.9686 12.3621 12.0737 12.616 12.2608 12.8033L16.2562 16.7987C16.4435 16.9858 16.6974 17.0908 16.9621 17.0908C17.2268 17.0908 17.4806 16.9858 17.6679 16.7987Z"
                  fill="#777777"/>
        </svg>
    )
}

const LastPageIcon = () => {
    return (
        <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd"
                  d="M11.9219 16.7987C11.7349 16.6114 11.6298 16.3575 11.6298 16.0928C11.6298 15.8281 11.7349 15.5743 11.9219 15.387L15.2107 12.0974L11.9219 8.80781C11.8238 8.71637 11.7451 8.60609 11.6905 8.48356C11.6359 8.36104 11.6065 8.22877 11.6042 8.09466C11.6018 7.96054 11.6265 7.82732 11.6767 7.70294C11.7269 7.57857 11.8017 7.46558 11.8966 7.37073C11.9914 7.27588 12.1044 7.20111 12.2288 7.15087C12.3531 7.10063 12.4864 7.07596 12.6205 7.07833C12.7546 7.08069 12.8869 7.11005 13.0094 7.16464C13.1319 7.21924 13.2422 7.29795 13.3336 7.39609L17.3291 11.3915C17.5161 11.5788 17.6212 11.8327 17.6212 12.0974C17.6212 12.3621 17.5161 12.616 17.3291 12.8033L13.3336 16.7987C13.1464 16.9858 12.8925 17.0908 12.6278 17.0908C12.3631 17.0908 12.1092 16.9858 11.9219 16.7987Z"
                  fill="#777777"/>
            <path fillRule="evenodd" clipRule="evenodd"
                  d="M6.33208 16.7987C6.14502 16.6114 6.03996 16.3575 6.03996 16.0928C6.03996 15.8281 6.14502 15.5743 6.33208 15.387L9.62083 12.0974L6.33207 8.80781C6.23394 8.71637 6.15522 8.60609 6.10063 8.48356C6.04604 8.36104 6.01668 8.22877 6.01432 8.09466C6.01195 7.96054 6.03662 7.82732 6.08686 7.70294C6.1371 7.57857 6.21187 7.46558 6.30672 7.37073C6.40157 7.27588 6.51455 7.20111 6.63893 7.15087C6.7633 7.10063 6.89652 7.07596 7.03064 7.07833C7.16476 7.08069 7.29703 7.11005 7.41955 7.16464C7.54208 7.21924 7.65235 7.29795 7.7438 7.39609L11.7392 11.3915C11.9263 11.5788 12.0314 11.8327 12.0314 12.0974C12.0314 12.3621 11.9263 12.616 11.7392 12.8033L7.7438 16.7987C7.55651 16.9858 7.30264 17.0908 7.03794 17.0908C6.77324 17.0908 6.51936 16.9858 6.33208 16.7987Z"
                  fill="#777777"/>
        </svg>

    )
}

const TableCustomPagination: React.FC<TablePaginationControlsProps> = ({
                                                                           showFullBar,
                                                                           filteredRowsLength,
                                                                           page,
                                                                           rowsPerPage,
                                                                           setPage,
                                                                           setRowsPerPage,
                                                                       }) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const theme = useTheme();

    // eslint-disable-next-line react-hooks/rules-of-hooks
    const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));

    // eslint-disable-next-line react-hooks/rules-of-hooks
    const isMediumScreen = useMediaQuery(theme.breakpoints.down("md"));

    if (filteredRowsLength < 5) return null;

    return (
        <Box
            className={styles.paginationBox}
            display="flex"
            justifyContent={showFullBar ? "space-between" : 'center'}
            alignItems="center"
            flexWrap="wrap"
            sx={{width: '100%', padding: '40px 0 0px'}}
        >
            {showFullBar &&
                <Typography className={styles.pagiShowingBox} sx={{fontSize: '14px', color: '#3F3F3F'}}>
                    Showing
                    <Select
                        value={rowsPerPage}
                        onChange={({target: {value}}) => {
                            setRowsPerPage(Number(value));
                            setPage(0);
                        }}
                        sx={{
                            height: 34,
                            width: 65,
                            margin: '0 12px',
                            backgroundColor: '#fff',
                            borderRadius: '8px',
                            padding: 0,
                            "&.MuiInputBase-root": {
                                paddingRight: '8px',
                                ' svg': {
                                    width: '24px',
                                    position: 'absolute',
                                    right: '5px'
                                }
                            },
                            "& .MuiInputBase-input": {
                                fontSize: '16px',
                                fontFamily: 'inherit',
                                padding: "10px 0 10px 12px !important",
                                width: '100%',
                                position: 'relative',
                                zIndex: 1,
                            },
                            "& .MuiOutlinedInput-notchedOutline": {
                                borderColor: '#E1E4EA'
                            },
                            "&.MuiOutlinedInput-root": {
                                "&:hover fieldset": {
                                    border: "1px solid #E1E4EA !important",
                                },
                                "&.Mui-focused fieldset": {
                                    border: "1px solid #E1E4EA !important",
                                },
                            },
                        }}
                        IconComponent={(props) => (
                            <SvgIcon {...props} component={ArrowComponent}/>
                        )}
                    >
                        {[5, 10, 20, 30].map((value) => (
                            <MenuItem key={value} value={value}>{value}</MenuItem>
                        ))}
                    </Select>
                    {page * rowsPerPage + 1}–{Math.min((page + 1) * rowsPerPage, filteredRowsLength)} of {filteredRowsLength} entries
                </Typography>
            }

            {filteredRowsLength > rowsPerPage && (
                <Pagination
                    count={Math.ceil(filteredRowsLength / rowsPerPage)}
                    page={page + 1}
                    onChange={(event, newPage) => setPage(newPage - 1)}
                    color="primary"
                    shape="rounded"
                    showFirstButton
                    showLastButton
                    siblingCount={isSmallScreen ? 0 : isMediumScreen ? 1 : 2}
                    boundaryCount={isSmallScreen ? 0 : 1}
                    sx={{
                        "& .MuiPaginationItem-root": {
                            borderRadius: '12px',
                            border: '1px solid rgba(229, 229, 229, 1)',
                            background: 'rgba(250, 250, 250, 1)',
                            color: 'rgba(119, 119, 119, 1)',
                            width: 40,
                            height: 40,
                            fontWeight: '700',
                            fontSize: '12px',
                            marginRight: "6px",
                            paddingBottom: '2px !important',
                            "&:hover": {
                                background: '#E1E4EA',
                            },
                        },
                        "& .Mui-selected": {
                            border: 0,
                            backgroundColor: 'rgba(107, 204, 9, 1) !important',
                            color: "#fff  !important",
                            boxShadow: "0px -4px 0px 0px rgba(92, 179, 4, 1) inset !important",
                        },
                    }}
                    renderItem={(item) => (
                        <PaginationItem
                            slots={{
                                previous: ArrowBackNewIcon,
                                next: ArrowForwardIcon,
                                first: FirstPageIcon,
                                last: LastPageIcon
                            }}
                            {...item}
                        />
                    )}
                />
            )}
        </Box>
    );
};

export default TableCustomPagination;