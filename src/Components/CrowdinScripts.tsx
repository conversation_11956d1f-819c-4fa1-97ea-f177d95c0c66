"use client";
import { useParams } from "next/navigation";
import React from "react";

export default function CrowdinScripts() {
  const params = useParams();
  const locale = Array.isArray(params?.locale) ? params.locale[0] : params?.locale;
  if (locale !== "de") return null;
  return (
    <>
      <script
        type="text/javascript"
        dangerouslySetInnerHTML={{
          __html: `var _jipt = []; _jipt.push(['project', 'odevly']);`,
        }}
      />
      <script
        type="text/javascript"
        src="//cdn.crowdin.com/jipt/jipt.js"
        defer
      ></script>
    </>
  );
}
