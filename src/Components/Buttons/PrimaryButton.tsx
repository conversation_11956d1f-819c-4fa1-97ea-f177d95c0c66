import React from "react";
import {Button, CircularProgress} from "@mui/material";
import {
    confirmModalButtonStyle,
    defaultBiggerButtonStyle,
    defaultBlueButtonStyle,
    defaultGreenButtonStyle,
    defaultMediumButtonStyle,
    defaultOrangeButtonStyle,
    defaultPrimaryButtonStyle,
    defaultWhiteButtonStyle
} from "@/src/Utils/styles";

type BorderButtonProps = {
    label: string;
    size?: string;
    icon?: string;
    loading?: boolean;
    onClick?: () => void;
    disabled?: boolean;
    style?: any;
    confirmModal?: boolean;
};

const PrimaryButton: React.FC<BorderButtonProps> = ({
                                                        size,
                                                        label,
                                                        icon,
                                                        loading,
                                                        onClick,
                                                        confirmModal,
                                                        ...props
                                                    }: {
    label: string;
    size?: string;
    icon?: string;
    loading?: boolean;
    onClick?: () => void;
    confirmModal?: boolean;
}) => {
    return (
        <Button
            className={'primaryButton'}
            onClick={onClick}
            sx={
                confirmModal
                    ? confirmModalButtonStyle
                    : size && size === "medium"
                        ? defaultMediumButtonStyle
                        : size && size === "bigger"
                            ? defaultBiggerButtonStyle
                            : size && size === "greenBtn"
                                ? defaultGreenButtonStyle
                                : size && size === "orangeBtn"
                                    ? defaultOrangeButtonStyle
                                    : size && size === "blueBtn"
                                        ? defaultBlueButtonStyle :
                                        size && size === "whiteBtn"
                                            ? defaultWhiteButtonStyle
                                            : defaultPrimaryButtonStyle
            }
            startIcon={loading && <CircularProgress size={15} color="inherit"/>}
            {...props}
        >
            {icon ? <img src={`/icons/${icon}`} style={{marginRight: '5px'}} alt={'icon'}/> : ''}
            {!loading && label}
        </Button>
    );
};

export default PrimaryButton;
