import React from "react";
import {Button, CircularProgress} from "@mui/material";
import {
    defaultBlackBorderButtonStyle,
    defaultBorderButtonStyle,
    defaultDarkBorderButtonStyle,
    defaultGreenBorderButtonStyle
} from "@/src/Utils/styles";

type BorderButtonProps = {
    label: string;
    type?: string;
    loading?: boolean;
    onClick?: () => void;
    disabled?: boolean;
    style?: any;
};

const BorderButton: React.FC<BorderButtonProps> = ({
                                                       label,
                                                       type,
                                                       loading,
                                                       onClick,
                                                       ...props
                                                   }) => {
    return (
        <Button
            className={'borderButton'}
            onClick={onClick}
            disableRipple
            sx={type && type === "dark" ? defaultDarkBorderButtonStyle : type && type === "green" ? defaultGreenBorderButtonStyle : type && type === "black" ? defaultBlackBorderButtonStyle : defaultBorderButtonStyle}
            startIcon={loading && <CircularProgress size={15} color="inherit"/>}
            {...props}
        >
            {!loading && label}
        </Button>
    );
};

export default BorderButton;