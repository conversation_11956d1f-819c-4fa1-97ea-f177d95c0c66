import React from 'react';
import {Button, CircularProgress} from '@mui/material';
import {defaultSmallButtonStyle} from "@/src/Utils/styles";

type BorderButtonProps = {
    label: string;
    loading?: boolean;
    onClick?: () => void;
    style?: any;
};

const SmallFillButton: React.FC<BorderButtonProps> = ({label, loading, onClick, ...props}) => {
    return (
        <Button
            onClick={onClick}
            sx={defaultSmallButtonStyle}
            startIcon={loading && <CircularProgress size={15} color="inherit"/>}
            {...props}
        >
            {!loading && label}
        </Button>
    );
};

export default SmallFillButton;
