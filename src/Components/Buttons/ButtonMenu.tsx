import React, {useEffect, useState} from 'react';
import styles from "@/src/styles/buttonMenu.module.css";

type ChildItem = {
    label: string;
    color?: string;
    icon?: React.ReactNode;
    onClick?: () => void;
};

type MenuItem = {
    label: string;
    children: ChildItem[];
};

type ButtonMenuProps = {
    item: MenuItem;
    borderUI?: boolean;
    zeroBottom?: boolean;
    defaultValue?: string;
    style?: any;
};

const ButtonMenu: React.FC<ButtonMenuProps> = ({item, borderUI, zeroBottom, defaultValue, style = {}}) => {
    const [isOpen, setIsOpen] = useState(false); // State to control the open/close of child items
    const [selectedLabel, setSelectedLabel] = useState('');
    const menuRef = React.useRef() as React.MutableRefObject<HTMLDivElement>;
    const toggleMenu = () => {
        setIsOpen(!isOpen);
    };

    const handleClickOutside = (event: MouseEvent) => {
        if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
            setIsOpen(false);
        }
    };

    useEffect(() => {
        if (defaultValue) {
            setSelectedLabel(defaultValue);
        }
    }, [defaultValue]);

    // Add event listener for clicks outside
    useEffect(() => {
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    return (
        <div className={zeroBottom ? `${styles.menuContainer} mb0` : styles.menuContainer} ref={menuRef}>
            <div className={borderUI ? styles.menuBorderHeader : styles.menuHeader} onClick={toggleMenu} style={style}>
                {selectedLabel && borderUI ? selectedLabel : item.label}
            </div>
            {isOpen && (
                <div className={styles.subMenuBox}>
                    {item.children.map((child: any, index) => {
                        if (child.label === 'divider') {
                            return (
                                <div key={index} className={styles.childItemDivider}></div>
                            );
                        }

                        return (
                            <div
                                key={index}
                                className={styles.childItem}
                                onClick={child.onClick}
                                onMouseUp={() => setTimeout(() => {
                                    setSelectedLabel(child.label);
                                    setIsOpen(false);
                                }, 500)}
                                style={{color: child.color}}
                            >
                                <img className={styles.icon} src={child.icon} alt={'icon'}/>
                                {child.label}
                            </div>
                        );
                    })}
                </div>

            )}
        </div>
    );
};

export default ButtonMenu;