import React from "react";
import {Button, CircularProgress} from "@mui/material";
import {defaultCancelButtonStyle} from "@/src/Utils/styles";

type CancelButtonProps = {
    label: string;
    type?: string;
    loading?: boolean;
    onClick?: () => void;
    disabled?: boolean;
    style?: any;
};

const CancelButton: React.FC<CancelButtonProps> = ({
                                                       label,
                                                       type,
                                                       loading,
                                                       onClick,
                                                       ...props
                                                   }) => {
    return (
        <Button
            onClick={onClick}
            sx={defaultCancelButtonStyle}
            startIcon={loading && <CircularProgress size={15} color="inherit"/>}
            {...props}
        >
            {!loading && label}
        </Button>
    );
};

export default CancelButton;
