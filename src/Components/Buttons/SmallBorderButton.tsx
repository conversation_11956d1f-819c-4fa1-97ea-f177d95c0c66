import {But<PERSON>} from "@mui/material";
import React from "react";

type BorderButtonProps = {
    label: string;
    icon?: string;
    height?: string;
    onClick?: () => void;
    className?: string;
    size?: string;
    color?: string;
    skin?: string;
    disabled?: boolean;
    style?: any;
};

const SmallBorderButton: React.FC<BorderButtonProps> = ({
                                                            label,
                                                            icon,
                                                            height,
                                                            color,
                                                            className,
                                                            skin,
                                                            onClick,
                                                            ...props
                                                        }) => {
    const ButtonIcon = () => {
        return <img src={`/icons/${icon}.svg`} alt={"button-icon"}/>;
    };

    return (
        <Button
            className={className}
            variant="outlined"
            disabled={props.disabled}
            startIcon={icon ? <ButtonIcon/> : ""}
            onClick={onClick}
            sx={{
                border: 0,
                backgroundColor: color && !props.disabled ? color : skin && skin === 'white' ? '#fff' : "#F1F5F9",
                color: color ? "#fff" : "#000",
                fontSize: "14px",
                fontWeight: 400,
                borderRadius: "8px",
                textTransform: "capitalize",
                boxShadow: "0px 0px 0px 1px rgba(62, 62, 62, 0.04)",
                padding: "5px 16px",
                height: height ? height : "40px",
                "&:hover": {
                    border: 0,
                    backgroundColor: color ? color : "#fff",
                    boxShadow: skin && skin === 'white' ? "0px 0px 0px 1px #375dfb" : "0px 0px 0px 1px rgba(62, 62, 62, 0.04)",
                },
                ...props,
            }}
        >
            {label}
        </Button>
    );
};

export default SmallBorderButton;
