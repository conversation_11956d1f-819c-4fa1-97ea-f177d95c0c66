import React from 'react';
import { locales, Locale, useTranslation } from '@/src/Utils/i18n';

interface LanguageDropdownProps {
  currentLocale?: string;
  onLocaleChange?: (locale: Locale) => void;
}

const LanguageDropdown: React.FC<LanguageDropdownProps> = ({ 
  currentLocale: propCurrentLocale, 
  onLocaleChange: propOnLocaleChange 
}) => {
  // Use the i18n hook as fallback if props are not provided
  const { locale: hookLocale, setLocale: hookSetLocale } = useTranslation();
  
  const currentLocale = propCurrentLocale || hookLocale;
  const onLocaleChange = propOnLocaleChange || hookSetLocale;

  return (
    <select
      value={currentLocale}
      onChange={e => onLocaleChange(e.target.value as Locale)}
      style={{
        height: '49px',
        paddingLeft:'15px',
        color: '#fff',
        fontSize: '16px',
        borderRadius: '14px',
        border: '2px solid #fff',
        fontFamily: 'inherit',
        fontWeight: 700,
        textTransform: 'none',
        width: 'fit-content',
        minWidth: '110px',
        backgroundColor: 'transparent',
        cursor: 'pointer',
        appearance: 'none',
        backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
        backgroundPosition: 'right 12px center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: '16px',
        paddingRight: '40px',
      }}
      aria-label="Select language"
    >
      {Object.entries(locales).map(([key, label]) => (
        <option 
          key={key} 
          value={key}
          style={{
            color: '#000',
            backgroundColor: '#fff',
            padding: '8px 12px',
            fontSize: '16px',
            fontWeight: 400,
          }}
        >
          {label}
        </option>
      ))}
    </select>
  );
};

export default LanguageDropdown;