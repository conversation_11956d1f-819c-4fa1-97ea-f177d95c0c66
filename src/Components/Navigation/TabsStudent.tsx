import React, {useCallback} from 'react';
import { useRouter} from "next/navigation";
import styles from '@/src/styles/legal.module.css';
import {STUDENT_TABS} from "@/src/Constants/tabNavigation";
import {useTranslation} from "@/src/Utils/i18n";

interface StudentTabsProps {
    active: number;
    remQuestions: number;
    setActiveTab?: (tab: number) => void;
}

interface Tab {
    path: string;
    index: number;
}

const TabsStudent: React.FC<StudentTabsProps> = ({
                                                     active,
                                                     remQuestions,
                                                     setActiveTab
                                                 }) => {
    const router = useRouter();
    const {t,locale} = useTranslation();

    const handleTabClick = useCallback((tab: Tab) => {
        const pathWithLocale = tab.path.startsWith(`/${locale}`) ? tab.path : `/${locale}${tab.path}`;
        router.push(pathWithLocale);
        setActiveTab?.(tab.index);
    }, [router, setActiveTab, locale]);

    const handlePlusClick = useCallback((e: React.MouseEvent) => {
        e.stopPropagation();
        const packsTab = STUDENT_TABS.find(tab => tab.path.endsWith('/packs'));
        if (packsTab) {
            const pathWithLocale = packsTab.path.startsWith(`/${locale}`) ? packsTab.path : `/${locale}${packsTab.path}`;
            router.push(pathWithLocale);
            setActiveTab?.(packsTab.index);
        }
    }, [router, setActiveTab, locale]);

    return (
        <div className={`${styles.tabsContainer} ${styles.tabsStudent}`}>
            <div className="mainContainer navbarOption">
                <div className={styles.tabsHolder}>
                    <nav className={styles.tabsNav}>
                        {STUDENT_TABS.slice(0, 4).map((tab) => {
                            const isActive = tab.index === active;
                            return (
                                <a
                                    key={tab.name}
                                    className={`${styles.tabLink} ${styles.tabGreenLink} ${isActive ? styles.activeTab : ''}`}
                                    onClick={() => handleTabClick(tab)}
                                >
                                    <img src={tab.icon} alt={t('common.menu_icon')} />
                                    <span>{t('student.' + tab.name)}</span>
                                </a>
                            );
                        })}
                    </nav>

                    {remQuestions > 0 && (
                        <div className={styles.quesRemains}>
                            <span>
                                {remQuestions}
                                    <img
                                        src="/icons/plus-icon.svg"
                                        alt="plus-icon"
                                        style={{cursor: 'pointer'}}
                                        onClick={handlePlusClick}
                                    />
                            </span>
                            Questions Remaining
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default TabsStudent;