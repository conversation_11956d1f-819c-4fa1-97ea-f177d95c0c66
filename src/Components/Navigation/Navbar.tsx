import React, {useContext, useEffect, useState} from "react";
import AppBar from "@mui/material/AppBar";
import Link from "next/link";
import {usePathname, useRouter} from "next/navigation";
import {useTranslation} from "@/src/Utils/i18n";
import styles from "@/src/styles/navbar.module.css";
import BorderButton from "@/src/Components/Buttons/BorderButton";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import { STUDENT_TABS, TUTOR_TABS, SPECIAL_ROUTES } from "@/src/Constants/tabNavigation";
import {handleScrollToSection} from "@/src/Utils/helpers";
import { signOutUser } from "@/src/Firebase/firebase.utils";
import { UserContext } from "@/src/Contexts/UserContext";



function Navbar(props: {
    logo?: string;
    id?: string;
    isAdmin?: boolean;
    isPaidUser?: boolean;
    isPublicPage?: boolean;
    hideVerify?: boolean;
    setCurrentLanguage?: (lang: string) => void;
    setActiveTab?: (tab: number) => void;
    user?: any;
    active?: number;
}) {
    const {user, fetching, setIsLoggingOut,isLoggingOut} = useContext(UserContext);
    
    const {t,} = useTranslation();
    const [showMenu, setShowMenu] = useState(false);
    const [scrolled, setScrolled] = useState(false);
    const dropdownRef = React.useRef<HTMLDivElement>(null);
    const pathname = usePathname();
    const router = useRouter();

    // Extract locale from pathname (assumes /[locale]/...)
    const locale = pathname?.split('/')[1] || 'en';

    const {
        active: activeTab,
        setActiveTab,
        isAdmin: adminUser,
        isPublicPage,
    } = props;

 

   
    // Handle scroll effect
    useEffect(() => {
        if (user && user.id) return;

        const handleScroll = () => {
            setScrolled(window.scrollY > 100);
        };

        handleScroll();
        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, [pathname, user]);

    // click outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setShowMenu(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    // smooth scroll to id
    useEffect(() => {
        const scrollToHash = () => {
            const hash = window.location.hash.substring(1);
            if (!hash) return;

            const element = document.getElementById(hash);
            const header = document.getElementById("appHeaderBar");
            const headerHeight = Math.max(header?.getBoundingClientRect().height || 0, 100);

            if (element) {
                const elementTop = element.getBoundingClientRect().top + window.scrollY;
                const scrollToPosition = elementTop - headerHeight;

                window.scrollTo({
                    top: scrollToPosition,
                    behavior: "smooth",
                });
            }
        };

        const timeoutId = setTimeout(scrollToHash, 700);
        return () => clearTimeout(timeoutId);
    }, []);

    // Handler to change locale in the URL
    const handleLocaleChange = (newLocale: string) => {
        // Remove current locale from pathname and prepend new locale
        const segments = pathname.split('/');
        if (segments[1].length === 2) {
            segments[1] = newLocale;
        } else {
            segments.splice(1, 0, newLocale);
        }
        const newPath = segments.join('/') || '/';
        router.push(newPath);
        if (props.setCurrentLanguage) props.setCurrentLanguage(newLocale);
    };

    // Helpers
    const handleNavigation = (path: string, tabIndex?: number) => {
        // Prepend locale if not already present
        const targetPath = path.startsWith(`/${locale}/`) ? path : `/${locale}${path.startsWith('/') ? path : '/' + path}`;
        router.push(targetPath);
        if (tabIndex !== undefined && setActiveTab) {
            setActiveTab(tabIndex);
        }
        setShowMenu(false); // Close mobile menu
    };

    const handleSignOut = async () => {
         try {
            setIsLoggingOut(true);
            await signOutUser();
            
            router.push("/");
        } catch (error) {
            console.error(t('error.signout_failed'), error);
            setIsLoggingOut(false); // Reset on error
        }
    };

    const handleGoBack = () => {
        router.back();
    };

    const handleLogoClick = () => {
        if (user?.type === "admin") {
            handleNavigation("/admin/dashboard", 0);
        } else if (user?.type === "tutor") {
            handleNavigation("/questions", 0);
        } else if (user?.type === "user") {
            handleNavigation("/ask", 0);
        } else {
            handleNavigation("/", 0);
        }
    };

    const renderMobileHeader = () => (
        <div className={styles.navMobileHead}>
            {user?.id && user.type === "tutor" && (
                <img
                    className={styles.myAccountIcon}
                    onClick={() => handleNavigation(SPECIAL_ROUTES.PROFILE, 5)}
                    src={'/icons/logged-user.svg'}
                    alt={'common.user_profile'}
                />
            )}
            <div className={styles.navMobileClose} onClick={() => setShowMenu(!showMenu)}>
                <img src={"/icons/burger-icon.svg"} alt={'Toggle menu'}/>
            </div>
        </div>
    );

    const renderGuestNavigation = () => (
        <div id={"mainNavigation"} className={showMenu ? `${styles.navigation}` : styles.navigation}>
            {renderMobileHeader()}

            <div
                className={showMenu ? `${styles.leftOptions} ${styles.showForceMenu}` : styles.leftOptions}
                ref={dropdownRef}
            >
                <a
                    href={'#subjects-section'}
                    data-href="subjects-section"
                    onClick={handleScrollToSection}
                    className={`${styles.navbarOption}`}
                >
                    <span>{t('homepage.features')}</span>
                </a>

                <a
                    href={'#how-it-works-section'}
                    data-href="how-it-works-section"
                    onClick={handleScrollToSection}
                    className={`${styles.navbarOption}`}
                >
                    <span>{t('homepage.how_it_works')}</span>
                </a>

                <a
                    data-href="pricing-section"  
                    onClick={handleScrollToSection}
                    className={`${styles.navbarOption}`}
                >
                    <span>{t('homepage.pricing')}</span>
                </a>
            </div>

            <div className={styles.actionOptions}>
                <BorderButton
                    label={t('auth.sign_in')}
                    onClick={() => handleNavigation("/login")}
                />
                <PrimaryButton
                    label={t('homepage.become_tutor')}
                    onClick={() => handleNavigation("/signup")}
                />
                {/* Language dropdown at the end */}
                {require('./LanguageDropdown').default &&
                    React.createElement(require('./LanguageDropdown').default, {
                        currentLocale: locale,
                        onLocaleChange: handleLocaleChange
                    })}
            </div>
        </div>
    );

    const renderPublicPageNavigation = () => (
        <div id={"mainNavigation"} className={showMenu ? `${styles.navigation}` : styles.navigation}>
            <PrimaryButton
                label={t('common.back')}
                onClick={() => handleNavigation("/")}
            />
        </div>
    );

    const renderAdminNavigation = () => (
        <>
            {renderMobileHeader()}

            <div
                className={showMenu ? `${styles.leftOptions} ${styles.showForceMenu}` : `${styles.leftOptions} ${styles.hiddenOptions}`}
                ref={dropdownRef}
            >
                <div
                    className={`${styles.navbarOption} ${activeTab === 0 ? styles.activeTab : ''}`}
                    onClick={() => handleNavigation("/admin/dashboard", 0)}
                >
                    {t('admin.dashboard')}
                </div>
                <div
                    className={`${styles.navbarOption} ${activeTab === 1 ? styles.activeTab : ''}`}
                    onClick={() => handleNavigation("/admin/users", 1)}
                >
                    {t('admin.users')}
                </div>
                <div
                    className={`${styles.navbarOption} ${activeTab === 2 ? styles.activeTab : ''}`}
                    onClick={() => handleNavigation("/admin/applicant-questions", 2)}
                >
                    {t('admin.test_questions')}
                </div>
                <div
                    className={`${styles.navbarOption} ${activeTab === 3 ? styles.activeTab : ''}`}
                    onClick={() => handleNavigation("/admin/user-questions", 3)}
                >
                    {t('admin.student_questions')}
                </div>
                <div
                    className={`${styles.navbarOption} ${activeTab === 4 ? styles.activeTab : ''}`}
                    onClick={() => handleNavigation("/admin/settings", 4)}
                >
                    {t('admin.settings')}
                </div>
                <div className={styles.navbarOption} onClick={handleSignOut}>
                     {isLoggingOut ? t('common.logging_out') : t('common.logout')}
                </div>
            </div>

            <div className={`${styles.actionOptions} ${styles.logoutAction}`}>
                <PrimaryButton
                    label= {isLoggingOut ? t('common.logging_out') : t('common.logout')}
                    onClick={handleSignOut}
                />
            </div>
        </>
    );

    const renderStudentNavigation = () => (
        <>
            <div
                className={showMenu ? `${styles.leftOptions} ${styles.showForceMenu}` : `${styles.leftOptions} ${styles.hiddenOptions}`}
                ref={dropdownRef}
            >
                {STUDENT_TABS.slice(0, 4).map((tab) => (
                    <Link
                        key={tab.name}
                        href={tab.path}
                       className={`${styles.navbarOption} ${activeTab === tab.index ? styles.activeTab : ''}`}
                        data-pathname={tab.path}
                    >
                        {tab.name}
                        <img src={tab.icon} alt={`${tab.name} icon`} />
                    </Link>
                ))}
                <div
                    className={`${styles.navbarOption} ${isLoggingOut ? styles.disabled : ''}`}
                    onClick={handleSignOut}
                >
                    {isLoggingOut ? 'Logging out...' : 'Logout'}
                    <img src="/icons/menu/gear-icon.svg" alt="Logout icon" />
                </div>
            </div>
            <div className={`${styles.actionOptions} ${styles.logoutAction}`}>
                <PrimaryButton
                    label={isLoggingOut ? 'Logging out...' : (pathname === '/payment' ? 'Back' : 'Logout')}
                    onClick={pathname === '/payment' ? handleGoBack : handleSignOut}
                    disabled={isLoggingOut}
                    style={{ padding: '6px 25px 8px', minWidth: '109px' }}
                />
            </div>
        </>
    );

    const renderTutorNavigation = () => (
        <>
            <div
                className={showMenu ? `${styles.leftOptions} ${styles.showForceMenu}` : `${styles.leftOptions} ${styles.hiddenOptions}`}
                ref={dropdownRef}
            >
                {TUTOR_TABS.map((tab) => (
                    <div
                        key={tab.name}
                        className={`${styles.navbarOption} ${activeTab === tab.index ? styles.activeTab : ''}`}
                        onClick={() => handleNavigation(tab.path, tab.index)}
                    >
                        {tab.name}
                        <img src={tab.icon} alt={`${tab.name} icon`}/>
                    </div>
                ))}
                <div className={styles.navbarOption} onClick={handleSignOut}>
                    {t('common.logout')}
                    <img src="/icons/menu/gear-icon.svg" alt={t('common.logout_icon')} />
                </div>
            </div>
            <div className={`${styles.actionOptions} ${styles.logoutAction}`}>
                <PrimaryButton
                    label={t('common.logout')}
                    onClick={handleSignOut}
                    disabled={isLoggingOut}
                    style={{ padding: '6px 25px 8px', minWidth: '109px' }}
                />
                <img
                    className={styles.myAccountIcon}
                    onClick={() => handleNavigation(SPECIAL_ROUTES.PROFILE, 5)}
                    src={"/icons/logged-user.svg"}
                    alt={t('common.user_profile')}
                />
            </div>
        </>
    );

    const renderUserNavigation = () => {
         if (!user?.id || fetching) return null;
        
        switch (user.type) {
            case "user":
                return renderStudentNavigation();
            case "tutor":
                return renderTutorNavigation();
            default:
                return null;
        }
    };

    const isAdminHome = user && user?.type === 'admin' && pathname === '/';
    const shouldFix = user && user?.type
  const position = shouldFix ? 'fixed' : (isAdminHome ? 'fixed' : 'relative');

  

    return (
        <AppBar
            id={"appHeaderBar"}
            position="fixed"
            color="transparent"
            elevation={0}
            className={scrolled ? `${styles.topNavbar} ${styles.topNavbarFixed}` : styles.topNavbar}
            style={{position, background: '#6BCC09'}} 
        >
            <div className={`${styles.navWrapper} mainContainer ${isPublicPage ? styles.navPublicWrapper : ''}`}>
                <div className={styles.logoWrapper} onClick={handleLogoClick}>
                    <img className={styles.mainLogo} src={"/images/logo.svg"} alt={"Company logo"}/>
                </div>

               {isPublicPage && renderPublicPageNavigation()}
                {!fetching && !user?.id && !isPublicPage && renderGuestNavigation()}
                {adminUser && renderAdminNavigation()}
                {!adminUser && renderMobileHeader()}
                {!adminUser && renderUserNavigation()}
            </div>
        </AppBar>
    );
}

export default Navbar;