import React, { FC, useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import Container from "@mui/material/Container";
import styles from "@/src/styles/footer.module.css";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import { handleScrollToSection } from "@/src/Utils/helpers";
import { useTranslation } from "@/src/Utils/i18n";

interface FooterProps {
  id?: any;
  userType?: string;
  isLegalPage?: boolean;
  isPublicPage?: boolean;
  setActiveTab?: (tabName: any) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
}

const Footer: FC<FooterProps> = ({
  t,
  id,
  userType,
  isLegalPage,
  isPublicPage,
  setActiveTab,
}) => {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [effectiveUserType, setEffectiveUserType] = useState(userType || "");

  // Correctly call useTranslation to get locale
  const { locale } = useTranslation();

  // Handle hydration mismatch by waiting for component to mount
  useEffect(() => {
    setMounted(true);

    // Only access localStorage after component mounts
    if (!userType && typeof window !== "undefined") {
      const savedUserType =
        localStorage.getItem("HMWK_DEFAULT_USER_TYPE") || "";
      setEffectiveUserType(savedUserType);
    }
  }, [userType]);

  const isStudentUser = effectiveUserType === "user";
  const isTutorUser = effectiveUserType === "tutor";

  const handleGotoPage = (path) => {
    router.push(path);
  };

  // Render a consistent fallback until mounted
  if (!mounted) {
    return (
      <footer
        className={
          isLegalPage
            ? `${styles.footer} ${styles.legalFooter}`
            : isPublicPage
              ? `${styles.footer} ${styles.publicFooter}`
              : styles.footer
        }
        id={id}
      >
        <Container maxWidth={false} className="mainContainer">
          {!isPublicPage && (
            <div className={styles.footerCtaContainer}>
              <h2 className={styles.footerHeading}>
                {t("homepage.questions")}
              </h2>
              <div className={styles.footerSubheading}>
                {t("homepage.get_help_feedback")}
              </div>
              <a
                href="mailto:<EMAIL>"
                style={{ textDecoration: "none" }}
              >
                <PrimaryButton
                  size={isLegalPage ? "whiteBtn" : ""}
                  icon={
                    isLegalPage
                      ? "footer-email-blue-icon.svg"
                      : "footer-email-icon.svg"
                  }
                  label={t("homepage.contact_us")}
                />
              </a>
            </div>
          )}

          <hr className={styles.footerDivider} />

          <div className={styles.footerLinksContainer}>
            {/* Show default public links during SSR */}
            <a data-href="subjects-section" onClick={handleScrollToSection}>
              {t("homepage.subjects")}
            </a>
            <a data-href="pricing-section" onClick={handleScrollToSection}>
              {t("homepage.pricing")}
            </a>

            <Link href={`/${locale}/help`} legacyBehavior>
              <a className={styles.footerLink}>{t("common.help")}</a>
            </Link>

            <Link href={`/${locale}/terms`} legacyBehavior>
              <a className={styles.footerLink}>{t("homepage.terms")}</a>
            </Link>

            <Link href={`/${locale}/privacy`} legacyBehavior>
              <a className={styles.footerLink}>{t("homepage.privacy")}</a>
            </Link>
          </div>

          <div className={styles.footerCopyright}>
            {t("homepage.copyright")}
          </div>
        </Container>
      </footer>
    );
  }

  return (
    <footer
      className={
        isLegalPage
          ? `${styles.footer} ${styles.legalFooter}`
          : isPublicPage
            ? `${styles.footer} ${styles.publicFooter}`
            : styles.footer
      }
      id={id}
    >
      <Container maxWidth={false} className="mainContainer">
        {!isPublicPage ?
                    <div className={styles.footerCtaContainer}>
                        <h2 className={styles.footerHeading}>{t("homepage.questions")}</h2>
                        <div className={styles.footerSubheading}>{t("homepage.get_help_feedback")}
                        </div>
                        <a href="mailto:<EMAIL>" style={{textDecoration: 'none'}}>
                            <PrimaryButton size={isLegalPage ? 'whiteBtn' : ''}
                                           icon={isLegalPage ? 'footer-email-blue-icon.svg' : 'footer-email-icon.svg'}
                                           label={t("homepage.contact_us")}/>
                        </a>
                    </div> : ''
                }


        <hr className={styles.footerDivider} />

        <div className={styles.footerLinksContainer}>
          {isStudentUser ? (
            <>
              <a
                onClick={() => handleGotoPage(`/${locale}/my-questions`)}
                className={styles.footerLink}
              >
                {t("student.history")}
              </a>
               <a
                onClick={() => handleGotoPage(`/${locale}/packs`)}
                className={styles.footerLink}
              >
                {t("navigation.pricing")}
              </a>
              {/* <Link href={{ pathname: `/${locale}`, hash: "subjects-section" }} legacyBehavior>
                <a className={styles.footerLink}>{t("homepage.subjects")}</a>
              </Link> */}
              {/* <Link href={{ pathname: `/${locale}`, hash: "pricing-section" }} legacyBehavior>
                <a className={styles.footerLink}>{t("navigation.pricing")}</a>
              </Link> */}
            </>
          ) : isTutorUser ? (
            <>
              <a
                onClick={() => handleGotoPage(`/${locale}/profile`)}
                className={styles.footerLink}
              >
                {t("navigation.profile")}
              </a>
              <a
                onClick={() => handleGotoPage(`/${locale}/registration`)}
                className={styles.footerLink}
              >
                {t("navigation.registration")}
              </a>
            </>
          ) : !effectiveUserType || effectiveUserType === "" || isPublicPage ? (
            <>
              <Link href={{ pathname: `/${locale}`, hash: "subjects-section" }} legacyBehavior>
                <a className={styles.footerLink}>{t("homepage.subjects")}</a>
              </Link>
              <Link href={{ pathname: `/${locale}`, hash: "pricing-section" }} legacyBehavior>
                <a className={styles.footerLink}>{t("navigation.pricing")}</a>
              </Link>
            </>
          ) : (
            <>
              <Link href={{ pathname: `/${locale}`, hash: "subjects-section" }} legacyBehavior>
                <a className={styles.footerLink}>{t("homepage.subjects")}</a>
              </Link>
              <Link href={{ pathname: `/${locale}`, hash: "pricing-section" }} legacyBehavior>
                <a className={styles.footerLink}>{t("navigation.pricing")}</a>
              </Link>
            </>
          )}

          <Link href={`/${locale}/help`} legacyBehavior>
            <a className={styles.footerLink}>{t("common.help")}</a>
          </Link>

          <Link href={`/${locale}/terms`} legacyBehavior>
            <a className={styles.footerLink}>{t("homepage.terms")}</a>
          </Link>

          <Link href={`/${locale}/privacy`} legacyBehavior>
            <a className={styles.footerLink}>{t("homepage.privacy")}</a>
          </Link>
        </div>

        <div className={styles.footerCopyright}>
          {t("homepage.copyright")}
        </div>
      </Container>
    </footer>
  );
};

export default Footer;
