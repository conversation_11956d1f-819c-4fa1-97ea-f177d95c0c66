'use client';

import React, {useCallback, useContext, useRef, useState} from 'react';
import Link from 'next/link';
import {usePathname, useRouter} from 'next/navigation';
import {signOutUser} from '@/src/Firebase/firebase.utils';
import {STUDENT_TABS} from '@/src/Constants/tabNavigation';
import PrimaryButton from '@/src/Components/Buttons/PrimaryButton';
import {UserContext} from '@/src/Contexts/UserContext';
import LanguageDropdown from "@/src/Components/Navigation/LanguageDropdown";
import { useTranslation } from 'next-i18next';
import styles from '@/src/styles/navbar.module.css';

export default function NavbarStudent() {
    const { t } = useTranslation();
    const router = useRouter();
    const pathname = usePathname();
    const {user, isLoggingOut} = useContext(UserContext);

    const dropdownRef = useRef<HTMLDivElement>(null);
    const [showMenu, setShowMenu] = useState(false);

    const remQuestions = user?.questions || 0;
    const isPaidUser = remQuestions > 0;

    const handleLogoClick = () => {
        router.push('/');
    };

    const handleGoBack = () => {
        router.back();
    };

    const handleSignOut = useCallback(async () => {
        try {
            await signOutUser();
            router.push('/');
        } catch (error) {
            console.error('Error signing out:', error);
        }
    }, [router]);

    const renderMobileHeader = () => (
        <div className={styles.navMobileHead}>
            <div className={styles.navMobileClose} onClick={() => setShowMenu(!showMenu)}>
                <img src="/icons/burger-icon.svg" alt="Toggle menu"/>
            </div>
        </div>
    );

    return (
        <>
            <div id="appHeaderBar" className={styles.appHeaderBar}>
                <div className={`${styles.navWrapper} mainContainer`}>
                    <div className={styles.logoWrapper} onClick={handleLogoClick}>
                        <img className={styles.mainLogo} src="/images/logo.svg" alt="Company logo"/>
                    </div>

                    {renderMobileHeader()}

                    <div
                        className={showMenu ? `${styles.leftOptions} ${styles.showForceMenu}` : `${styles.leftOptions} ${styles.hiddenOptions}`}
                        ref={dropdownRef}>
                        {STUDENT_TABS.slice(0, 4).map((tab) => (
                            <Link
                                key={tab.name}
                                href={tab.path}
                                className={`${styles.navbarOption}`}
                                data-pathname={tab.path}
                            >
                                {t(`navigation.${tab.name.toLowerCase()}`)}
                                <img src={tab.icon} alt={`${t(`navigation.${tab.name.toLowerCase()}`)} icon`}/>
                            </Link>
                        ))}

                        <div
                            className={`${styles.navbarOption} ${isLoggingOut ? styles.disabled : ''}`}
                            onClick={handleSignOut}
                        >
                            {isLoggingOut ? t('common.logging_out') : t('common.logout')}
                            <img src="/icons/menu/gear-icon.svg" alt={t('common.logout_icon')}/>
                        </div>
                    </div>

                    <div className={`${styles.actionOptions} ${styles.logoutAction}`}>
                        <PrimaryButton
                            label={
                                isLoggingOut
                                    ? t('common.logging_out')
                                    : isPaidUser
                                        ? pathname === '/payment'
                                            ? t('common.back')
                                            : t('common.logout')
                                        : t('common.cancel')
                            }
                            onClick={
                                isPaidUser && pathname === '/payment' ? handleGoBack : handleSignOut
                            }
                            disabled={isLoggingOut}
                            style={{padding: '6px 25px 8px', minWidth: '109px'}}
                        />
                        <LanguageDropdown />
                    </div>
                </div>
            </div>

        </>
    );
}