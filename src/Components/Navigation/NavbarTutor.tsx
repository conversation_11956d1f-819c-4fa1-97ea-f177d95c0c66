'use client';

import React, { useCallback, useContext, useRef, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { signOutUser } from '@/src/Firebase/firebase.utils';
import { SPECIAL_ROUTES, TUTOR_TABS } from '@/src/Constants/tabNavigation';
import PrimaryButton from '@/src/Components/Buttons/PrimaryButton';
import { UserContext } from '@/src/Contexts/UserContext';
import styles from '@/src/styles/navbar.module.css';
import LanguageDropdown from '@/src/Components/Navigation/LanguageDropdown';
import { useTranslation } from '@/src/Utils/i18n';

export default function NavbarTutor() {
    const router = useRouter();
    const { isLoggingOut } = useContext(UserContext);
    const { t } = useTranslation();
    const dropdownRef = useRef<HTMLDivElement>(null);
    const [showMenu, setShowMenu] = useState(false);

    const handleLogoClick = () => {
        router.push('/');
    };

    const handleSignOut = useCallback(async () => {
        try {
            await signOutUser();
            router.push('/');
        } catch (error) {
            console.error('Error signing out:', error);
        }
    }, [router]);

    const renderMobileHeader = () => (
        <div className={styles.navMobileHead}>
            <Link href={SPECIAL_ROUTES.PROFILE}>
                <img
                    className={styles.myAccountIcon}
                    src={'/icons/logged-user.svg'}
                    alt={'User profile'}
                />
            </Link>
            <div className={styles.navMobileClose} onClick={() => setShowMenu(!showMenu)}>
                <img src={"/icons/burger-icon.svg"} alt={"Toggle menu"} />
            </div>
        </div>
    );

    return (
        <>
            <div id="appHeaderBar" className={styles.appHeaderBar}>
                <div className={`${styles.navWrapper} mainContainer`}>
                    <div className={styles.logoWrapper} onClick={handleLogoClick}>
                        <img className={styles.mainLogo} src="/images/logo.svg" alt="Company logo" />
                    </div>

                    {renderMobileHeader()}

                    <div className={showMenu ? `${styles.leftOptions} ${styles.showForceMenu}` : `${styles.leftOptions} ${styles.hiddenOptions}`} ref={dropdownRef}>
                        {TUTOR_TABS.map((tab) => (
                            <Link
                                key={tab.name}
                                href={tab.path}
                                className={`${styles.navbarOption}`}
                                data-pathname={tab.path}
                            >
                                {t(`navigation.${tab.name}`)}
                                <img src={tab.icon} alt={`${t('common.menu_icon')}`} />
                            </Link>
                        ))}
                        <div
                            className={`${styles.navbarOption} ${isLoggingOut ? styles.disabled : ''}`}
                            onClick={handleSignOut}
                        >
                            {isLoggingOut ? t('common.logging_out') : t('common.logout')}
                            <img src="/icons/menu/gear-icon.svg" alt={t('common.logout_icon')} />
                        </div>
                    </div>

                    <div className={`${styles.actionOptions} ${styles.logoutAction}`}>
                        <PrimaryButton
                            label={isLoggingOut ? t('common.logging_out') : t('common.logout')}
                            onClick={handleSignOut}
                            disabled={isLoggingOut}
                            style={{ padding: '6px 25px 8px', minWidth: '109px' }}
                        />
                        <Link href={SPECIAL_ROUTES.PROFILE}>
                            <img
                                className={styles.myAccountIcon}
                                src="/icons/logged-user.svg"
                                alt={t('common.user_profile')}
                                style={{ paddingTop: '6px' }}
                            />
                        </Link>
                        <LanguageDropdown />
                    </div>
                </div>
            </div>

        </>
    );
}