import React from 'react';
import {usePathname} from 'next/navigation';
import styles from '@/src/styles/legal.module.css';
import { useTranslation } from '@/src/Utils/i18n';
import Link from 'next/link';


const TabsLegal: React.FC = () => {
    const pathname = usePathname();
    const { t ,locale} = useTranslation();
const tabs = [
    { name: 'terms', path: `/${locale}/terms` },
    { name: 'privacy', path: `/${locale}/privacy` },
    { name: 'help', path: `/${locale}/help` },
    
];
    return (
        <div className={styles.tabsContainer}>
            <div className={'mainContainer navbarOption'}>
                <nav className={styles.tabsNav}>
                    {tabs.map((tab) => (
                        <Link key={tab.path} href={tab.path} legacyBehavior>
                            <a className={`${styles.tabLink} ${pathname === tab.path ? styles.activeTab : ''}`}>
                                {t('navigation_tabs_legal.' + tab.name)}
                            </a>
                        </Link>
                    ))}
                </nav>
            </div>
        </div>
    );
};

export default TabsLegal;