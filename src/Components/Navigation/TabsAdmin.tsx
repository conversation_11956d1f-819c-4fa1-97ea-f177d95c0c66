import React, { useMemo } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import styles from '@/src/styles/legal.module.css';
import { useTranslation } from '@/src/Utils/i18n';




const AdminTabs: React.FC = () => {
    const pathname = usePathname();
    const router = useRouter();
    const { t, locale } = useTranslation();

    const tabs = useMemo(() => {
        return [
            {
                name: 'Dashboard',
                icon: '/icons/menu/house-icon.svg',
                path: `/${locale}/admin/dashboard`,
                activePaths: [`/${locale}/admin/dashboard`]
            },
            {
                name: 'Users',
                icon: '/icons/menu/users-icon.svg',
                path: `/${locale}/admin/users`,
                activePaths: [`/${locale}/admin/users`, `/${locale}/admin/edit-user`]
            },
            {
                name: 'Test Questions',
                icon: '/icons/menu/admin-applicant-icon.svg',
                path: `/${locale}/admin/applicant-questions`,
                activePaths: [`/${locale}/admin/applicant-questions`]
            },
            {
                name: 'Student Questions',
                icon: '/icons/menu/register-icon.svg',
                path: `/${locale}/admin/user-questions`,
                activePaths: [`/${locale}/admin/user-questions`]
            },
            {
                name: 'Settings',
                icon: '/icons/menu/gear-icon.svg',
                path: `/${locale}/admin/settings`,
                activePaths: [`/${locale}/admin/settings`]
            },
        ]
    }, [locale]);


    const handleGoto = (tab) => {
        router.push(tab.path);
    }

    const tabKeys = [
        'dashboard',
        'users',
        'test_questions',
        'student_questions',
        'settings'
    ];

    return (
        <div className={`${styles.tabsContainer} ${styles.tabsStudent}`}>
            <div className={'mainContainer'}>
                <div className={styles.tabsHolder}>
                    <nav className={styles.tabsNav}>
                        {tabs.map((tab, index) => (
                            <a
                                key={`${tab.name}-${index}`}
                                onClick={() => handleGoto(tab)}
                                className={`${styles.tabLink} ${styles.tabGreenLink} ${tab.activePaths?.includes(pathname)
                                        ? styles.activeTab
                                        : ''
                                    }`}
                            >
                                <img src={tab.icon} alt={t('common.menu_icon')} />
                                <span>{t(`navigation_tabs_admin.${tabKeys[index]}`)}</span>
                            </a>
                        ))}
                    </nav>
                </div>
            </div>
        </div>
    );
};

export default AdminTabs;