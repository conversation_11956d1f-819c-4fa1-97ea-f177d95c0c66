import React, { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import styles from '@/src/styles/legal.module.css';
import { TUTOR_TABS } from "@/src/Constants/tabNavigation";
import { useTranslation } from '@/src/Utils/i18n';
import { calculateTutorRegProgress } from '@/src/Utils/helpers';

interface TutorTabsProps {
    user: any;
    active: number;
    setActiveTab?: (tab: number) => void;
}

interface Tab {
    path: string;
    index: number;
}

const TabsTutor: React.FC<TutorTabsProps> = ({ user, active, setActiveTab }) => {
    const router = useRouter();
    const { t, locale } = useTranslation();
    const [showRegistrationDot, setShowRegistrationDot] = useState(false);

    useEffect(() => {
        if (!user) {
            setShowRegistrationDot(false);
            return;
        }
        const {
            firstName,
            lastName,
            phoneNumber,
            dateOfBirth,
            educationLevel,
            tutorExperience,
            country,
            certificateNumber
        } = user;
        const progressPercentage = calculateTutorRegProgress({
            firstName,
            lastName,
            phoneNumber,
            dateOfBirth,
            educationLevel,
            tutorExperience,
            country,
            certificateNumber,
        });
        setShowRegistrationDot(progressPercentage < 100);
    }, [user, locale]);

    const handleTabClick = useCallback((tab: Tab) => {
       router.push(`/${locale}/${tab.path}`);
        setActiveTab?.(tab.index);
    }, [router, setActiveTab, locale]);

    return (
        <div className={`${styles.tabsContainer} ${styles.tabsStudent}`}>
            <div className="mainContainer navbarOption">
                <div className={styles.tabsHolder}>
                    <nav className={styles.tabsNav}>
                        {TUTOR_TABS.map((tab) => {
                            const isActive = tab.index === active;
                            const isRegistration = tab.name === 'registration';

                            return (
                                <div
                                    key={tab.name}
                                     className={`${styles.tabLink} ${styles.tabGreenLink} ${isActive ? styles.activeTab : ''}`}
                                    data-pathname={tab.path}
                                    onClick={() => handleTabClick(tab)}
                                >
                                   {isRegistration ? (
                                        <span
                                            className={`${styles.tabRedDotBox} ${showRegistrationDot ? styles.tabRedDotActive : ''}`}>
                      <img src={tab.icon} alt="menu icon"/>
                    </span>
                                    ) : (
                                        <img src={tab.icon} alt="menu icon"/>
                                    )}
                                    <span>{t('tutor.' + tab.name)}</span>
                                </div>
                            );
                        })}
                    </nav>
                </div>
            </div>
        </div>
    );
};

export default TabsTutor;