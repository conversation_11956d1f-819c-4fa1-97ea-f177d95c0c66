import React, {useContext, useState, useEffect, useRef} from 'react';
import AppBar from '@mui/material/AppBar';
import Link from 'next/link';
import {useRouter} from 'next/navigation';
import styles from '@/src/styles/navbar.module.css';
import PrimaryButton from '@/src/Components/Buttons/PrimaryButton';
import {UserContext} from "@/src/Contexts/UserContext";
import LanguageDropdown from "@/src/Components/Navigation/LanguageDropdown";
import { useTranslation } from '@/src/Utils/i18n'; // Use your custom i18n hook
import type { Locale } from '@/src/Utils/i18n';

const StaticPageNavbar: React.FC = () => {
    const { t, locale, setLocale } = useTranslation(); // Get locale and setLocale from your i18n
    const {user} = useContext(UserContext);
    const [showMenu, setShowMenu] = useState(false);
    const [mounted, setMounted] = useState(false);
    const router = useRouter();
    const dropdownRef = useRef() as React.MutableRefObject<HTMLDivElement>;

    let localUserType: any = "";
    if (typeof window !== "undefined") {
        localUserType = localStorage.getItem("HMWK_DEFAULT_USER_TYPE");

        if(localUserType && localUserType === 'user'){
          //  isStudentUser = true;
        }
        else if(localUserType && localUserType === 'tutor'){
            // isTutorUser = true;
        }
    }

    // Handle hydration
    useEffect(() => {
        setMounted(true);
    }, []);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setShowMenu(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    const handleBackWithReload = () => {
        sessionStorage.setItem('reloadOnBack', 'true');
        window.history.back();
    };

    const handleLocaleChange = (newLocale: Locale) => {
        setLocale(newLocale);
    };

    const renderMobileHeader = () => {
        return (
            <div className={styles.navMobileHead}>
                {user && user.id && user.type === 'tutor' &&
                    <img
                        className={styles.myAccountIcon}
                        onClick={() => {
                            router.push(`/${locale}/profile`);
                        }}
                        src={'/icons/logged-user.svg'} alt={'logged-user.svg'}
                    />
                }
                <div className={styles.navMobileClose} onClick={() => setShowMenu(!showMenu)}>
                    <img src={"/icons/burger-icon.svg"} alt={"menu-toggle"}/>
                </div>
            </div>
        );
    };

    let userIn = !!(user && user.id);

    // Prevent hydration mismatch by not rendering until mounted
    if (!mounted) {
        return (
            <AppBar
                position="static"
                color="transparent"
                elevation={0}
                className={styles.staticTopNavbar}
            >
                <div className={`${styles.navWrapper} mainContainer`}>
                    <Link href={"/"} prefetch={false}>
                        <img className={styles.mainLogo} src={"/images/logo.svg"} alt={"logo"}/>
                    </Link>
                    {/* Show minimal content during SSR */}
                    <div className={styles.navigation} style={{marginLeft: 'auto'}}>
                        <div className={styles.actionOptions} style={{marginLeft: 'auto'}}>
                            <PrimaryButton
                                label="Login"
                                size={'whiteBtn'}
                                onClick={() => router.push(`/${locale}/login`)}
                            />
                        </div>
                    </div>
                </div>
            </AppBar>
        );
    }

    return (
        <AppBar
            position="static"
            color="transparent"
            elevation={0}
            className={styles.staticTopNavbar}
        >
            <div className={`${styles.navWrapper} mainContainer`}>
                <Link href={"/"} prefetch={false}><img className={styles.mainLogo} src={"/images/logo.svg"} alt={"logo"}/></Link>

                {renderMobileHeader()}

                <div className={showMenu ? `${styles.leftOptions} ${styles.showForceMenu}` : styles.leftOptions} ref={dropdownRef}>
                    {!userIn && (
                        <Link href={`/${locale}/login`} prefetch={false} className={`${styles.navbarOption}`}>{t('navbar_leagl.login')}</Link>
                    )}
                    <Link href={`/${locale}/terms`} prefetch={false} className={`${styles.navbarOption}`}>{t('navbar_leagl.terms')}</Link>
                    <Link href={`/${locale}/privacy`} prefetch={false} className={`${styles.navbarOption}`}>{t('navbar_leagl.privacy')}</Link>
                    <Link href={`/${locale}/help`} prefetch={false} className={`${styles.navbarOption}`}>{t('navbar_leagl.help')}</Link>
                    {!userIn && (
                        <Link
                            href={`/${locale}/signup`}
                            prefetch={false}
                            className={`${styles.navbarOption} ${styles.navRegOption}`}
                        >
                            {t('navbar_leagl.registerAsTutor')}
                        </Link>
                    )}
                </div>

                <div id="staticNavigation" className={showMenu ? `${styles.navigation}` : styles.navigation} style={{marginLeft: 'auto'}}>
                    <div className={styles.actionOptions} style={{marginLeft: 'auto'}}>
                        <PrimaryButton
                            label={user && user.id ? 'Back' : 'Login'}
                            size={'whiteBtn'}
                            onClick={() => {
                                user && user.id ? handleBackWithReload() : router.push(`/${locale}/login`)
                            }}
                        />
                        {user && user.id && user.type === 'tutor' &&
                            <img
                                className={styles.myAccountIcon}
                                onClick={() => {
                                    router.push(`/${locale}/profile`);
                                }}
                                src={'/icons/logged-user.svg'} alt={'logged-user.svg'}
                            />
                        }
                        <LanguageDropdown 
                            currentLocale={locale} 
                            onLocaleChange={handleLocaleChange}
                        />
                    </div>
                </div>
            </div>
        </AppBar>
    );
};

export default StaticPageNavbar;