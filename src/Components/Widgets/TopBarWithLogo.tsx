import React from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import styles from "@/src/styles/login.module.css";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import {TopBarProps} from "@/src/Types";

const TopBarWithLogo: React.FC<TopBarProps> = ({ buttonLabel, buttonUrl }) =>
{
    const router = useRouter();
    const handleClick = () => {
        if (buttonUrl) {
            router.push(buttonUrl);
        }
    };

    return (
        <div className={styles.topAuthBar}>
            <Link href={"/"} prefetch={false}>
                <img className={styles.topAuthBarLogo} src={"/images/logo-green.svg"} alt={"Logo"} />
            </Link>

            <PrimaryButton
                onClick={handleClick}
                size={'greenBtn'}
                label={buttonLabel}
            />
        </div>
    )
};

export default TopBarWithLogo;
