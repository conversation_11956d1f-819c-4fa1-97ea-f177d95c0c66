import React, {useContext, useEffect} from "react";
import {useTranslation} from "@/src/Utils/i18n";
import {
    isoLanguagesMobileNames,
    languageCodes,
    nativeLanguageNames,
} from "@/src/Utils/translationObject";
import {Language} from "@mui/icons-material";
import {Box, FormControl, MenuItem, Select, SvgIcon} from "@mui/material";
import {usePathname, useRouter} from "next/navigation";
import {UserContext} from "@/src/Contexts/UserContext";

const ExpandMoreIcon = () => {
    return (
        <span className={'dropdownIcon'}>
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 8L10.5 11.5L14 8" stroke="black" stroke-linecap="round"/>
        </svg>
      </span>
    )
}

function LanguageSelection(props: { sx?: React.CSSProperties; id: string }) {
    const {t} = useTranslation();
    const {setFetching, setLangSwitch} = useContext(UserContext);
    const router = useRouter();

    const activeLanguage = typeof window !== "undefined" ? localStorage.getItem("HMWK_LANGUAGE") : "";
    const pathname = usePathname();

    useEffect(() => {
        if (activeLanguage === "") {
            localStorage.setItem(`HMWK_LANGUAGE`, "");
        } else if (activeLanguage === "es") {
            // router.push("/es");
        }
    }, []);

    const handleChangeEnglish = () => {
        localStorage.setItem(`HMWK_LANGUAGE`, "");
        if (pathname === "/user/my-account") {
            router.refresh();
        } else {
            setLangSwitch(true);
            router.push("/");
        }
    };

    const handleChangeLanguage = (code: string) => {
        localStorage.setItem(`HMWK_LANGUAGE`, code);
        if (pathname === "/user/my-account") {
            router.refresh();
        } else {
            setLangSwitch(true);
            setFetching(true);
            router.push(`/${code}`);
        }
    };

    return (
        <>
            <FormControl sx={props.sx} id="language-select-desktop">
                <Select
                    id="language-select"
                    size="small"
                    IconComponent={ExpandMoreIcon}
                    value={
                        props.id === "" || !props.id
                            ? t('common.international')
                            : nativeLanguageNames[
                                props.id as keyof typeof nativeLanguageNames
                                ]
                    }
                    sx={{borderRadius: "8px"}}
                    renderValue={(value: string) => {
                        return (
                            <Box sx={{display: "flex", gap: 1}}>
                                <SvgIcon color="action">
                                    <Language/>
                                </SvgIcon>
                                <span>{props.id === "" || !props.id
                                    ? t('common.international')
                                    : nativeLanguageNames[
                                        props.id as keyof typeof nativeLanguageNames
                                        ]}</span>
                            </Box>
                        );
                    }}
                >
                    {languageCodes
                        ? languageCodes.map((lang) => {
                            let langName = lang.name;
                            if (lang.name === "International") {
                                langName = t('common.international');
                            }
                            return (
                                <MenuItem
                                    key={lang.name}
                                    value={lang.name}
                                    onClick={() =>
                                        lang.name === "International"
                                            ? handleChangeEnglish()
                                            : handleChangeLanguage(lang.code)
                                    }
                                >
                                    {langName}
                                </MenuItem>
                            );
                        })
                        : null}
                </Select>
            </FormControl>

            <FormControl sx={props.sx} id="language-select-mobile">
                <Select
                    id="language-select"
                    sx={{borderRadius: "8px"}}
                    IconComponent={ExpandMoreIcon}
                    value={
                        props.id === "" || !props.id
                            ? "EN"
                            : isoLanguagesMobileNames[
                                props.id as keyof typeof nativeLanguageNames
                                ]
                    }
                    renderValue={(value: string) => {
                        return (
                            <Box sx={{display: "flex", gap: 1}}>
                                <SvgIcon color="action">
                                    <Language/>
                                </SvgIcon>
                                <span>{value}</span>
                            </Box>
                        );
                    }}
                >
                    {languageCodes
                        ? languageCodes.map((lang) => {
                            let langName = lang.name;
                            if (lang.name === "International") {
                                langName = t('common.international');
                            }
                            return (
                                <MenuItem
                                    key={lang.name}
                                    value={lang.name}
                                    onClick={() =>
                                        lang.name === "International"
                                            ? handleChangeEnglish()
                                            : handleChangeLanguage(lang.code)
                                    }
                                >
                                    {langName}
                                </MenuItem>
                            );
                        })
                        : null}
                </Select>
            </FormControl>
        </>
    );
}

export default LanguageSelection;
