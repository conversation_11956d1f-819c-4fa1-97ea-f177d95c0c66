import React from 'react';
import styles from "@/src/styles/widgets.module.css";
import {NotificationProps} from "@/src/Types";

type NotificationType = 'info' | 'warning' | 'error' | 'success';

const defaultIcons: Record<NotificationType, string> = {
    info: '/icons/info-blue-icon.svg',
    warning: '/icons/warning-yellow-icon.svg',
    error: '/icons/error-red-icon.svg',
    success: '/icons/payment-right.svg',
};

export const Notification: React.FC<NotificationProps> = ({
  type = 'info',
  title,
  message,
  iconSrc,
  style,
}) =>
{
    const icon = iconSrc || defaultIcons[type];

    return (
        <div className={`${styles.notifyBox} ${styles[type]}`} style={style}>
            <div className={styles.notifyIcon}>
                <img src={icon} alt={`${type} icon`} />
            </div>
            <div className={styles.notifyInfo}>
                <div className={styles.notifyTitle}>{title}</div>
                <div className={styles.notifyText}>{message}</div>
            </div>
        </div>
    );
};
