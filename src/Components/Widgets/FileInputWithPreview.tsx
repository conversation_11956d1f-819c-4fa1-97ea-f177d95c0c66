import React, {ChangeEvent, useEffect, useRef, useState} from "react";
import styles from "@/src/styles/widgets.module.css";
import homeStyles from "@/src/styles/home.module.css";
import {useTranslation} from "@/src/Utils/i18n";

interface FieldProps {
    simpleChange?: boolean;
    onChange: (value: object | null) => void;
    settingsPage?: boolean;
    profilePage?: boolean;
    userImg?: string;
    disabled?: boolean;
}

const FileInputWithPreview: React.FC<FieldProps> = ({
                                                        onChange,
                                                        simpleChange = false,
                                                        profilePage = false,
                                                        settingsPage = false,
                                                        userImg = "",
                                                        disabled = false,
                                                    }) => {
    const { t } = useTranslation();
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const lastUserImgRef = useRef<string>(userImg);
    const [iconState, setIconState] = useState<'add' | 'edit'>(
        !userImg || userImg === '/icons/profile/avatar.png' ? 'add' : 'edit'
    );

    const handleRemove = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setPreviewUrl(null);
        setSelectedFile(null);
        setIconState('add');
        if (onChange) {
            onChange(null);
        }
    };

    const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            setSelectedFile(file);
            setIconState('edit');

            if (file.type.startsWith("image/")) {
                if (previewUrl && previewUrl.startsWith('blob:')) {
                    URL.revokeObjectURL(previewUrl);
                }

                const objectUrl = URL.createObjectURL(file);
                setPreviewUrl(objectUrl);

                if (onChange) {
                    onChange(file);
                }
            } else {
                setPreviewUrl(null);
            }
        }
    };

    const getProfileImageIcon = () => {
        return iconState === 'add'
            ? '/icons/profile/green-add-icon.svg'
            : '/icons/profile/blue-pen-icon.svg';
    };

    useEffect(() => {
        if (userImg !== lastUserImgRef.current) {
            lastUserImgRef.current = userImg;

            if (!userImg || userImg === '/icons/profile/avatar.png') {
                setIconState('add');
            } else {
                setIconState('edit');
            }

            if (!selectedFile) {
                if (previewUrl && previewUrl.startsWith('blob:')) {
                    URL.revokeObjectURL(previewUrl);
                }
                setPreviewUrl(null);
            }
        }
    }, [userImg]);

    useEffect(() => {
        return () => {
            if (previewUrl && previewUrl.startsWith('blob:')) {
                URL.revokeObjectURL(previewUrl);
            }
        };
    }, []);

    return (
        <div className={`${styles.fileInputContainer} ${settingsPage || simpleChange ? styles.userImg : ""}`}>
            {settingsPage || simpleChange ? (
                <div className={`${styles.filePreview} ${settingsPage || simpleChange ? styles.userImgModal : ""}`}>
                    {previewUrl ? (
                        <img src={previewUrl} alt={t('common.preview')}/>
                    ) : userImg ? (
                        <img src={userImg} alt={t('common.preview')}/>
                    ) : (
                        <img
                            src="/icons/placeholder-image.svg"
                            alt={t('common.default_user')}
                            style={{
                                height: "40px",
                                width: "40px",
                            }}
                        />
                    )}
                </div>
            ) : ''}

            {profilePage && (
                <div className={`${styles.profilePreview}`}>
                    {previewUrl ? (
                        <img src={previewUrl} alt={t('common.preview')}/>
                    ) : userImg && userImg !== '/icons/profile/avatar.png' ? (
                        <img src={userImg} alt={t('common.preview')}/>
                    ) : (
                        <img src="/icons/profile/avatar.png" alt={t('common.preview')}/>
                    )}

                    {!disabled &&
                        <label>
                            <img
                                src={getProfileImageIcon()}
                                alt={iconState === 'add' ? t('common.add_icon') : t('common.edit_icon')}
                            />
                            <input
                                ref={fileInputRef}
                                type="file"
                                onChange={handleFileChange}
                                accept="image/*"
                            />
                        </label>
                    }
                </div>
            )}

            {simpleChange &&
                <div className={`${homeStyles.askFormField} ${homeStyles.askFormFileInput}`}>
                    <div className={homeStyles.askFieldInput}>
                        <label style={{padding: 0, border: 0}}>
                            <input
                                ref={fileInputRef}
                                type="file"
                                onChange={handleFileChange}
                                accept="image/*"
                            />

                            <span>{t('common.change_image')}</span>
                        </label>
                    </div>
                </div>
            }

            {!profilePage && !simpleChange && (
                <div
                    className={`${homeStyles.askFormField} ${homeStyles.askFormFileInput} ${disabled ? homeStyles.askFileInputDisabled : ''}`}>
                    <div className={homeStyles.askFieldInput}>
                        <label style={{paddingRight: previewUrl ? '10px' : '25px'}}>
                            <input
                                ref={fileInputRef}
                                type="file"
                                onChange={handleFileChange}
                                accept="image/*"
                            />

                            <span>{settingsPage ? t('common.change_image') : t('common.choose_files')}</span>

                            {!settingsPage && previewUrl ? (
                                <>
                                    <div className={styles.filePreviewWrapper}>
                                        <img src={previewUrl} alt={t('common.preview')} className={styles.filePreviewImage}/>
                                        <img className={homeStyles.askFormPreviewRemove}
                                             onClick={(e) => handleRemove(e)} src="/icons/small-remove-icon.svg"
                                             alt={t('common.remove')}/>
                                    </div>
                                    <div className={styles.fileUploadedMsg}>{t('common.uploaded')}</div>
                                </>
                            ) : <div className={styles.fileMaxUploadSizeMsg}>{t('common.max_file_size_100mb')}</div>}
                        </label>
                    </div>
                </div>
            )}
        </div>
    );
};

export default FileInputWithPreview;