import React, {FC, useEffect, useRef, useState} from "react";
import {MenuItem, Select, SvgIcon} from "@mui/material";
import {Box} from "@mui/system";
import styles from "@/src/styles/widgets.module.css";
import SmallFillButton from "@/src/Components/Buttons/SmallFillButton";
import SmallBorderButton from "@/src/Components/Buttons/SmallBorderButton";
import {filterSelectStyle} from "@/src/Utils/styles";
import {useTranslation} from "@/src/Utils/i18n";

interface FiltersProps {
    ref?;
    onApplyFilters?: (e) => void;
    setFiltersApplied?: (val: boolean) => void;
}

const ArrowComponent = () => {
    return (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="chevron-down">
                <path id="Vector" d="M6 9L12 15L18 9" stroke="#3F3F3F" strokeWidth="2" strokeLinecap="round"
                      strokeLinejoin="round"/>
            </g>
        </svg>
    )
}

const Filters: FC<FiltersProps> = React.forwardRef(
    ({onApplyFilters, setFiltersApplied}, ref) => {
        const { t } = useTranslation();
        // Ref
        const menuRef: any = useRef();
        const defaultValues = {answered: "", userType: "", status: ""};
        console.log(ref , "ref in filters");

        // States
        const [open, setOpen] = useState(false);
        const [filterValues, setFilterValues] = useState(defaultValues);

        const resetFilters = () => {
            if (setFiltersApplied) {
                setFiltersApplied(false);
            }
            setFilterValues(defaultValues);
            setOpen(false);
            if (onApplyFilters) {
                onApplyFilters(defaultValues);
            }
        };


        const handleChangeFilter = (name, value) => {
            let tempData = {...filterValues};
            tempData[name] = value;
            setFilterValues(tempData);
        };

        const handleApplyFilters = () => {
            setOpen(false);
            if (onApplyFilters) {
                onApplyFilters(filterValues);
            }
        };

        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                // setOpen(false);
            }
        };

        useEffect(() => {
            document.addEventListener("click", handleClickOutside, true);
            return () => {
                document.removeEventListener("click", handleClickOutside, true);
            };
        });

        return (
            <div className={styles.filtersWrapper}>
                <div
                    className={open ? `${styles.filterButton} ${styles.filterButtonActive}` : styles.filterButton}
                    onClick={() => setOpen(!open)}
                >
                    <img src={"/icons/filter-icon.svg"} alt={"filter-icon"}/>
                    <span>Filter</span>
                </div>
                {open && (
                    <div className={styles.filterFieldsBox} ref={menuRef}>
                        <div className={styles.filterField}>
                            <Select
                                className={"selectBox"}
                                label=""
                                size="small"
                                value={filterValues.answered}
                                onChange={(e) => handleChangeFilter("answered", e.target.value)}
                                displayEmpty
                                renderValue={(value) => {
                                    return (
                                        <Box sx={{display: "flex", gap: 1}}>
                                            {value || t('common.questions_answered')}
                                        </Box>
                                    );
                                }}
                                sx={filterSelectStyle}
                                IconComponent={() => (
                                    <SvgIcon component={ArrowComponent}/>
                                )}
                            >
                                <MenuItem value="" style={{background: '#f9f9f9', color: '#777'}}>
                                    {t('common.questions_answered')}
                                </MenuItem>
                                <MenuItem value="answered">{t('student.status_answered')}</MenuItem>
                                <MenuItem value="unanswered">{t('student.status_pending')}</MenuItem>
                            </Select>
                        </div>

                        <div className={styles.filterField}>
                            <Select
                                label=""
                                size="small"
                                value={filterValues.userType}
                                onChange={(e) => handleChangeFilter("userType", e.target.value)}
                                displayEmpty
                                renderValue={(value) => {
                                    return (
                                        <Box sx={{display: "flex", gap: 1}}>
                                            {value || "User Type"}
                                        </Box>
                                    );
                                }}
                                sx={filterSelectStyle}
                                IconComponent={() => (
                                    <SvgIcon component={ArrowComponent}/>
                                )}
                            >
                                <MenuItem value="" style={{background: '#f9f9f9', color: '#777'}}>User Type</MenuItem>
                                <MenuItem value="Applicant">Applicant</MenuItem>
                                <MenuItem value="Student">Student</MenuItem>
                                <MenuItem value="Tutor">Tutor</MenuItem>
                            </Select>
                        </div>

                        <div className={styles.filterField}>
                            <Select
                                className={"selectBox"}
                                label=""
                                size="small"
                                value={filterValues.status}
                                onChange={(e) => handleChangeFilter("status", e.target.value)}
                                displayEmpty
                                renderValue={(value) => {
                                    return (
                                        <Box sx={{display: "flex", gap: 1}}>
                                            {value || "Account Status"}
                                        </Box>
                                    );
                                }}
                                sx={filterSelectStyle}
                                IconComponent={() => (
                                    <SvgIcon component={ArrowComponent}/>
                                )}
                            >
                                <MenuItem value="" style={{background: '#f9f9f9', color: '#777'}}>Account
                                    Status</MenuItem>
                                <MenuItem value="Active">Active</MenuItem>
                                <MenuItem value="Archived">Archived</MenuItem>
                            </Select>
                        </div>

                        <div className={styles.filterDivider}></div>
                        <div className={styles.actionFooter}>
                            <SmallBorderButton
                                className={styles.filterCancel}
                                label={t('common.cancel')}
                                onClick={resetFilters}
                            />
                            <SmallFillButton
                                label={t('common.apply_filter')}
                                onClick={handleApplyFilters}
                            />
                        </div>
                    </div>
                )}
            </div>
        );
    }
);

export default Filters;
