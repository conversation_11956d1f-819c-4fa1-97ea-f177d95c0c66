import React, {FC, useState} from "react";
import {TextField} from "@mui/material";
import styles from "@/src/styles/widgets.module.css";
import {defaultInputFieldStyle} from "@/src/Utils/styles";
import {useTranslation} from "@/src/Utils/i18n";

interface SearchBoxProps {
    onInputChange?: (e) => void;
    className?: string;
    placeholder?: string;
    style?: any;
}

const SearchBox: FC<SearchBoxProps> = ({
                                           onInputChange,
                                           className,
                                           style,
                                           placeholder = "Search questions.",
                                       }) => {
    const { t } = useTranslation();
    // States
    const [searchValue, setSearchValue] = useState("");

    const handleInputChange = (val) => {
        setSearchValue(val);
        if (onInputChange) {
            onInputChange(val);
        }
    };

    return (
        <div className={`${styles.searchWrapper} ${className}`} style={style}>
            <img
                className={styles.searchIcon}
                src={"/icons/search-icon.svg"}
                alt={"search-icon"}
            />
            <TextField
                fullWidth
                id="outlined-helperTexts"
                size={"medium"}
                placeholder={t('common.search_questions')}
                defaultValue=""
                helperText=""
                value={searchValue}
                onChange={(e) => handleInputChange(e.target.value)}
                sx={defaultInputFieldStyle}
                InputProps={{
                    style: {
                        height: "40px",
                        fontSize: "14px !important",
                        fontWeight: "500",
                        border: 0,
                        paddingLeft: "40px",
                        color: '#333'
                    },
                }}
            />
        </div>
    );
};

export default SearchBox;
