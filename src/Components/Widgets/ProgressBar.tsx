import styles from "@/src/styles/tutorProfile.module.css";
import {ProgressBarProps} from "@/src/Types";

export default function ProgressBar({value, max, showTooltip, tooltipText = ''}: ProgressBarProps) {
    const percentage = Math.min((value / max) * 100, 100);
    const isComplete = value && max && value === max;

    if (isComplete && !showTooltip) {
        return (
            <div className={styles.progressWrap}>
                <div className={styles.progressComplete}>Completed</div>
            </div>
        );
    }

    return (
        <div className={styles.progressWrap}>
            <div className={styles.progressBar}>
                <div
                    className={styles.progressBarFill}
                    style={{width: `${percentage}%`}}
                />
            </div>
            {showTooltip && (
                <div className={styles.progressTooltip}>
                    {tooltipText}
                    <div className={styles.progressTooltipArrow}/>
                </div>
            )}
        </div>
    );
}
