import React, {useEffect, useState} from "react";
import {useTranslation} from "@/src/Utils/i18n";
import styles from "@/src/styles/selectSubject.module.css";

type Subject = "Math" | "Science" | "Language Arts" | "Social Studies";

const SelectSubject: React.FC = () => {
    const {t} = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    const [selected, setSelected] = useState<Subject | null>(null);
    const dropdownRef = React.useRef() as React.MutableRefObject<HTMLDivElement>;

    const subjectList: { name: Subject; icon: string; translationKey: string }[] = [
        {name: "Math", icon: "/icons/home/<USER>", translationKey: "student.subject_math"},
        {name: "Science", icon: "/icons/home/<USER>", translationKey: "student.subject_science"},
        {name: "Language Arts", icon: "/icons/home/<USER>", translationKey: "student.subject_language_arts"},
        {name: "Social Studies", icon: "/icons/home/<USER>", translationKey: "student.subject_social_studies"},
    ];

    const handleSelect = (subject: Subject) => {
        setSelected(subject);
        setIsOpen(false);
    };

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    return (
        <div className={isOpen ? `${styles.dropdown} ${styles.dropdownOpen}` : styles.dropdown} ref={dropdownRef}>
            <div className={styles.selected} onClick={() => setIsOpen(!isOpen)}>
                {selected ? (
                    <>
                        <img
                            src={subjectList.find((s) => s.name === selected)?.icon}
                            alt={t(subjectList.find((s) => s.name === selected)?.translationKey || '')}
                            className={styles.icon}
                        />
                        <span>{t(subjectList.find((s) => s.name === selected)?.translationKey || '')}</span>
                    </>
                ) : (
                    <span className={styles.placeholder}>{t('student.subject')}</span>
                )}
                <span className={styles.arrow}><img src={'/icons/ask-form/select-down-arrow.svg'}
                                                    alt={'dropdown'}/></span>
            </div>
            {isOpen && (
                <ul className={styles.menu}>
                    {subjectList.map((subject) => (
                        <li
                            key={subject.name}
                            className={styles.item}
                            onClick={() => handleSelect(subject.name)}
                        >
                            <img src={subject.icon} alt={t(subject.translationKey)} className={styles.icon}/>
                            <span>{t(subject.translationKey)}</span>
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
};

export default SelectSubject;
