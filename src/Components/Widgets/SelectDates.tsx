import React, {FC, forwardRef, useRef, useState} from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import styles from "@/src/styles/widgets.module.css";
import {AppNotify} from "@/src/Utils/AppNotify";
import SmallFillButton from "@/src/Components/Buttons/SmallFillButton";
import CancelButton from "@/src/Components/Buttons/CancelButton";
import {useTranslation} from "@/src/Utils/i18n";

interface SelectDatesProps {
    className?: string;
    onApplyDateRange?: (e: { startDate: Date | null; endDate: Date | null }) => void;
    singleDateMode?: boolean;
}

const SelectDates: FC<SelectDatesProps> = ({onApplyDateRange, className, singleDateMode = false}) => {
    const { t } = useTranslation();
    const calRef = useRef<DatePicker>();

    // State
    const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
    const [singleDate, setSingleDate] = useState<Date | null>(null);
    const [startDate, endDate] = dateRange;

    const handleApply = () => {
        if (singleDateMode) {
            if (!singleDate) {
                AppNotify(t('common.please_select_valid_date'), "error");
                return;
            }
            onApplyDateRange?.({startDate: singleDate, endDate: null});
        } else {
            if (!endDate) {
                AppNotify(t('common.please_select_valid_date_range'), "error");
                return;
            }
            onApplyDateRange?.({startDate, endDate});
        }

        if (calRef.current) calRef.current.setOpen(false);
    };

    const CustomInput = forwardRef<HTMLDivElement, { value?: string; onClick?: () => void }>(
        ({value, onClick}, ref) => (
            <div onClick={onClick} ref={ref} className={styles.filterDateButton}>
                <img src={"/icons/calendar-blank-icon.svg"} alt="calendar-icon"/>
                {value || t('common.select_date')}
                <img style={{marginLeft: "auto"}} src={"/icons/arrow-line-down-icon.svg"} alt="ArrowLineDown-icon"/>
            </div>
        )
    );

    return (
        <div className={`${styles.datesWrapper} ${className}`}>
            <DatePicker
                popperClassName="range-datePicker"
                ref={calRef}
                selectsRange={!singleDateMode}
                selected={singleDateMode ? singleDate : undefined}
                onChange={(update: any) => {
                    if (singleDateMode) {
                        setSingleDate(update);
                    } else {
                        setDateRange(update);
                    }
                }}
                startDate={!singleDateMode ? startDate : undefined}
                endDate={!singleDateMode ? endDate : undefined}
                shouldCloseOnSelect={false}
                isClearable={false}
                customInput={<CustomInput/>}
                popperPlacement="top-end"
                dateFormat="dd MMM yyyy"
            >
                <div className={styles.pickerFooter}>
                    <CancelButton
                        label={t('common.cancel')}
                        onClick={() => {
                            if (singleDateMode) {
                                setSingleDate(null);
                                onApplyDateRange?.({startDate: null, endDate: null});
                            } else {
                                setDateRange([null, null]);
                                onApplyDateRange?.({startDate: null, endDate: null});
                            }
                            calRef.current?.setOpen(false);
                        }}
                    />
                    <SmallFillButton
                        onClick={handleApply}
                        label={t('common.apply')}
                        style={{height: "36px", fontSize: "14px"}}
                    />
                </div>
            </DatePicker>
        </div>
    );
};

export default SelectDates;
