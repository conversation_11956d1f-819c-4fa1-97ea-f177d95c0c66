import React, { useState } from 'react';
import styles from "@/src/styles/widgets.module.css";
import {TextToggleProps} from "@/src/Types";

const TextToggle: React.FC<TextToggleProps> = ({ text }) =>
{
    const [expanded, setExpanded] = useState(false);

    const renderContent = () => {
        if (Array.isArray(text)) {
            return text.map((p, index) => <p key={index}>{p}</p>);
        }
        return <p>{text}</p>;
    };

    return (
        <div>
            <div className={expanded ? styles.toggleTextBox: `${styles.toggleTextBox} ${styles.truncateTwoLines}`}> {renderContent()}</div>
            {text.length > 0 && (
                <button className={styles.toggleTextIcon} onClick={() => setExpanded(!expanded)}>
                    <img
                        className={expanded ? styles.toggleTextIconOpen : ''}
                        style={{maxWidth: '32px'}}
                        src="/icons/ques-toggle-icon.svg"
                        alt="Expand Icon"
                        onClick={() => setExpanded(!expanded)}
                    />
                </button>
            )}
        </div>
    );
};

export default TextToggle;
