"use client";
import React, {useEffect, useState} from "react";
import styles from "@/src/styles/addQuestion.module.css";
import {Checkbox} from "@mui/material";
import {addDocInCollection, getDocument, updateCollection,} from "@/src/Firebase/firebase.utils";
import {AppNotify} from "@/src/Utils/AppNotify";
import {zeroPadId} from "@/src/Utils/helpers";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import ConfirmModal from "@/src/Components/Modals/ConfirmModal";
import BorderButton from "@/src/Components/Buttons/BorderButton";
import { useTranslation } from "@/src/Utils/i18n";

const checkboxStyle = {
    padding: '8px 0 0 0',
    position: 'relative',
    left: '5px',
    color: '#9FACC4',
    '& .MuiSvgIcon-root': {
        width: 24,
        height: 24
    },
    '&.Mui-checked': {
        color: '#375dfb'
    },
    '&:hover': {
        color: '#375dfb',
    },
}

const AddQuestion = ({
                         setAddQuestionActive,
                         getQuestions,
                         edit,
                         currentQuestion,
                         totalQuestions,
                     }: {
    setAddQuestionActive: React.Dispatch<React.SetStateAction<boolean>>;
    getQuestions: () => void;
    edit: boolean;
    currentQuestion?: any;
    totalQuestions?: any;
}) => {
    const [open, setOpen] = useState(false);
    const [questionCounter, setQuestionCounter] = useState(0);
    const [question, setQuestion] = useState(
        edit ? currentQuestion.question || "" : ""
    );
    const [answer1, setAnswer1] = useState(
        edit ? currentQuestion.answer1 || "" : ""
    );
    const [answer2, setAnswer2] = useState(
        edit ? currentQuestion.answer2 || "" : ""
    );
    const [answer3, setAnswer3] = useState(
        edit ? currentQuestion.answer3 || "" : ""
    );
    const [answer4, setAnswer4] = useState(
        edit ? currentQuestion.answer4 || "" : ""
    );
    const [triggerError, setTriggerError] = useState(false);
    const [correctAnswer, setCorrectAnswer] = useState(
        edit ? currentQuestion.correctAnswer || "" : ""
    );
    const [adding, setAdding] = useState(false);
    const [sessionAddedCount, setSessionAddedCount] = useState(0);
    const [language, setLanguage] = useState(
        edit ? currentQuestion.language || "en" : "en"
    );
      const {t} = useTranslation();

    const getQuestionCounter = React.useCallback(async () => {
        const resp: any = await getDocument("applicant_question_counter", "counters");

        if (resp.status && resp.exists && resp.data) {
            if (typeof resp.data.count === 'number') {
                setQuestionCounter(resp.data.count);
            } else {
                setQuestionCounter(0);
                AppNotify(t('admin_add_question_notify.question_counter_invalid'), "error");
            }
        } else {
            setQuestionCounter(0);
        }
    }, [t]);

    useEffect(() => {
        getQuestionCounter();
        setSessionAddedCount(0);
    }, [getQuestionCounter]);

    useEffect(() => {
        if (correctAnswer !== "" && triggerError) {
            setTriggerError(false);
        }
    }, [correctAnswer, triggerError]);

    const handleOpen = () => {
        setOpen(true);
    };

    const handleClose = () => {
        setOpen(false);
    };

    const resetFields = () => {
        setQuestion("");
        setAnswer1("");
        setAnswer2("");
        setAnswer3("");
        setAnswer4("");
        setCorrectAnswer("");
    };

    const handleAdd = async () => {
        if (question === "") {
            AppNotify(t('admin_add_question_notify.question_required'), "error");
            return;
        }
        if (answer1 === "") {
            AppNotify(t('admin_add_question_notify.answer1_required'), "error");
            return;
        }
        if (answer2 === "") {
            AppNotify(t('admin_add_question_notify.answer2_required'), "error");
            return;
        }
        if (answer3 === "") {
            AppNotify(t('admin_add_question_notify.answer3_required'), "error");
            return;
        }
        if (answer4 === "") {
            AppNotify(t('admin_add_question_notify.answer4_required'), "error");
            return null;
        }
        if (correctAnswer === "") {
            setTriggerError(true);
            return null;
        }

        const newCountForGuid = questionCounter + 1;

        const data: any = {
            question,
            answer1,
            answer2,
            answer3,
            answer4,
            date: new Date().getTime().toString(),
            correctAnswer,
            answeredBy: [],
            language, // Add language to Firestore document
        };

        if (!edit) {
            data.guid = zeroPadId(newCountForGuid);
        } else if (currentQuestion && currentQuestion.guid) {
            data.guid = currentQuestion.guid;
        }

        setAdding(true);
        let resp: { status: boolean; id?: string };

        if (edit) {
            resp = await updateCollection(
                "applicant_questions",
                currentQuestion.docId,
                data
            );
        } else {
            resp = await addDocInCollection("applicant_questions", data);
            if (resp.status) {
                const counterUpdateResp = await updateCollection(
                    "counters",
                    "applicant_question_counter",
                    {
                        count: newCountForGuid,
                    },
                    true
                );
                if (counterUpdateResp.status) {
                    setQuestionCounter(newCountForGuid);
                } else {
                    AppNotify(t('admin_add_question_notify.failed_update_counter'), "error");
                }
            }
        }

        setAdding(false);
        if (resp.status) {
            if (edit) {
                getQuestions();
                setAddQuestionActive(false);
            } else {
                setSessionAddedCount(prev => prev + 1);
                resetFields();
                handleOpen();
            }
        }
    };

    let buttonDisable = edit ? false : !(question && answer1 && answer2 && answer3 && answer4 && correctAnswer);

    return (
        <div className={styles.addQuestion}>
            <div>
                {/* Language Dropdown */}
                <div>
                    <div className={styles.questionLabel}>{t("common.select_language")}</div>
                    <select
                        style={{width: '100%', height: '40px', padding: '0 10px', borderRadius: '4px', border: '1px solid #ccc',marginBottom: '10px',marginTop: '10px'}}
                        value={language}
                        onChange={e => setLanguage(e.target.value)}
                    >
                        <option value="en">English</option>
                        <option value="tr">Türkçe</option>
                        <option value="es">Español</option>
                    </select>
                </div>
                <div className={styles.questionBox}>
                    <div className={styles.questionLabel}>{t("admin_add_question.question_label")}</div>
                    <textarea
                        className={styles.questionInput}
                        rows={10}
                        value={question}
                        onChange={(e) => setQuestion(e.target.value)}
                    />
                </div>
                <div className={styles.answerBox}>
                    <div className={styles.answer}>
                        <Checkbox
                            size="medium"
                            onClick={() => setCorrectAnswer("answer1")}
                            checked={correctAnswer === "answer1"}
                            sx={checkboxStyle}
                        />
                        <div className={styles.answerVal}>
                            <div className={styles.answerLabel}>{t("admin_add_question.answer_1_label")}</div>
                            <textarea
                                value={typeof answer1 === 'string' ? answer1 : ''}
                                className={styles.answerInput}
                                rows={10}
                                onChange={(e) => setAnswer1(e.target.value)}
                            />
                        </div>
                    </div>
                    <div className={styles.answer}>
                        <Checkbox
                            size="medium"
                            onClick={() => setCorrectAnswer("answer2")}
                            checked={correctAnswer === "answer2"}
                            sx={checkboxStyle}
                        />
                        <div className={styles.answerVal}>
                            <div className={styles.answerLabel}>{t("admin_add_question.answer_2_label")}</div>
                            <textarea
                                value={typeof answer2 === 'string' ? answer2 : ''}
                                className={styles.answerInput}
                                rows={10}
                                onChange={(e) => setAnswer2(e.target.value)}
                            />
                        </div>
                    </div>
                    <div className={styles.answer}>
                        <Checkbox
                            size="medium"
                            onClick={() => setCorrectAnswer("answer3")}
                            checked={correctAnswer === "answer3"}
                            sx={checkboxStyle}
                        />
                        <div className={styles.answerVal}>
                            <div className={styles.answerLabel}>{t("admin_add_question.answer_3_label")}</div>
                            <textarea
                                value={typeof answer3 === 'string' ? answer3 : ''}
                                className={styles.answerInput}
                                rows={10}
                                onChange={(e) => setAnswer3(e.target.value)}
                            />
                        </div>
                    </div>
                    <div className={styles.answer}>
                        <Checkbox
                            size="medium"
                            onClick={() => setCorrectAnswer("answer4")}
                            checked={correctAnswer === "answer4"}
                            sx={checkboxStyle}
                        />
                        <div className={styles.answerVal}>
                            <div className={styles.answerLabel}>{t("admin_add_question.answer_4_label")}</div>
                            <textarea
                                value={typeof answer4 === 'string' ? answer4 : ''}
                                className={styles.answerInput}
                                rows={10}
                                onChange={(e) => setAnswer4(e.target.value)}
                            />
                        </div>
                    </div>
                </div>
                {triggerError && (
                    <div className={styles.errorNoAnswer}>
                        {t("admin_add_question.error_no_answer")}
                    </div>
                )}
                <div className={`${styles.btnContainer}`}>
                    <BorderButton
                        type={'dark'}
                        label={t("admin_add_question.cancel_button")}
                        onClick={() => {
                            setAddQuestionActive(false);
                        }}
                        style={{width: '120px'}}
                    />
                    <PrimaryButton
                        label={edit ? t("admin_add_question.save_question_button") : t("admin_add_question.add_question_button")}
                        size={'greenBtn'}
                        onClick={handleAdd}
                        disabled={buttonDisable}
                        loading={adding}
                        style={{width: '210px'}}
                    />
                </div>
            </div>

            <ConfirmModal
                open={open}
                title={t("admin_add_question.confirm_modal_title")}
                content={`There are now ${(totalQuestions || 0) + sessionAddedCount} questions in the bank.`}
                cancelLabel={t("admin_add_question.close_button")}
                actionLabel={t("admin_add_question.add_another_button")}
                handleClose={handleClose}
                onConfirm={handleClose}
                onCancel={() => {
                    getQuestions();
                    setAddQuestionActive(false);
                }}
                isLoading={adding}
            />
        </div>
    );
};

export default AddQuestion;