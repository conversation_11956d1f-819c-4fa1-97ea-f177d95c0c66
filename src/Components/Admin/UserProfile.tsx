"use client";
import React, {useCallback, useEffect, useState} from "react";
import {useTranslation} from "@/src/Utils/i18n";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import TablePagination from "@mui/material/TablePagination";
import {useRouter} from "next/navigation";
import {
    addDocInCollection,
    deleteDocument,
    deleteDocumentQuery,
    getAllDocs,
    getApplicantQuestionsFunc,
    getDocument,
    queryData,
    updateCollection,
} from "@/src/Firebase/firebase.utils";
import {formatDate, getComparator, stableSort, validateEmail, zeroPadId,} from "@/src/Utils/helpers";
import quesStyles from "@/src/styles/questionsBank.module.css";
import styles from "@/src/styles/users.module.css";
import Spinner from "@/src/Components/Widgets/Spinner";
import {Modal} from "@mui/material";
import {AppNotify} from "@/src/Utils/AppNotify";
import axios from "axios";
import ConfirmModal from "@/src/Components/Modals/ConfirmModal";
import {increment} from "firebase/firestore";
import EnhancedTableHead from "@/src/Components/Table/EnhancedTableHead";
import {Box} from "@mui/system";
import SearchBox from "@/src/Components/Widgets/SearchBox";
import modalStyles from "@/src/styles/modals.module.css";
import userStyles from "@/src/styles/user.module.css";
import SmallBorderButton from "@/src/Components/Buttons/SmallBorderButton";
import InputTextField from "@/src/Components/Form/InputTextField";
import MuiPhoneNumber from "mui-phone-number";
import {DatePicker} from "@mui/x-date-pickers";
import moment from "moment";
import TableCustomPagination from "@/src/Components/Table/TableCustomPagination";
import ButtonMenu from "@/src/Components/Buttons/ButtonMenu";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import QuantityStepper from "@/src/Components/Form/QuantityStepper";
import ViewQuestionModal from "@/src/Components/Modals/ViewQuestionModal";
import {defaultInputFieldStyle, tutorQuesStyle} from "@/src/Utils/styles";

interface Column {
    id: "question" | "date" | "id" | "answered" | "view";
    label: string;
    minWidth?: number;
    sorting?: boolean;
    align?: "right";
    format?: (value: number) => string;
}

interface Column3 {
    id: "date" | "id" | "correct" | "amount" | "view";
    label: string;
    minWidth?: number;
    sorting?: boolean;
    align?: "right";
    format?: (value: number) => string;
}

interface Data {
    question?: string;
    date: string;
    id?: string;
    answered?: string;
    view?: string;
}

interface Data3 {
    date: string;
    id: string;
    correct: string;
    amount: string;
    view: string;
}

function createData(question: string, date: string): Data {
    return {
        question,
        date,
    };
}

function createUserData(date: string, id: string, answered: string, view: string): Data {
    return {
        date,
        id,
        answered,
        view,
    };
}

function createData3(
    date: string,
    id: string,
    correct: string,
    amount: string,
    view: string
): Data3 {
    return {
        date,
        id,
        correct,
        amount,
        view,
    };
}

const UserProfile = ({
                         backToTitle = "Back to All Users",
                         selectedUserId = "",
                         handleGotoBack,
                         withdrawalPage,
                         isAdmin,
                     }: {
    backToTitle?: string;
    selectedUserId?: any;
    handleGotoBack?: () => void;
    withdrawalPage?: boolean;
    isAdmin?: boolean;
}) => {
    const {t} = useTranslation();
    const router = useRouter();

    const columns: Column[] = [
        {id: "question", label: t('admin.question'), minWidth: 120, sorting: true},
        {id: "date", label: t('common.date'), minWidth: 100, sorting: true},
    ];

    const userColumns: Column[] = [
        {id: "date", label: t('common.date'), minWidth: 120, sorting: true},
        {id: "id", label: t('admin.question_id'), minWidth: 100, sorting: true},
        {id: "answered", label: t('admin.answered'), minWidth: 100, sorting: true},
        {id: "view", label: t('admin.link'), minWidth: 50, sorting: false},
    ];

    const answersColumns: readonly Column3[] = [
        {id: "date", label: t('common.date'), minWidth: 120, sorting: true},
        {id: "id", label: t('admin.id'), minWidth: 120, sorting: true},
        {id: "amount", label: t('common.amount'), minWidth: 100, sorting: true},
        {id: "view", label: t('admin.link'), minWidth: 50, sorting: false}
    ];

    const testAnswersColumns: readonly Column3[] = [
        {id: "date", label: t('common.date'), minWidth: 120, sorting: true},
        {id: "id", label: t('admin.id'), minWidth: 120, sorting: true},
        {id: "correct", label: t('admin.correct'), minWidth: 100, sorting: true},
        {id: "amount", label: t('common.amount'), minWidth: 100, sorting: true},
        {id: "view", label: t('admin.link'), minWidth: 50, sorting: false}
    ];

    // States
    const [isProcessing, setProcess] = useState(false);
    const [isLoading, setLoading] = useState(true);
    const [isArchive, setArchive] = useState(false);
    const [isUpdating, setUpdate] = useState(false);
    const [newFirstName, setNewFirstName] = useState("");
    const [withdrawalStatus, setWithdrawalStatus] = useState("");
    const [newLastName, setNewLastName] = useState("");
    const [joinedDate, setJoinedDate] = useState("");
    const [startApproveApplicant, setStartApproveApplicant] = useState(false);
    const [questionsCredits, setQuestionsCredits] = useState();
    const [newEmail, setNewEmail] = useState("");
    const [newPhone, setNewPhone] = useState("");
    const [newTutorCertificationNumber, setNewTutorCertificationNumber] = useState("");
    const [newDob, setNewDob] = useState<any>(null);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [order, setOrder] = useState("desc");
    const [orderBy, setOrderBy] = useState("examDate");
    const [selectedQuestion, setSelectedQuestion] = useState({} as any);
    const [selectedUser, setSelectedUser] = useState({} as any);
    const [usersQuestions, setUsersQuestions] = useState([]);
    const [withdrawStart, setWithdrawStart] = useState(false);
    const [startDenyApplicant, setStartDenyApplicant] = useState(false);
    const [currentWithdrawalData, setCurrentWithdrawalData] = useState({});
    const [questionsFilter, setQuestionsFilter] = useState("");
    const [rows, setRows] = useState([] as any);
    const [creditAmount, setCreditAmount] = useState<any>("");
    const [deductAmount, setDeductAmount] = useState<any>("");
    const [startDelete, setStartDelete] = useState(false);
    // eslint-disable-next-line no-unused-vars
    const [applicantAnswers, setApplicantAnswers] = useState([]);
    const [questions, setQuestions] = useState([]);
    const [openArchiveUserModal, setOpenArchiveUserModal] = useState(false);
    const [openViewQuestionModal, setOpenViewQuestionModal] = useState(false);
    const [rows3, setRows3] = useState([] as any);
    const [rows4, setRows4] = useState([] as any);
    const [rows5, ] = useState([] as any);
    const [open, setOpen] = useState(false);
    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    // useCallback hooks must be defined before useEffect hooks that use them
    const getQuestions = useCallback(async () => {
        const resp: any = await getAllDocs("applicant_questions");
        if (resp.status && resp.fullData.length > 0) {
            const arr: any = [];
            resp.fullData.forEach((question: any) => {
                arr.push(question);
            });
            setQuestions(arr);
        }
    }, []);

    const getPendingWithdrawals = useCallback(async () => {
        const resp: any = queryData(
            "pending_withdrawals",
            "userId",
            selectedUserId
        );
        if (resp.status && resp.fullData.length > 0) {
            const rowData: any = [];
            resp.fullData.forEach((option: any) => {
                rowData.push({
                    ...option,
                    amount: "$" + option.amount.toFixed(2),
                    date: formatDate(Number(option.date)),
                });
            });
            setRows4(rowData);
        } else {
            setRows4([]);
        }
    }, [selectedUserId]);

    const getUserInfo = useCallback(async () => {
        setLoading(true);
        const resp: any = await getDocument(selectedUserId, "users");
        setLoading(false);

        if (resp.status && resp.exists) {
            const createdAt = resp.data && resp.data.createdAt ? resp.data.createdAt : "";
            const dateJoined = createdAt ? createdAt.seconds : ""

            setQuestionsCredits(resp.data.questions);
            setJoinedDate(dateJoined);
            setSelectedUser(resp.data);
            getUserQuestions(resp.data.type);
            getAnswers(resp.data);
        }
    }, [selectedUserId]);

    const approveApplicant = async () => {
        setProcess(true);
        const resp = await updateCollection(
            "users",
            selectedUserId,
            {
                isApplicant: false,
            },
            true
        );

        setProcess(false);
        if (resp.status) {
            AppNotify(t('admin_user_profile_notify.applicant_approved'), "success");
            setSelectedUser({...selectedUser, isApplicant: false});
            setStartApproveApplicant(false);
        }
        handleClose();
    };

    const denyApplicant = async () => {
        setProcess(true);
        const resp = await updateCollection(
            "users",
            selectedUserId,
            {
                applicantQuestionAnswered: 0, isApplicant: false
            },
            true
        );

        const resp2: any = await deleteDocumentQuery(
            "answeredBy",
            selectedUserId,
            "applicant_answers"
        );
        setLoading(false);
        if (resp.status && resp2.status) {
            AppNotify(t('admin_user_profile_notify.applicant_denied'), "success");
            handleGotoBack && handleGotoBack();
        }
    };

    const getUserQuestions = async (type: string) => {
        const resp: any = queryData("users_questions", "userId", selectedUserId);
        if (resp.status && resp.fullData.length > 0) {
            const arr: any = [];
            resp.fullData.forEach((question: any) => {
                let createdData;
                if (type === "user") {
                    createdData = createUserData(
                        formatDate(Number(question.date)),
                        question.id ? zeroPadId(question.id) : "",
                        question.isAnswered ? "Yes" : "No",
                        question.docId
                    );
                } else {
                    createdData = createData(
                        question.question,
                        formatDate(Number(question.date))
                    );
                }
                arr.push(createdData);
            });
            setRows(arr);
            setUsersQuestions(resp.fullData);
            setLoading(false);
        }
    };

    const getAnswers = async (user) => {
        const dbToUse = "tutor_answers";
        const resp: any = queryData(dbToUse, "answeredBy", selectedUserId);
        const arr: any = [];
        if (resp.status && resp.fullData.length > 0) {
            resp.fullData.forEach((answer: any) => {
                const createdData = createData3(
                    formatDate(Number(answer.date)),
                    answer && answer.guid ? answer.guid : '',
                    "Yes",
                    `$${answer?.earning || 3}`,
                    answer.docId
                );
                arr.push(createdData);
            });
        }

        setRows3([...arr, ...rows5]);
    };

    const getApplicantAnswers = useCallback(async () => {
        const collectionName = "applicant_answers";
        const resp: any = queryData(collectionName, "answeredBy", selectedUserId);
        let arr: any = [];

        if (resp.status && resp.fullData.length > 0 && questions && questions.length) {
            setApplicantAnswers(resp.fullData);

            resp.fullData.forEach((answer: any) => {
                let ques: any = questions.find((item: any) => item.docId === answer.questionId);

                const createdData = createData3(
                    formatDate(Number(answer.date)),
                    ques && ques.guid ? ques.guid : '',
                    answer.answer.includes(answer.correctAnswer) ? "Yes" : "No",
                    "$10",
                    answer.questionId
                );
                arr.push(createdData);
            });
        }

        if (selectedUser.applicantQuestionAnswered) {
            const applicantResp: any = await getApplicantQuestionsFunc(selectedUserId as string);
            if (applicantResp.status && applicantResp.fullData.length > 0) {
                let filterArray: any = [];
                // @ts-ignore
                const filteredQuestions = questions.filter(q => q.answeredBy?.includes(selectedUserId));

                filteredQuestions.forEach((answer: any) => {
                    const createdData = createData3(
                        formatDate(Number(answer.date)),
                        answer && answer.guid ? answer.guid : '',
                        "Yes",
                        "$3",
                        answer.docId
                    );
                    filterArray.push(createdData);
                });
            arr = [...arr, ...filterArray];
            }
        }
    }, [questions, selectedUser, selectedUserId]);

    // Move handler functions above their first usage
    const handleRequestSort = (event, property) => {
        const isAsc = orderBy === property && order === "asc";
        setOrder(isAsc ? "desc" : "asc");
        setOrderBy(property);
    };

    const handleChangePage = (event: unknown, newPage: number) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        setRowsPerPage(+event.target.value);
        setPage(0);
    };

    const handleAdminWithdraw = async () => {
        const data: any = currentWithdrawalData;
        setWithdrawStart(false);
        setLoading(true);
        try {
            const preparedResponse = await axios.post(
                process.env.NEXT_PUBLIC_CREATE_TRANSFER as string,
                data
            );

            if (preparedResponse.data.status) {
                const createTransferData = preparedResponse.data;
                const confirmTransferResponse = await axios.post(
                    process.env.NEXT_PUBLIC_CONFIRM_TRANSFER as string,
                    {
                        profileId: createTransferData.profileId,
                        quoteId: createTransferData.quoteId,
                        recipientAccountId: createTransferData.recipientAccountId,
                    }
                );

                if (confirmTransferResponse.data.status) {
                    AppNotify(t('admin_user_profile_notify.withdrawal_approved'), "success");
                    await deleteDocument("pending_withdrawals", data.docId, true);
                    await updateCollection(
                        "users",
                        data.userId,
                        {
                            pendingWithdrawals: increment(-1),
                            pendingWithdrawalAmount: increment(-data.amount),
                        },
                        true
                    );
                    getPendingWithdrawals();
                } else {
                    AppNotify(t('admin_user_profile_notify.withdrawal_approval_failed'), "error");
                }
            } else {
                AppNotify(t('admin_user_profile_notify.withdrawal_approval_failed'), "error");
            }
            setLoading(false);
        } catch (error) {
            AppNotify(t('admin_user_profile_notify.withdrawal_approval_failed'), "error");
            setLoading(false);
        }
    };

    const handleDenyWithdrawal = async () => {
        const data: any = currentWithdrawalData;
        setWithdrawStart(false);
        await deleteDocument("pending_withdrawals", data.docId, true);
        await updateCollection(
            "users",
            data.userId,
            {
                pendingWithdrawals: increment(-1),
                pendingWithdrawalAmount: increment(-data.amount),
            },
            true
        );
        getPendingWithdrawals();
    };

    useEffect(() => {
        getQuestions();
        getUserInfo();
        getPendingWithdrawals();
    }, [getQuestions, getUserInfo, getPendingWithdrawals]);

    useEffect(() => {
        if (selectedUserId) {
            getUserInfo();
        }
    }, [selectedUserId, getUserInfo]);

    useEffect(() => {
        if (questions && questions.length && selectedUser && selectedUser.type) {
            getApplicantAnswers();
        }
    }, [questions, selectedUser, getApplicantAnswers]);

    useEffect(() => {
        if (rows5 && rows5.length) {
            setRows3(r => [...r, ...rows5]);
        }
    }, [rows5]);

    const handleEnableEdit = () => {
        setNewFirstName(selectedUser.firstName);
        setNewLastName(selectedUser.lastName);
        setNewEmail(selectedUser.email);
        setNewPhone(selectedUser.phoneNumber);
        setNewTutorCertificationNumber(selectedUser.certificateNumber);
        setNewDob(moment(selectedUser.dateOfBirth) || null);
        handleOpen();
    };

    const userActions = {
        label: t('admin_user_profile.edit_profile'),
        children: [
            {
                label: t('admin_user_profile.edit_user_details'),
                icon: "/icons/pen-icon.svg",
                onClick: () => handleEditClick()
            },
            {
                label: selectedUser.is_archived ? t('admin_user_profile.unarchive_account') : t('admin_user_profile.archive_account'),
                icon: "/icons/paperclip-icon.svg",
                onClick: () => handleOpenArchiveModal()
            },
            {
                label: t('admin_user_profile.delete_account'),
                icon: "/icons/file-clear-icon.svg",
                onClick: () => handleOpenDeleteModal()
            },
        ],
    };

    const handleOpenDeleteModal = () => {
        setStartDelete(true);
    }

    const handleOpenArchiveModal = () => {
        setOpenArchiveUserModal(true);
    }


    const handleEditClick = () => {
        if (selectedUser.type === "tutor") {
            // Tutors - edit page
            router.push(`/admin/edit-user?id=${selectedUserId}`);
        } else {
            // edit modal
            handleEnableEdit();
        }
    };

    const deleteUser = async () => {
        setLoading(true);
        const resp = await deleteDocument("users", selectedUserId, true);
        setLoading(false);
        if (resp.status) {
            AppNotify(t('admin_user_profile_notify.user_deleted'), "success");
            handleGotoBack && handleGotoBack();
        }
    };

    const handleArchiveUser = async () => {
        setArchive(true);
        const resp = await updateCollection(
            "users",
            selectedUserId,
            {
                is_archived: true,
            },
            true
        );

        setArchive(false);
        if (resp.status) {
            AppNotify(t('admin_user_profile_notify.user_archived'), "success");
            setOpenArchiveUserModal(false);
            getUserInfo();
        }
    }

    const handleUnarchiveUser = async () => {
        setArchive(true);
        const resp = await updateCollection(
            "users",
            selectedUserId,
            {
                is_archived: false,
            },
            true
        );
        setArchive(false);
        if (resp.status) {
            AppNotify(t('admin_user_profile_notify.user_restored'), "success");
            setOpenArchiveUserModal(false);
            getUserInfo();
        }
    }

    const handleUpdateUserName = async () => {
        if (newFirstName === "") {
            AppNotify(t('admin.enter_your_first_name'), "error");
            return;
        }

        if (newLastName === "") {
            AppNotify(t('admin.enter_your_last_name'), "error");
            return;
        }

        if (newEmail === "") {
            AppNotify(t('admin.email_required'), "error");
            return;
        }

        if (newEmail && !validateEmail(newEmail)) {
            AppNotify(t('admin.invalid_email'), "error");
            return;
        }

        setUpdate(true);

        let revenue = selectedUser.revenue ? selectedUser.revenue : 0;
        let creditQuestions = selectedUser.questions ? selectedUser.questions : 0;
        const regex = /^[+-]?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)?$/;
        let creditAmountNum = regex.test(creditAmount) ? Number(creditAmount) : 0;
        let debitAmountNum = regex.test(deductAmount) ? Number(deductAmount) : 0;

        let apiData: any = {
            uid: selectedUserId,
            firstName: newFirstName,
            lastName: newLastName,
            email: newEmail,
            certificateNumber: newTutorCertificationNumber || 0,
            billingAddress: selectedUser.billingAddress || "",
            dateOfBirth: newDob.toDate().getTime() || "",
            phoneNumber: newPhone || "",
            country: selectedUser.country || "",
            address1: selectedUser.address1 || "",
            address2: selectedUser.address2 || "",
            city: selectedUser.city || "",
            state: selectedUser.state || "",
            postalCode: selectedUser.postalCode || "",
            profileImageUrl: selectedUser.profileImageUrl || "",
        };

        if (selectedUser.type === 'user') {
            if (creditAmountNum > 0) {
                creditQuestions += Number(creditAmount);
            }

            if (debitAmountNum > 0) {
                creditQuestions -= Number(deductAmount);
            }

            apiData.questions = creditQuestions;
        } else {
            if (creditAmountNum > 0) {
                revenue += Number(creditAmount);
            }

            if (debitAmountNum > 0) {
                revenue -= Number(deductAmount);
            }

            apiData.revenue = revenue;
        }

        if (creditAmountNum) {
            await addDocInCollection(
                "admin_question_credits",
                {
                    userId: selectedUserId,
                    amount: creditAmountNum,
                    operation: 'add',
                    detail: `Admin addition of ${creditAmountNum} question credits`,
                    date: new Date().getTime(),
                },
                true
            );
        }

        if (debitAmountNum) {
            await addDocInCollection(
                "admin_question_credits",
                {
                    userId: selectedUserId,
                    amount: debitAmountNum,
                    operation: 'remove',
                    detail: `Admin removal of ${debitAmountNum} question credits`,
                    date: new Date().getTime(),
                },
                true
            );
        }

        // const idToken = await getIdToken();
        // if (!idToken) {
        //     setUpdate(false);
        //     return;
        // }

        const resp = await axios.post(
            process.env.NEXT_PUBLIC_ADMIN_UPDATE_USER as string,
            apiData,
            // {headers: {Authorization: `Bearer ${idToken}`}}
        );

        if (resp.data && resp.data.status) {
            handleClose();
            getUserInfo();
        }

        setUpdate(false);
    };

    const labelDisplayedRows = ({from, to, count}) => {
        return `${from}–${to} of ${count !== -1 ? count : `${to}`} items`;
    };

    let userFirstName = newFirstName ? newFirstName : selectedUser && selectedUser.firstName ? selectedUser.firstName : "";
    let userLastName = newLastName ? newLastName : selectedUser && selectedUser.lastName ? selectedUser.lastName : "";
    let userFullName = `${userFirstName} ${userLastName}`;
    const columnToUse = selectedUser.type === "user" ? userColumns : columns;

    const questionsArrToUse = usersQuestions ? usersQuestions : [];
    if (questionsFilter && questionsFilter !== "") {
        questionsArrToUse.filter((question: any) => {
            return question.id.includes(questionsFilter);
        });
    }

    const handleGotoQuestion = (id) => {
        if (selectedUser.isApplicant) {
            let ques: any = applicantAnswers.find((item: any) => item.questionId === id);
            if (ques) {
                setSelectedQuestion(ques);
                setOpenViewQuestionModal(true);
            }
        } else {
            router.push(`/admin/user-questions?id=${id}`);
        }
    }

    const isAdminProfile = !!(selectedUser && selectedUser.type && selectedUser.type === "admin");

    console.log('selectedUser', selectedUser)

    return (
        <div className={quesStyles.questionsWrapper} style={{padding: '0 15px'}}>
            <div className={styles.userProfileBox}>
                <div className={styles.userDetails}>
                    <div>
                        {isAdminProfile ? (
                            <div className={styles.adminLabel}>Admin</div>
                        ) : (
                            ""
                        )}
                        {selectedUser && userFullName ? (
                            <div className={styles.userName}>{userFullName}</div>
                        ) : (
                            ""
                        )}
                    </div>
                    <div className={styles.userActionBox} style={{width: 'auto'}}>
                        {selectedUser.isApplicant && (
                            <div className={styles.approvalBtns}>
                                <SmallBorderButton
                                    className={modalStyles.modalCancelButton}
                                    label={t('admin_user_profile.approve')}
                                    onClick={() => {
                                        setStartApproveApplicant(true);
                                    }}
                                />
                                <SmallBorderButton
                                    className={modalStyles.modalCancelButton}
                                    label={t('admin_user_profile.deny')}
                                    onClick={() => {
                                        setStartApproveApplicant(false);
                                        setStartDenyApplicant(true);
                                    }}
                                />
                            </div>
                        )}

                        {isAdmin ?
                            <div className={styles.editAccount}><ButtonMenu zeroBottom={true} item={userActions}/>
                            </div> : <div onClick={handleEditClick} className={styles.editAccount}>{t('admin_user_profile.edit_profile')}</div>}
                    </div>
                </div>
                <div className={styles.divider}></div>
                <div className={styles.userInfo}>
                    {selectedUser && (
                        <>
                            {selectedUser.email && (
                                <div className={styles.userInfoField}>
                                    <div className={styles.userFieldHeading}>{t('admin_user_profile.email_address')}</div>
                                    <div className={styles.userFieldContent}>
                                        {selectedUser.email}
                                    </div>
                                </div>
                            )}
                            {selectedUser.type === "tutor" && (
                                <>
                                    <div className={styles.userInfoField}>
                                        <div className={styles.userFieldHeading}>{t('admin_user_profile.total_earnings')}</div>
                                        <div className={styles.userFieldContent}>${selectedUser.earnings || 0}</div>
                                    </div>
                                </>
                            )}

                            <div className={styles.userInfoField}>
                                <div className={styles.userFieldHeading}>{t('admin_user_profile.joined_date')}</div>
                                <div
                                    className={styles.userFieldContent}>{moment.unix(Number(joinedDate) || 0).format("DD MMM, YYYY")}</div>
                            </div>

                            {selectedUser.type === "tutor" && (
                                <>
                                    <div className={styles.userInfoField}>
                                        <div className={styles.userFieldHeading}>{t('admin_user_profile.total_answers')}</div>
                                        <div className={styles.userFieldContent}>
                                            {(selectedUser.isApplicant ? selectedUser.applicantQuestionAnswered : selectedUser.answers) || 0}
                                        </div>
                                    </div>
                                </>
                            )}

                            {selectedUser.type === "user" && (
                                <>
                                    {selectedUser.email && (
                                        <div className={styles.userInfoField}>
                                            <div className={styles.userFieldHeading}>
                                                {t('admin_user_profile.question_credits')}
                                            </div>
                                            <div className={styles.userFieldContent}>
                                                {questionsCredits}
                                            </div>
                                        </div>
                                    )}
                                    {selectedUser.email && (
                                        <div className={styles.userInfoField}>
                                            <div className={styles.userFieldHeading}>
                                                {t('admin_user_profile.total_questions')}
                                            </div>
                                            <div className={styles.userFieldContent}>
                                                {usersQuestions.length}
                                            </div>
                                        </div>
                                    )}
                                </>
                            )}
                        </>
                    )}
                </div>
            </div>
            {rows4.length > 0 ?
                <div className={styles.payoutRequestContainer}>
                    {rows4.length > 0 &&
                        rows4.map((row: any) => {
                            return (
                                <div className={styles.payoutBox} key={row.docId}>
                                    <div className={styles.userInfo} style={{width: "100%"}}>
                                        <div
                                            className={styles.userFieldHeading}
                                            style={{fontSize: "16px"}}
                                        >
                                            {t('admin_user_profile.payout_request')}
                                        </div>
                                        <div className={styles.userField}>
                                            <div className={styles.userFieldHeading}>
                                                {t('admin_user_profile.date_of_request')}
                                            </div>
                                            <div className={styles.userFieldContent}>{row.date}</div>
                                        </div>
                                        <div className={styles.userField}>
                                            <div className={styles.userFieldHeading}>
                                                {t('admin_user_profile.amount_requested')}
                                            </div>
                                            <div className={styles.userFieldContent}>{row.amount}</div>
                                        </div>
                                        <div className={styles.approvalBtns}>
                                            <SmallBorderButton
                                                className={modalStyles.modalCancelButton}
                                                label={t('admin_user_profile.approve')}
                                                onClick={() => {
                                                    setCurrentWithdrawalData(row);
                                                    setWithdrawalStatus("approve");
                                                    setWithdrawStart(true);
                                                }}
                                            />
                                            <SmallBorderButton
                                                className={modalStyles.modalCancelButton}
                                                label={t('admin_user_profile.deny')}
                                                onClick={() => {
                                                    setCurrentWithdrawalData(row);
                                                    setWithdrawalStatus("deny");
                                                    setWithdrawStart(true);
                                                }}
                                            />
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                </div> : ''}
            {isLoading ? (
                <Spinner/>
            ) : (
                <div className={"tableWrapper"}>
                    {selectedUser.type !== "user" && !selectedUser.isApplicant && !isAdminProfile && (
                        <>
                            <h3 className={styles.allQuesTitle}>{selectedUser.isApplicant ? t('admin_user_profile.answers') : t('admin_user_profile.all_questions')}</h3>
                            <SearchBox
                                placeholder={t('admin_user_profile.search_by_question_id')}
                                style={{maxWidth: '388px'}}
                            />
                            <div
                                style={{
                                    marginTop: "10px",
                                    width: "100%",
                                }}
                            >
                                <TableContainer className={"tableContainer"}>
                                    <Table>
                                        <EnhancedTableHead
                                            columns={answersColumns as Column3[]}
                                            order={order}
                                            orderBy={orderBy}
                                            onRequestSort={handleRequestSort}
                                        />
                                        <TableBody>
                                            {rows3 && rows3.length ? (
                                                stableSort(rows3, getComparator(order, orderBy))
                                                    .slice(
                                                        page * rowsPerPage,
                                                        page * rowsPerPage + rowsPerPage
                                                    )
                                                    .map((row, index) => {
                                                        return (
                                                            <TableRow
                                                                hover
                                                                sx={{
                                                                    overflowY: "scroll",
                                                                }}
                                                                role="checkbox"
                                                                tabIndex={-1}
                                                                key={`rows3-${index}`}
                                                            >
                                                                {answersColumns.map((column) => {
                                                                    const value = row[column.id];
                                                                    return (
                                                                        <TableCell
                                                                            key={column.id}
                                                                            align={column.align}
                                                                        >
                                                                            {column.id === 'view' ? <a style={{
                                                                                cursor: 'pointer',
                                                                                color: 'rgba(51, 92, 255, 1)'
                                                                            }}
                                                                                                       onClick={() => handleGotoQuestion(value)}>View</a> : value}
                                                                        </TableCell>
                                                                    );
                                                                })}
                                                            </TableRow>
                                                        );
                                                    })
                                            ) : (
                                                <TableRow className="tableNoDataFound">
                                                    <TableCell colSpan={5}>
                                                        {t('admin_user_profile.the_user_has_not_yet_answered_a_question')}
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                        </TableBody>
                                    </Table>
                                </TableContainer>

                                <TableCustomPagination
                                    filteredRowsLength={rows3.length}
                                    page={page}
                                    rowsPerPage={rowsPerPage}
                                    setPage={setPage}
                                    setRowsPerPage={setRowsPerPage}
                                />
                            </div>
                        </>
                    )}

                    {selectedUser && selectedUser.isApplicant ?
                        <div
                            style={{
                                marginTop: "10px",
                                width: "100%",
                            }}
                        >
                            <h3 className={styles.allQuesTitle}>
                                {t('admin_user_profile.test_question_answers')}
                            </h3>

                            <TableContainer className={"tableContainer"}>
                                <Table>
                                    <EnhancedTableHead
                                        columns={testAnswersColumns as Column3[]}
                                        order={order}
                                        orderBy={orderBy}
                                        onRequestSort={handleRequestSort}
                                    />
                                    <TableBody>
                                        {rows5 && rows5.length ? (
                                            stableSort(rows5, getComparator(order, orderBy))
                                                .slice(
                                                    page * rowsPerPage,
                                                    page * rowsPerPage + rowsPerPage
                                                )
                                                .map((row, index) => {
                                                    return (
                                                        <TableRow
                                                            hover
                                                            sx={{
                                                                overflowY: "scroll",
                                                            }}
                                                            role="checkbox"
                                                            tabIndex={-1}
                                                            key={`rows5-${index}`}
                                                        >
                                                            {testAnswersColumns.map((column) => {
                                                                const value = row[column.id];
                                                                return (
                                                                    <TableCell
                                                                        key={column.id}
                                                                        align={column.align}
                                                                    >
                                                                        {column.id === 'view' ? <a style={{
                                                                            cursor: 'pointer',
                                                                            color: 'rgba(51, 92, 255, 1)'
                                                                        }}
                                                                                                   onClick={() => handleGotoQuestion(value)}>View</a> : value}
                                                                    </TableCell>
                                                                );
                                                            })}
                                                        </TableRow>
                                                    );
                                                })
                                        ) : (
                                            <TableRow className="tableNoDataFound">
                                                <TableCell colSpan={5}>
                                                    {t('admin_user_profile.the_user_has_not_yet_answered_a_question')}
                                                </TableCell>
                                            </TableRow>
                                        )}
                                    </TableBody>
                                </Table>
                            </TableContainer>

                            <TableCustomPagination
                                filteredRowsLength={rows3.length}
                                page={page}
                                rowsPerPage={rowsPerPage}
                                setPage={setPage}
                                setRowsPerPage={setRowsPerPage}
                            />
                        </div> : ''}

                    {selectedUser.type === "user" && (
                        <>
                            <h3>{t('admin_user_profile.all_questions')}</h3>
                            <SearchBox
                                placeholder={t('admin_user_profile.search_by_question_id')}
                                onInputChange={(value) => {
                                    setQuestionsFilter(value);
                                }}
                            />
                            <div style={{marginTop: "20px", width: "100%"}}>
                                <TableContainer className={"tableContainer"}>
                                    <Table>
                                        <EnhancedTableHead
                                            columns={columnToUse}
                                            order={order}
                                            orderBy={orderBy}
                                            onRequestSort={handleRequestSort}
                                        />
                                        <TableBody>
                                            {questionsArrToUse && questionsArrToUse.length ? (
                                                stableSort(rows, getComparator(order, orderBy))
                                                    .slice(
                                                        page * rowsPerPage,
                                                        page * rowsPerPage + rowsPerPage
                                                    )
                                                    .map((row, index) => {
                                                        return (
                                                            <TableRow
                                                                hover
                                                                sx={{
                                                                    overflowY: "scroll",
                                                                }}
                                                                role="checkbox"
                                                                tabIndex={-1}
                                                                key={`ques-${index}`}
                                                            >
                                                                {columnToUse.map((column) => {
                                                                    const value = row[column.id];
                                                                    return (
                                                                        <TableCell
                                                                            key={column.id}
                                                                            align={column.align}
                                                                            style={{maxWidth: "100px"}}
                                                                        >
                                                                            {column.id === 'view' ? <a style={{
                                                                                cursor: 'pointer',
                                                                                color: 'rgba(51, 92, 255, 1)'
                                                                            }}
                                                                                                       onClick={() => handleGotoQuestion(value)}>View</a> : value}
                                                                        </TableCell>
                                                                    );
                                                                })}
                                                            </TableRow>
                                                        );
                                                    })
                                            ) : (
                                                <div className={quesStyles.searchLoader}>
                                                    {t('admin_user_profile.the_user_has_not_yet_asked_a_question')}
                                                </div>
                                            )}
                                        </TableBody>
                                    </Table>
                                </TableContainer>
                                <TablePagination
                                    rowsPerPageOptions={[10, 20, 30]}
                                    component="div"
                                    count={rows.length}
                                    rowsPerPage={rowsPerPage}
                                    page={page}
                                    onPageChange={handleChangePage}
                                    onRowsPerPageChange={handleChangeRowsPerPage}
                                    labelRowsPerPage={t('admin_user_profile.showing') || "Showing"}
                                    labelDisplayedRows={labelDisplayedRows}
                                />
                            </div>
                        </>
                    )}
                </div>
            )}
            <ConfirmModal
                open={withdrawStart}
                title={withdrawalStatus === "approve" ? t('admin_user_profile.approve_payout_request') : t('admin_user_profile.deny_payout_request')}
                content={t('admin_user_profile.irreversible_action')}
                actionLabel={withdrawalStatus === "approve" ? t('admin_user_profile.approve') : t('admin_user_profile.deny')}
                handleClose={() => setWithdrawStart(false)}
                onConfirm={withdrawalStatus === "approve" ? handleAdminWithdraw : handleDenyWithdrawal}
            />
            <ConfirmModal
                open={startApproveApplicant || startDenyApplicant}
                title={startApproveApplicant ? t('admin_user_profile.approve_applicant') : t('admin_user_profile.deny_applicant')}
                content={t('admin_user_profile.irreversible_action')}
                actionLabel={startApproveApplicant ? t('admin_user_profile.approve') : t('admin_user_profile.deny')}
                handleClose={() => {
                    setStartApproveApplicant(false);
                    setStartDenyApplicant(false);
                }}
                onConfirm={startApproveApplicant ? approveApplicant : denyApplicant}
                isLoading={isProcessing}
            />

            <ConfirmModal
                open={startDelete}
                title={t('admin_user_profile.confirm_delete')}
                content={t('admin_user_profile.delete_user')}
                actionLabel={t('admin_user_profile.approve')}
                handleClose={() => {
                    setStartDelete(false);
                }}
                onConfirm={deleteUser}
                isDeleteConfirm={true}
            />

            <ConfirmModal
                open={openArchiveUserModal}
                title={selectedUser.is_archived ? t('admin_user_profile.confirm_unarchive') : t('admin_user_profile.confirm_archive')}
                content={selectedUser.is_archived ? t('admin_user_profile.restore_user') : t('admin_user_profile.archive_user')}
                actionLabel={t('admin_user_profile.approve')}
                handleClose={() => {
                    setOpenArchiveUserModal(false);
                }}
                onConfirm={selectedUser.is_archived ? handleUnarchiveUser : handleArchiveUser}
                isDeleteConfirm={true}
                isLoading={isArchive}
            />

            <Modal
                open={open}
                onClose={handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
                disableScrollLock
            >
                <Box sx={{...tutorQuesStyle, padding: '24px', maxWidth: "650px"}}>
                    <div>
                        {isUpdating ? (
                            <Spinner/>
                        ) : (
                            <>
                                <div className={userStyles.personalForm}>
                                    <h3>{t('admin_user_profile.update_user_details')}</h3>
                                    <div className={userStyles.twoColsFields}>
                                        <div className={userStyles.field}>
                                            <div className={userStyles.label}>
                                                {t('admin_user_profile.first_name')}
                                            </div>
                                            <InputTextField
                                                label=""
                                                placeholder={""}
                                                value={newFirstName}
                                                onChange={(e) => setNewFirstName(e.target.value)}
                                            />
                                        </div>

                                        <div className={userStyles.field}>
                                            <div className={userStyles.label}>
                                                {t('admin_user_profile.last_name')}
                                            </div>
                                            <InputTextField
                                                label=""
                                                placeholder={""}
                                                value={newLastName}
                                                onChange={(e) => setNewLastName(e.target.value)}
                                            />
                                        </div>
                                    </div>
                                    <div className={userStyles.twoColsFields}>
                                        <div className={userStyles.field}>
                                            <div className={userStyles.label}>{t('admin_user_profile.email_address')}</div>
                                            <InputTextField
                                                label=""
                                                placeholder={""}
                                                value={newEmail}
                                                onChange={(e) => setNewEmail(e.target.value)}
                                            />
                                        </div>
                                        <div className={userStyles.field}>
                                            <div className={userStyles.label}>{t('admin_user_profile.phone_number')}</div>
                                            <MuiPhoneNumber
                                                className="flag-dropdown"
                                                defaultCountry={"us"}
                                                value={newPhone}
                                                onChange={(val) => {
                                                    setNewPhone(val);
                                                }}
                                                sx={defaultInputFieldStyle}
                                                variant="outlined"
                                            />
                                        </div>
                                    </div>
                                    {selectedUser.type === "tutor" && (
                                        <div className={userStyles.twoColsFields}>
                                            <div className={userStyles.field}>
                                                <div className={userStyles.label}>
                                                    {t('admin_user_profile.tutor_certification_number')}
                                                </div>
                                                <InputTextField
                                                    label=""
                                                    placeholder={""}
                                                    value={newTutorCertificationNumber}
                                                    onChange={(e) =>
                                                        setNewTutorCertificationNumber(e.target.value)
                                                    }
                                                />
                                            </div>
                                            <div className={userStyles.field}>
                                                <div className={userStyles.label}>{t('admin_user_profile.birthday')}</div>
                                                <DatePicker
                                                    sx={defaultInputFieldStyle}
                                                    // variant="outlined"
                                                    value={newDob}
                                                    onChange={(newValue) => setNewDob(newValue)}
                                                />
                                            </div>
                                        </div>
                                    )}
                                    {!isAdminProfile ? <>{selectedUser.type === 'user' ? <h3>{t('admin_user_profile.question_credits')}: {selectedUser.questions ? selectedUser.questions : '0'}</h3> :
                                        <h3>{t('admin_user_profile.current_balance')}: ${selectedUser.revenue ? selectedUser.revenue : '0.00'}</h3>}</> : ''}

                                    {!isAdminProfile ?
                                        <div className={userStyles.twoColsFields}>
                                            <div className={userStyles.field}>
                                                <div className={userStyles.label}>{t('admin_user_profile.credit_amount')}</div>
                                                <QuantityStepper
                                                    min={0}
                                                    max={10000}
                                                    onChange={setCreditAmount}
                                                    value={creditAmount}
                                                />
                                            </div>

                                            <div className={userStyles.field}>
                                                <div className={userStyles.label}>{t('admin_user_profile.deduct_amount')}</div>
                                                <QuantityStepper
                                                    min={0}
                                                    max={10000}
                                                    onChange={setDeductAmount}
                                                    value={deductAmount}
                                                />
                                            </div>
                                        </div> : ''
                                    }

                                    <div className={styles.btnContainer}>
                                        {isAdmin ?
                                            <div className={styles.leftButtons}>
                                                <SmallBorderButton
                                                    className={modalStyles.modalCancelButton}
                                                    label={selectedUser.is_archived ? t('admin_user_profile.unarchive') : t('admin_user_profile.archive')}
                                                    onClick={() => {
                                                        setOpenArchiveUserModal(true);
                                                    }}
                                                />
                                                <SmallBorderButton
                                                    className={modalStyles.modalCancelButton}
                                                    label={t('admin_user_profile.delete')}
                                                    onClick={() => {
                                                        setStartDelete(true);
                                                    }}
                                                />
                                            </div> : ''
                                        }
                                        <div>
                                            <PrimaryButton
                                                onClick={handleUpdateUserName}
                                                label={t('admin_user_profile.update')}
                                                size={'medium'}
                                                style={{height: '38px', width: '80px'}}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </>
                        )}
                    </div>
                </Box>
            </Modal>

            <ViewQuestionModal
                open={openViewQuestionModal}
                title={t('admin_user_profile.view')}
                selectedQuestion={selectedQuestion}
                handleClose={() => {
                    setOpenViewQuestionModal(false);
                    setSelectedQuestion({})
                }}
            />
        </div>
    );
};

export default UserProfile;