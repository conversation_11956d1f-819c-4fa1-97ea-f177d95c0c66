/* eslint-disable */
// @ts-nocheck
"use client";
import React, { useEffect, useState, useCallback } from "react";
import { Paper } from "@mui/material";
import axios from "axios";
import moment from "moment";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "mui-phone-number";
import { DatePicker } from "@mui/x-date-pickers";
import styles from "@/src/styles/users.module.css";
import quesStyles from "@/src/styles/questionsBank.module.css";
import userStyles from "@/src/styles/user.module.css";
import Spinner from "@/src/Components/Widgets/Spinner";
import InputTextField from "@/src/Components/Form/InputTextField";
import QuantityStepper from "@/src/Components/Form/QuantityStepper";
import ConfirmModal from "@/src/Components/Modals/ConfirmModal";
import {
  addDocInCollection,
  deleteDocument,
  getAllDocs,
  getDocument,
  queryData,
  updateCollection,
  uploadImage,
} from "@/src/Firebase/firebase.utils";
import {
  formatDate,
  getComparator,
  stableSort,
  validateEmail,
} from "@/src/Utils/helpers";
import { defaultInputFieldStyle } from "@/src/Utils/styles";
import { AppNotify } from "@/src/Utils/AppNotify";
import { useTranslation } from "@/src/Utils/i18n";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableRow from "@mui/material/TableRow";
import EnhancedTableHead from "@/src/Components/Table/EnhancedTableHead";
import TableCustomPagination from "@/src/Components/Table/TableCustomPagination";
import BorderButton from "@/src/Components/Buttons/BorderButton";
import FileInputWithPreview from "@/src/Components/Widgets/FileInputWithPreview";
import CustomSelectField from "@/src/Components/Form/CustomSelectField";
import { getAuth } from "firebase/auth";
import { useRouter } from "next/navigation";
import { useContext } from "react";
import { UserContext } from "@/src/Contexts/UserContext";
import TopBarWithLogo from "@/src/Components/Widgets/TopBarWithLogo";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import CustomCheckbox from "@/src/Components/Form/CustomCheckbox";
import { getDownloadURL, getStorage, ref, uploadBytes } from "firebase/storage";
import {
  getFirestore,
  doc,
  updateDoc as updateFirestoreDoc,
} from "firebase/firestore";
import { getApp } from "firebase/app";
import { getFunctions, httpsCallable } from "firebase/functions";

interface Column3 {
  id: "date" | "id" | "correct" | "amount" | "view";
  label: string;
  minWidth?: number;
  sorting?: boolean;
  align?: "right";
  format?: (value: number) => string;
}
const { t } = useTranslation();
const testAnswersColumns: readonly Column3[] = [
  {
    id: "date",
    label: t("admin_edit_user_profile.date_column"),
    minWidth: 120,
    sorting: true,
  },
  {
    id: "id",
    label: t("admin_edit_user_profile.id_column"),
    minWidth: 120,
    sorting: true,
  },
  {
    id: "correct",
    label: t("admin_edit_user_profile.correct_column"),
    minWidth: 100,
    sorting: true,
  },
  {
    id: "amount",
    label: t("admin_edit_user_profile.amount_column"),
    minWidth: 100,
    sorting: true,
  },
  {
    id: "view",
    label: t("admin_edit_user_profile.link_column"),
    minWidth: 50,
    sorting: false,
  },
];

const statsFields = [
  {
    key: "acceptanceRate",
    label: t("admin_edit_user_profile.acceptance_rate_label"),
    placeholder: t("admin_edit_user_profile.acceptance_rate_placeholder"),
    type: "number",
  },
  {
    key: "studentsHelped",
    label: t("admin_edit_user_profile.students_helped_label"),
    placeholder: t("admin_edit_user_profile.students_helped_placeholder"),
    type: "number",
  },
  {
    key: "topSubject",
    label: t("admin_edit_user_profile.acceptance_rate_label"),
    placeholder: t("admin_edit_user_profile.top_subject_placeholder"),
    type: "dropdown",
    options: [
      { label: t("admin_edit_user_profile.maths_option"), value: "Maths" },
      { label: t("admin_edit_user_profile.reading_option"), value: "Reading" },
      { label: t("admin_edit_user_profile.writing_option"), value: "Writing" },
      { label: t("admin_edit_user_profile.science_option"), value: "Science" },
    ],
  },
  {
    key: "dailyStreak",
    label: t("admin_edit_user_profile.daily_streak_label"),
    placeholder: t("admin_edit_user_profile.daily_streak_placeholder"),
    type: "number",
  },
  {
    key: "monthlyRank",
    label: t("admin_edit_user_profile.monthly_rank_label"),
    placeholder: t("admin_edit_user_profile.monthly_rank_placeholder"),
    type: "dropdown",
    options: [
      {
        label: t("admin_edit_user_profile.answer_apprentice_option"),
        value: "1",
      },
      { label: t("admin_edit_user_profile.homework_hero_option"), value: "2" },
      {
        label: t("admin_edit_user_profile.knowledge_knight_option"),
        value: "3",
      },
      { label: t("admin_edit_user_profile.wisdom_wizard_option"), value: "4" },
    ],
  },
  {
    key: "monthlyAnswers",
    label: t("admin_edit_user_profile.monthly_answers_label"),
    placeholder: t("admin_edit_user_profile.monthly_answers_placeholder"),
    type: "number",
  },
];

type FormDataType = {
  [key: string]: string;
};

interface StatisticsFormProps {
  selectedUser: any;
  selectedUserId: string;
}

const StatisticsForm = ({
  selectedUser,
  selectedUserId,
}: StatisticsFormProps) => {
  const [formData, setFormData] = useState<FormDataType>(() =>
    statsFields.reduce(
      (acc, field) => ({
        ...acc,
        [field.key]: selectedUser?.statistics?.[field.key] || "",
      }),
      {}
    )
  );
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setFormData(
      statsFields.reduce(
        (acc, field) => ({
          ...acc,
          [field.key]: selectedUser?.statistics?.[field.key] || "",
        }),
        {}
      )
    );
  }, [selectedUser, selectedUser?.statistics]);

  const handleChange = (key: string, value: string) => {
    const field = statsFields.find((f) => f.key === key);
    if (field?.type === "number") {
      const numericValue = value.replace(/[^0-9.]/g, "");
      setFormData((prev) => ({ ...prev, [key]: numericValue }));
    } else {
      setFormData((prev) => ({ ...prev, [key]: value }));
    }
  };

  const handleUpdateStatistics = async () => {
    setLoading(true);
    const idToken = await getIdToken();
    if (!idToken) {
      setLoading(false);
      return;
    }

    try {
      const payload = {
        uid: selectedUserId,
        statistics: formData,
      };

      await axios.post(
        process.env.NEXT_PUBLIC_ADMIN_UPDATE_USER as string,
        payload,
        {
          headers: { Authorization: `Bearer ${idToken}` },
        }
      );
      AppNotify("Statistics updated successfully", "success");
    } catch (err: any) {
      console.error(t('admin_edit_user_profile_notify.error_updating_statistics'), err);
      AppNotify(
        err.response?.data?.message || t('admin_edit_user_profile_notify.failed_update_statistics'),
        "error"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={userStyles.personalForm}>
      <h3>Statistics</h3>
      {Array.from({ length: Math.ceil(statsFields.length / 2) }).map(
        (_, rowIndex) => (
          <div key={rowIndex} className={userStyles.twoColsFields}>
            {[0, 1].map((colOffset) => {
              const field = statsFields[rowIndex * 2 + colOffset];
              if (!field) return null;
              return (
                <div key={field.key} className={userStyles.field}>
                  <div className={userStyles.label}>{field.label}</div>
                  {field.type === "dropdown" ? (
                    <CustomSelectField
                      value={formData[field.key] || ""}
                      options={field.options || []}
                      onSelect={(value: string) =>
                        handleChange(field.key, value)
                      }
                    />
                  ) : (
                    <InputTextField
                      label=""
                      placeholder={field.placeholder}
                      value={formData[field.key] || ""}
                      onChange={(e) => handleChange(field.key, e.target.value)}
                      type={field.type === "number" ? "text" : "text"}
                    />
                  )}
                </div>
              );
            })}
          </div>
        )
      )}
      <div
        className={styles.btnContainer}
        style={{ justifyContent: "flex-end" }}
      >
        <BorderButton
          onClick={handleUpdateStatistics}
          label={loading ? "Updating..." : "Update"}
          type="black"
          style={{ minWidth: "153px" }}
          disabled={loading}
        />
      </div>
    </div>
  );
};

interface AchievementsFormProps {
  selectedUser: any;
  selectedUserId: string;
}

const AchievementsForm = ({
  selectedUser,
  selectedUserId,
}: AchievementsFormProps) => {
  const totalAchievements = 12;
  const [achievements, setAchievements] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const defaultAchievements: Record<string, string> = {};
    for (let i = 0; i < totalAchievements; i++) {
      const key = `achievement${i + 1}`;
      defaultAchievements[key] = selectedUser?.achievements?.[key] || "Locked";
    }
    setAchievements(defaultAchievements);
  }, [selectedUser, selectedUser?.achievements]);

  const handleSelect = (index: number, value: string) => {
    setAchievements((prev) => ({
      ...prev,
      [`achievement${index + 1}`]: value,
    }));
  };

  const handleUpdateAchievements = async () => {
    setLoading(true);
    const idToken = await getIdToken();
    if (!idToken) {
      setLoading(false);
      return;
    }
    try {
      const payload = {
        uid: selectedUserId,
        achievements,
      };
      await axios.post(process.env.NEXT_PUBLIC_UPDATE_USER as string, payload, {
        headers: { Authorization: `Bearer ${idToken}` },
      });
      AppNotify("Achievements updated successfully", "success");
    } catch (err: any) {
      console.error(t('admin_edit_user_profile_notify.error_updating_achievements'), err);
      AppNotify(
        err.response?.data?.message || t('admin_edit_user_profile_notify.failed_update_achievements'),
        "error"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={userStyles.personalForm}>
      <h3>Achievements</h3>
      {[...Array(Math.ceil(totalAchievements / 2))].map((_, rowIndex) => (
        <div
          className={userStyles.twoColsFields}
          key={`achievement-row-${rowIndex}`}
        >
          {[0, 1].map((colIndex) => {
            const index = rowIndex * 2 + colIndex;
            if (index >= totalAchievements) return null;
            const key = `achievement${index + 1}`;
            return (
              <div className={userStyles.field} key={key}>
                <div
                  className={userStyles.label}
                >{`Achievement #${index + 1}`}</div>
                <CustomSelectField
                  value={achievements[key] || "Locked"}
                  options={[
                    { label: "Locked", value: "Locked" },
                    { label: "Unlocked", value: "Unlocked" },
                  ]}
                  onSelect={(value: string) => handleSelect(index, value)}
                />
              </div>
            );
          })}
        </div>
      ))}
      <div
        className={styles.btnContainer}
        style={{ justifyContent: "flex-end" }}
      >
        <BorderButton
          onClick={handleUpdateAchievements}
          label={loading ? "Updating..." : "Update"}
          type={"black"}
          disabled={loading}
          style={{ minWidth: "153px" }}
        />
      </div>
    </div>
  );
};

const EditUserProfile = ({ selectedUserId = "", handleGotoBack }) => {
  const { t } = useTranslation();
  const router = useRouter();
  const { user, fetching } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState<any>(null);
  const [originalUserData, setOriginalUserData] = useState<any>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setUpdate] = useState(false);
  const [isArchive, setArchive] = useState(false);
  const [startDelete, setStartDelete] = useState(false);
  const [openArchiveUserModal, setOpenArchiveUserModal] = useState(false);
  const [startApproveApplicant, setStartApproveApplicant] = useState(false);
  const [startDenyApplicant, setStartDenyApplicant] = useState(false);

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [order, setOrder] = useState<"asc" | "desc">("desc");
  const [orderBy, setOrderBy] = useState("date");
  const [rows5, setRows5] = useState<any[]>([]);

  const [profileImage, setProfileImage] = useState<File | string | null>(null);
  const [selectedUser, setSelectedUser] = useState<any>({});
  const [newFirstName, setNewFirstName] = useState("");
  const [newLastName, setNewLastName] = useState("");
  const [newEmail, setNewEmail] = useState("");
  const [newPhone, setNewPhone] = useState("");
  const [newTutorCertificationNumber, setNewTutorCertificationNumber] =
    useState("");
  const [newDob, setNewDob] = useState<any>(null);
  const [creditAmount, setCreditAmount] = useState<number>(0);
  const [deductAmount, setDeductAmount] = useState<number>(0);

  const getUserInfo = useCallback(async () => {
    if (!selectedUserId) {
      setIsLoading(false);
      return;
    }
    setIsLoading(true);
    try {
      const resp: any = await getDocument(selectedUserId, "users");
      if (resp.status && resp.exists) {
        setSelectedUser(resp.data);
        setProfileImage(resp.data.profileImageUrl || null);
        setNewFirstName(resp.data.firstName || "");
        setNewLastName(resp.data.lastName || "");
        setNewEmail(resp.data.email || "");
        setNewPhone(resp.data.phoneNumber || "");
        setNewTutorCertificationNumber(resp.data.certificateNumber || "");
        setNewDob(resp.data.dateOfBirth ? moment(resp.data.dateOfBirth) : null);
      } else {
        AppNotify(t('admin_edit_user_profile_notify.user_not_found'), "error");
        setSelectedUser({});
      }
    } catch (error) {
      console.error(t('admin_edit_user_profile_notify.error_fetching_user'), error);
      AppNotify(t('admin_edit_user_profile_notify.error_fetching_user'), "error");
    } finally {
      setIsLoading(false);
    }
  }, [selectedUserId]);

  const getApplicantAnswers = useCallback(async () => {
    if (!selectedUserId) return;
    try {
      const collectionName = "applicant_answers";
      const resp: any = await queryData(
        collectionName,
        "answeredBy",
        selectedUserId
      );
      const arr: any = [];

      if (resp && resp.status && resp.fullData && resp.fullData.length > 0) {
        const questionsResp: any = await getAllDocs("applicant_questions");
        const questionsMap = new Map(
          questionsResp.fullData.map((q: any) => [q.docId, q])
        );

        resp.fullData.forEach((answer: any) => {
          const ques: any = questionsMap.get(answer.questionId);
          const createdData = {
            date: formatDate(Number(answer.date)),
            id: ques?.guid || "",
            correct:
              answer.answer &&
              answer.correctAnswer &&
              String(answer.answer).includes(String(answer.correctAnswer))
                ? "Yes"
                : "No",
            amount: "$3",
            view: answer.questionId,
            docId: answer.docId || answer.questionId,
          };
          arr.push(createdData);
        });
      }
      setRows5(arr);
    } catch (error) {
      console.error(t('admin_edit_user_profile_notify.error_fetching_applicant_answers'), error);
      AppNotify(t('admin_edit_user_profile_notify.error_fetching_applicant_answers'), "error");
    }
  }, [selectedUserId]);

  useEffect(() => {
    getUserInfo();
    if (selectedUserId) {
      getApplicantAnswers();
    }
  }, [selectedUserId, getUserInfo, getApplicantAnswers]);

  const handleUpdateUserProfile = async () => {
    if (newFirstName.trim() === "") {
      AppNotify(t("admin.enter_your_first_name"), "error");
      return;
    }
    if (newLastName.trim() === "") {
      AppNotify(t("admin.enter_your_last_name"), "error");
      return;
    }
    if (newEmail.trim() === "") {
      AppNotify(t("admin.email_required"), "error");
      return;
    }
    if (!validateEmail(newEmail)) {
      AppNotify(t("admin.invalid_email"), "error");
      return;
    }

    setUpdate(true);
    const idToken = await getIdToken();
    if (!idToken) {
      setUpdate(false);
      return;
    }

    let uploadedProfileImageUrl: string | undefined = undefined;
    let profileImageAction: "upload" | "clear" | "none" = "none";

    if (profileImage && typeof profileImage !== "string") {
      try {
        const respUpload = await uploadImage(
          profileImage,
          selectedUser?.id as string,
          true
        );
        if (respUpload.status) {
          uploadedProfileImageUrl = respUpload.downloadUrl;
          if (uploadedProfileImageUrl !== selectedUser.profileImageUrl) {
            profileImageAction = "upload";
          }
        } else {
          AppNotify(t('admin_edit_user_profile_notify.failed_to_upload_profile_image'), "warning");
        }
      } catch (uploadError) {
        console.error(t('admin_edit_user_profile_notify.error_uploading_profile_image'), uploadError);
        AppNotify(t('admin_edit_user_profile_notify.error_uploading_profile_image'), "error");
      }
    } else if (profileImage === null && selectedUser.profileImageUrl) {
      uploadedProfileImageUrl = "";
      profileImageAction = "clear";
    }

    const apiData: any = {
      uid: selectedUserId,
      ...(newFirstName !== selectedUser.firstName && {
        firstName: newFirstName,
      }),
      ...(newLastName !== selectedUser.lastName && { lastName: newLastName }),
      ...(newEmail !== selectedUser.email && { email: newEmail }),
      ...(newPhone !== selectedUser.phoneNumber && {
        phoneNumber: newPhone || "",
      }),
      ...((newDob ? newDob.toDate().getTime() : "") !==
        (selectedUser.dateOfBirth || "") && {
        dateOfBirth: newDob ? newDob.toDate().getTime() : "",
      }),
    };

    if (selectedUser.type === "tutor") {
      if (newTutorCertificationNumber !== selectedUser.certificateNumber) {
        apiData.certificateNumber = newTutorCertificationNumber || "";
      }
    }

    if (profileImageAction === "upload" || profileImageAction === "clear") {
      apiData.profileImageUrl = uploadedProfileImageUrl;
    }

    const regex = /^[+-]?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)?$/;
    const creditAmountNum = regex.test(creditAmount) ? Number(creditAmount) : 0;
    const debitAmountNum = regex.test(deductAmount) ? Number(deductAmount) : 0;

    let newBalance;
    if (creditAmountNum > 0 || debitAmountNum > 0) {
      if (selectedUser.type === "user") {
        const currentQuestions = selectedUser.questions
          ? Number(selectedUser.questions)
          : 0;
        newBalance = currentQuestions + creditAmountNum - debitAmountNum;
        if (newBalance !== currentQuestions) apiData.questions = newBalance;
      } else {
        // tutor
        const currentRevenue = selectedUser.revenue
          ? Number(selectedUser.revenue)
          : 0;
        newBalance = currentRevenue + creditAmountNum - debitAmountNum;
        if (newBalance !== currentRevenue) apiData.revenue = newBalance;
      }
    }

    const keysToUpdate = Object.keys(apiData).filter((k) => k !== "uid");
    if (keysToUpdate.length === 0) {
      AppNotify(t('admin_edit_user_profile_notify.no_changes'), "info");
      setUpdate(false);
      setCreditAmount("");
      setDeductAmount("");
      return;
    }

    const auth = getAuth();
    const adminUid = auth.currentUser?.uid || "unknown_admin";

    if (creditAmountNum > 0) {
      await addDocInCollection(
        "admin_question_credits",
        {
          userId: selectedUserId,
          amount: creditAmountNum,
          operation: "add",
          detail: `Admin addition of ${creditAmountNum} ${selectedUser.type === "user" ? "question credits" : "to revenue"}`,
          date: new Date().getTime(),
          adminUid,
        },
        true
      );
    }
    if (debitAmountNum > 0) {
      await addDocInCollection(
        "admin_question_credits",
        {
          userId: selectedUserId,
          amount: debitAmountNum,
          operation: "remove",
          detail: `Admin removal of ${debitAmountNum} ${selectedUser.type === "user" ? "question credits" : "from revenue"}`,
          date: new Date().getTime(),
          adminUid,
        },
        true
      );
    }

    try {
      const resp = await axios.post(
        process.env.NEXT_PUBLIC_ADMIN_UPDATE_USER as string,
        apiData,
        { headers: { Authorization: `Bearer ${idToken}` } }
      );

      if (resp.data && resp.data.status) {
        AppNotify(t('admin_edit_user_profile_notify.profile_updated'), "success");
        await getUserInfo();
        setCreditAmount("");
        setDeductAmount("");
      } else {
        AppNotify(
          resp.data.message || t('admin_edit_user_profile_notify.failed_update'),
          "error"
        );
      }
    } catch (error: any) {
      console.error(t('admin_edit_user_profile_notify.error_updating'), error);
      AppNotify(
        error.response?.data?.message || t('admin_edit_user_profile_notify.error_updating'),
        "error"
      );
    } finally {
      setUpdate(false);
    }
  };

  const approveApplicant = async () => {
    setIsLoading(true);
    try {
      const resp = await updateCollection(
        "users",
        selectedUserId,
        { isApplicant: false },
        true
      );
      if (resp.status) {
        AppNotify(t('admin_edit_user_profile_notify.applicant_approved'), "success");
        await getUserInfo();
        setStartApproveApplicant(false);
      } else {
        AppNotify(t('admin_edit_user_profile_notify.failed_approve'), "error");
      }
    } catch (error) {
      console.error(t('admin_edit_user_profile_notify.error_approving'), error);
      AppNotify(t('admin_edit_user_profile_notify.error_approving'), "error");
    } finally {
      setIsLoading(false);
    }
  };

  const denyApplicant = async () => {
    setIsLoading(true);
    try {
      const respUserUpdate = await updateCollection(
        "users",
        selectedUserId,
        { applicantQuestionAnswered: 0, isApplicant: false },
        true
      );

      const answersQuery = await queryData(
        "applicant_answers",
        "answeredBy",
        selectedUserId
      );
      let allAnswersDeleted = true;
      if (
        answersQuery.status &&
        answersQuery.fullData &&
        answersQuery.fullData.length > 0
      ) {
        for (const answerDoc of answersQuery.fullData) {
          const deleteResp = await deleteDocument(
            "applicant_answers",
            answerDoc.docId,
            true
          );
          if (!deleteResp.status) {
            allAnswersDeleted = false;
            console.error(
              `Failed to delete applicant answer: ${answerDoc.docId}`
            );
          }
        }
      }

      if (respUserUpdate.status && allAnswersDeleted) {
        AppNotify(t('admin_edit_user_profile_notify.applicant_denied'), "success");
        await getUserInfo();
        setStartDenyApplicant(false);
      } else {
        AppNotify(
          t('admin_edit_user_profile_notify.failed_deny'),
          "error"
        );
      }
    } catch (error) {
      console.error(t('admin_edit_user_profile_notify.error_denying'), error);
      AppNotify(t('admin_edit_user_profile_notify.error_denying'), "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleArchiveUser = async () => {
    setArchive(true);
    try {
      const resp = await updateCollection(
        "users",
        selectedUserId,
        { is_archived: true },
        true
      );
      if (resp.status) {
        AppNotify(t('admin_edit_user_profile_notify.archived'), "success");
        setOpenArchiveUserModal(false);
        await getUserInfo();
      } else {
        AppNotify(t('admin_edit_user_profile_notify.failed_archive'), "error");
      }
    } catch (error) {
      console.error(t('admin_edit_user_profile_notify.error_archiving'), error);
      AppNotify(t('admin_edit_user_profile_notify.error_archiving'), "error");
    } finally {
      setArchive(false);
    }
  };

  const handleUnarchiveUser = async () => {
    setArchive(true);
    try {
      const resp = await updateCollection(
        "users",
        selectedUserId,
        { is_archived: false },
        true
      );
      if (resp.status) {
        AppNotify(t('admin_edit_user_profile_notify.restored'), "success");
        setOpenArchiveUserModal(false);
        await getUserInfo();
      } else {
        AppNotify(t('admin_edit_user_profile_notify.failed_restore'), "error");
      }
    } catch (error) {
      console.error(t('admin_edit_user_profile_notify.error_restoring'), error);
      AppNotify(t('admin_edit_user_profile_notify.error_restoring'), "error");
    } finally {
      setArchive(false);
    }
  };

  const deleteUser = async () => {
    setIsLoading(true);
    try {
      const resp = await deleteDocument("users", selectedUserId, true);
      if (resp.status) {
        AppNotify(t('admin_edit_user_profile_notify.deleted'), "success");
        setStartDelete(false);
        handleGotoBack && handleGotoBack();
      } else {
        AppNotify(t('admin_edit_user_profile_notify.failed_delete'), "error");
      }
    } catch (error) {
      console.error(t('admin_edit_user_profile_notify.error_deleting'), error);
      AppNotify(t('admin_edit_user_profile_notify.error_deleting'), "error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleGotoQuestion = (questionIdToGo: string) => {
    console.log("Navigate to question:", questionIdToGo);
    AppNotify(
      `Navigation to question ${questionIdToGo} (placeholder).`,
      "info"
    );
  };

  const isAdminProfile = !!(
    selectedUser &&
    selectedUser.type &&
    selectedUser.type === "admin"
  );

  if (isLoading && !Object.keys(selectedUser).length) {
    return <Spinner />;
  }

  return (
    <div className={quesStyles.questionsWrapper} style={{ padding: "0 15px" }}>
      <div className={styles.userProfileBox}>
        {isAdminProfile && <div className={styles.adminLabel}>Admin</div>}

        {selectedUser.isApplicant && (
          <div className={styles.userDetails} style={{ width: "100%" }}>
            <div className={styles.userActionBox}>
              <img
                src={
                  selectedUser.profileImageUrl || "/icons/placeholder-image.svg"
                }
                alt="Applicant Profile"
                style={{
                  width: "100px",
                  height: "100px",
                  borderRadius: "50%",
                  objectFit: "cover",
                  marginBottom: "10px",
                }}
              />
              <div className={styles.approvalBtns}>
                <BorderButton
                  type={"black"}
                  label={"Approve"}
                  onClick={() => setStartApproveApplicant(true)}
                />
                <BorderButton
                  type={"black"}
                  label={"Deny"}
                  onClick={() => setStartDenyApplicant(true)}
                />
              </div>
            </div>
          </div>
        )}

        {isUpdating && <Spinner />}
        <>
          {!selectedUser.isApplicant && (
            <div className={userStyles.personalForm}>
              <h3>Update User Details</h3>
              <div style={{ marginBottom: "20px" }}>
                <FileInputWithPreview
                  simpleChange={true}
                  onChange={(fileOrNull) => setProfileImage(fileOrNull)}
                  userImg={
                    profileImage && typeof profileImage !== "string"
                      ? URL.createObjectURL(profileImage)
                      : profileImage || "/icons/placeholder-image.svg"
                  }
                />
              </div>
              <div className={userStyles.twoColsFields}>
                <div className={userStyles.field}>
                  <div className={userStyles.label}>{t('admin_edit_user_profile.first_name_label')}</div>
                  <InputTextField
                    label=""
                    placeholder={t('admin_edit_user_profile.first_name_placeholder')}
                    value={newFirstName}
                    onChange={(e) => setNewFirstName(e.target.value)}
                  />
                </div>
                <div className={userStyles.field}>
                  <div className={userStyles.label}>{t('admin_edit_user_profile.last_name_label')}</div>
                  <InputTextField
                    label=""
                    placeholder={t('admin_edit_user_profile.last_name_placeholder')}
                    value={newLastName}
                    onChange={(e) => setNewLastName(e.target.value)}
                  />
                </div>
              </div>
              <div className={userStyles.twoColsFields}>
                <div className={userStyles.field}>
                  <div className={userStyles.label}>{t('admin_edit_user_profile.email_address_label')}</div>
                  <InputTextField
                    label=""
                    placeholder={t('admin_edit_user_profile.email_placeholder')}
                    value={newEmail}
                    onChange={(e) => setNewEmail(e.target.value)}
                  />
                </div>
                <div className={userStyles.field}>
                  <div className={userStyles.label}>Phone Number</div>
                  <MuiPhoneNumber
                    className="flag-dropdown"
                    defaultCountry={"us"}
                    value={newPhone}
                    onChange={(val: string) => setNewPhone(val)}
                    sx={defaultInputFieldStyle}
                    variant="outlined"
                  />
                </div>
              </div>
              {selectedUser.type === "tutor" && (
                <div className={userStyles.twoColsFields}>
                  <div className={userStyles.field}>
                    <div className={userStyles.label}>{t('admin_edit_user_profile.tutor_certification_label')}</div>
                    <InputTextField
                      label=""
                      placeholder={t('admin_edit_user_profile.certification_placeholder')}
                      value={newTutorCertificationNumber}
                      onChange={(e) => setNewTutorCertificationNumber(e.target.value)}
                    />
                  </div>
                  <div className={userStyles.field}>
                    <div className={userStyles.label}>Birthday</div>
                    <DatePicker
                      sx={defaultInputFieldStyle}
                      value={newDob}
                      onChange={(newValue) => setNewDob(newValue)}
                    />
                  </div>
                </div>
              )}
              {!isAdminProfile && (
                <div className={userStyles.creditBox}>
                  {selectedUser.type === "user" ? (
                    <div className={userStyles.label}>
                      Question Credits:{" "}
                      <span>{selectedUser.questions ?? "0"}</span>
                    </div>
                  ) : (
                    <div className={userStyles.label}>
                      Current Balance:{" "}
                      <span>${selectedUser.revenue?.toFixed(2) ?? "0.00"}</span>
                    </div>
                  )}
                  <div className={userStyles.creditBoxFields}>
                    <div className={userStyles.field}>
                      <div className={userStyles.label}>{t('admin_edit_user_profile.credit_amount_label')}</div>
                      <QuantityStepper
                        min={0}
                        max={10000}
                        onChange={setCreditAmount}
                        value={creditAmount}
                      />
                    </div>
                    <div className={userStyles.field}>
                      <div className={userStyles.label}>{t('admin_edit_user_profile.deduct_amount_label')}</div>
                      <QuantityStepper
                        min={0}
                        max={10000}
                        onChange={setDeductAmount}
                        value={deductAmount}
                      />
                    </div>
                  </div>
                </div>
              )}
              <div
                className={styles.btnContainer}
                style={{ justifyContent: "flex-end" }}
              >
                <BorderButton
                  onClick={handleUpdateUserProfile}
                  label={isUpdating ? "Updating..." : "Update Profile"}
                  type={"black"}
                  style={{ minWidth: "153px" }}
                  disabled={isUpdating || isLoading}
                />
              </div>
            </div>
          )}

          {selectedUser.type === "tutor" &&
            !selectedUser.isApplicant &&
            Object.keys(selectedUser).length > 0 && (
              <StatisticsForm
                selectedUser={selectedUser}
                selectedUserId={selectedUserId}
              />
            )}
          {selectedUser.type === "tutor" &&
            !selectedUser.isApplicant &&
            Object.keys(selectedUser).length > 0 && (
              <AchievementsForm
                selectedUser={selectedUser}
                selectedUserId={selectedUserId}
              />
            )}
        </>
      </div>

      {selectedUser.isApplicant && rows5.length > 0 && (
        <div className={userStyles.personalForm} style={{ marginTop: "20px" }}>
          <h3>Test Question Answers</h3>
          <Paper
            sx={{
              width: "100%",
              boxShadow: "none",
              borderRadius: "8px",
              border: "1px solid rgba(229, 229, 229, 1)",
            }}
          >
            <TableContainer className={"tableContainer"}>
              <Table>
                <EnhancedTableHead
                  columns={testAnswersColumns as Column3[]}
                  order={order}
                  orderBy={orderBy}
                  onRequestSort={handleRequestSort}
                />
                <TableBody>
                  {stableSort(rows5, getComparator(order, orderBy))
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((row) => (
                      <TableRow
                        hover
                        role="checkbox"
                        tabIndex={-1}
                        key={row.docId}
                      >
                        {testAnswersColumns.map((column) => {
                          const value = row[column.id];
                          return (
                            <TableCell key={column.id} align={column.align}>
                              {column.id === "view" ? (
                                <a
                                  style={{
                                    cursor: "pointer",
                                    color: "rgba(69, 176, 246, 1)",
                                  }}
                                  onClick={() => handleGotoQuestion(value)}
                                >
                                  View
                                </a>
                              ) : column.id === "amount" ? (
                                <span style={{ color: "rgba(107, 204, 9, 1)" }}>
                                  {value}
                                </span>
                              ) : (
                                value
                              )}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
          {rows5.length > 0 && (
            <TableCustomPagination
              filteredRowsLength={rows5.length}
              page={page}
              rowsPerPage={rowsPerPage}
              setPage={setPage}
              setRowsPerPage={setRowsPerPage}
            />
          )}
        </div>
      )}
      {selectedUser.isApplicant && rows5.length === 0 && !isLoading && (
        <div className={userStyles.personalForm} style={{ marginTop: "20px" }}>
          <h3>Test Question Answers</h3>
          <div
            className={quesStyles.searchLoader}
            style={{ margin: "9px 12px" }}
          >
            The user has not yet answered a question.
          </div>
        </div>
      )}

      {/* Modals */}
      <ConfirmModal
        open={startApproveApplicant || startDenyApplicant}
        title={startApproveApplicant ? t("admin_edit_user_profile.approve_applicant_title") : t("admin_edit_user_profile.deny_applicant_title")}
        content={t("admin_edit_user_profile.irreversible_action_content")}
        actionLabel={startApproveApplicant ? t("admin_edit_user_profile.approve_button") : t("admin_edit_user_profile.deny_button")}
        handleClose={() => {
          setStartApproveApplicant(false);
          setStartDenyApplicant(false);
        }}
        onConfirm={startApproveApplicant ? approveApplicant : denyApplicant}
        isLoading={isLoading}
      />
      <ConfirmModal
        open={startDelete}
        title={t("admin_edit_user_profile.delete_user_title") }
        content={t("admin_edit_user_profile.delete_user_content")}
        actionLabel={t("admin_edit_user_profile.delete_user_button")}
        handleClose={() => setStartDelete(false)}
        onConfirm={deleteUser}
        isDeleteConfirm={true}
        isLoading={isLoading}
      />
      <ConfirmModal
        open={openArchiveUserModal}
        title={
          selectedUser.is_archived
            ? t("admin_edit_user_profile.unarchive_user_title")
            : t("admin_edit_user_profile.archive_user_title")
        }
        content={
          selectedUser.is_archived
            ? t("admin_edit_user_profile.unarchive_user_content")
            : t("archive_user_content")
        }
        actionLabel={selectedUser.is_archived ? t("admin_edit_user_profile.unarchive_button") : t("admin_edit_user_profile.archive_button")}
        handleClose={() => setOpenArchiveUserModal(false)}
        onConfirm={
          selectedUser.is_archived ? handleUnarchiveUser : handleArchiveUser
        }
        isDeleteConfirm={selectedUser.is_archived ? false : true}
        isLoading={isArchive}
      />
    </div>
  );
};

export default EditUserProfile;
