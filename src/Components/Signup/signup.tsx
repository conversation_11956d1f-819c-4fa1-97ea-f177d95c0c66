"use client";
import React, {useContext, useEffect, useState} from "react";
import styles from "@/src/styles/login.module.css";
import sgStyles from "@/src/styles/signup.module.css";
import {createAuthUserWithEmailAndPassword, createUserDocumentFromAuth,} from "@/src/Firebase/firebase.utils";
import {UserCredential} from "firebase/auth";
import {useRouter, useSearchParams} from "next/navigation";
import {validateEmail} from "@/src/Utils/helpers";
import {UserContext} from "@/src/Contexts/UserContext";
import {useTranslation} from "@/src/Utils/i18n";
import {AppNotify} from "@/src/Utils/AppNotify";
import CustomCheckbox from "../Form/CustomCheckbox";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import InputTextField from "@/src/Components/Form/InputTextField";
import PasswordField from "@/src/Components/Form/PasswordField";
import "../../styles/globals.css";
import TopBarWithLogo from "@/src/Components/Widgets/TopBarWithLogo";
import axios from "axios";

const Signup = () => {
    const {t, locale} = useTranslation();
    const [email, setEmail] = useState("");
    const {user, fetching} = useContext(UserContext);
    const [emailHelper, setEmailHelper] = useState("");
    const [passwordHelper, setPasswordHelper] = useState("");
    const [password, setPassword] = useState("");
    const [creating, setCreating] = useState(false);
    const [authSuccessful, setAuthSuccessful] = useState(false); 
    const [termsCheck, setTermsCheck] = useState(false);
    const [ageCheck, setAgeCheck] = useState(true);
    const [firstName, setFirstName] = useState("");
    const [lastName, setLastName] = useState("");
    const searchParams = useSearchParams();
    const redirectTo = searchParams.get("redirect_to");
    const router = useRouter();
    const type = redirectTo === "packs" ? "user" : "tutor";

    const [isOpen, setIsOpen] = useState(true);
    const toggleContent = () => setIsOpen(!isOpen);

    const pushPage = () => {
        if (user?.type === "tutor") {
            router.push(`/${locale}/profile`);
        } else if (user?.type === "user") {
            router.push(`/${locale}/payment`);
        } else {
            router.push(`/${locale}`);
        }
    };

    useEffect(() => {
        if (authSuccessful && fetching === false && user.id !== "") {
            pushPage();
        }
    }, [authSuccessful, fetching, user, router, redirectTo]);

    // Handle already logged in users
    useEffect(() => {
        if (fetching === false && user.id !== "") {
            // Intentionally left empty: handle already logged in users here if needed in the future
        }
    }, [user, fetching]);

    useEffect(() => {
        setEmailHelper("");
        setPasswordHelper("");
    }, [email, password]);

    const handleSubmit = async () => {
        if (email === "") {
            setEmailHelper(t('auth.email_required'));
            return null;
        }

        if (password === "") {
            setPasswordHelper(t('auth.password_required'));
            return null;
        }

        if (!validateEmail(email)) {
            setEmailHelper(t('auth.invalid_email'));
            return null;
        }

        if (firstName === "" && type === "tutor") {
            AppNotify(t('auth.first_name_required'), "error");
            return null;
        }

        if (firstName === "" && type === "user") {
            AppNotify(t('auth.first_name_required'), "error");
            return null;
        }

        if (lastName === "" && type === "user") {
            AppNotify(t('auth.last_name_required'), "error");
            return null;
        }

        if (password.length < 8) {
            setPasswordHelper(t('auth.password_too_short'));
            return null;
        }

            setCreating(true);
            setAuthSuccessful(false);
    
            try {
                const resp = await axios.post(
                    process.env.NEXT_PUBLIC_CHECK_ACCOUNT_EXISTS as string,
                    {
                        email,
                    }
                );
    
                if (resp.data.status && resp.data.exists) {
                    AppNotify(t('auth.email_exists'), "warning");
                    setCreating(false);
                    return null;
                }
    
                const userData: any = {
                    type: redirectTo === "packs" ? "user" : "tutor",
                    email,
                    firstName,
                    lastName,
                    isApplicant: redirectTo !== "packs",
                    isAvailable: true,
                };
    
                const userCredential: UserCredential | undefined = await createAuthUserWithEmailAndPassword(email, password);
    
                if (userCredential) {
                    await createUserDocumentFromAuth(userCredential.user, userData);
    
                    if (redirectTo === "packs") {
                        window.location.href = `/${locale}/payment`;
                    } else if (type === "tutor") {
                        window.location.href = `/${locale}/profile`;
                    } else {
                        router.push(`/${locale}`);
                    }
                } else {
                    AppNotify("Failed to create user account.", "error");
                    setAuthSuccessful(false);
                }
            } catch (error) {
                console.error("Error creating user:", error);
                console.log("Error type:", typeof error);
                console.log("Error object:", error);
                
                // Handle Firebase Auth errors, including duplicate email
                if (error && typeof error === 'object' && 'code' in error) {
                    const firebaseError = error as { code: string; message?: string };
                    console.log("Firebase error code:", firebaseError.code);
                    
                    if (firebaseError.code === 'auth/email-already-in-use') {
                        AppNotify("An account with this Email Address already exists.", "warning");
                        setEmailHelper("An account with this Email Address already exists.");
                    } else if (firebaseError.code === 'auth/weak-password') {
                        AppNotify("Password is too weak.", "error");
                        setPasswordHelper("Password is too weak.");
                    } else if (firebaseError.code === 'auth/invalid-email') {
                        AppNotify("Invalid email address.", "error");
                        setEmailHelper("Invalid email address.");
                    } else {
                        console.log("Unhandled Firebase error code:", firebaseError.code);
                        AppNotify("Failed to create user account.", "error");
                    }
                } else {
                    console.log("Error doesn't have expected structure");
                    AppNotify(t('auth.create_user_failed'), "error");
                }
                
                setCreating(false);
                setAuthSuccessful(false);
            }
        };
    

    const handleDisable = () => {
        return !(email && password && firstName && lastName && termsCheck && ageCheck);
    };

    const isLoading = creating || (authSuccessful && fetching !== false);

    const renderSignupFields = () => {
        return (
            <>
                {type === "tutor" ? (
                    <>
                        <div className={styles.formTitle}>{t('auth.register_as_tutor')}</div>
                        <div className={sgStyles.mustBeText}>
                            {t('auth.must_be_13_tutor')}
                        </div>
                    </>
                ) : (
                    <>
                        <div className={styles.formTitle}>{t('auth.register_as_student')}</div>
                        <div className={sgStyles.mustBeText}>
                            {t('auth.must_be_13_student')}
                        </div>
                    </>
                )}

                <div className={`${styles.twoFieldsRow}`}>
                    <div className={styles.field}>
                        <div className={styles.label}>{t('common.first_name')}</div>
                        <InputTextField
                            label=""
                            icon={"user-icon"}
                            placeholder={t('auth.enter_first_name')}
                            value={firstName}
                            onChange={(e) => setFirstName(e.target.value)}
                        />
                    </div>

                    <div className={styles.field}>
                        <div className={styles.label}>{t('common.last_name')}</div>
                        <InputTextField
                            label=""
                            icon={"user-icon"}
                            placeholder={t('auth.enter_last_name')}
                            value={lastName}
                            onChange={(e) => setLastName(e.target.value)}
                        />
                    </div>
                </div>

                <div className={styles.field}>
                    <div className={styles.label}>{t('common.email')}</div>
                    <InputTextField
                        label=""
                        icon={"envelope-icon"}
                        placeholder={t('auth.enter_email')}
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                    />
                    {emailHelper ? (
                        <div className={styles.helper}>
                            <img src="/icons/validate.svg" alt="validate"/>
                            {emailHelper}
                        </div>
                    ) : (
                        ""
                    )}
                </div>

                <div className={styles.field}>
                    <div className={styles.label}>{t('common.password')}</div>
                    <PasswordField
                        showIcon={true}
                        placeholder={t('auth.enter_password')}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        fullWidth
                    />
                    {passwordHelper ? (
                        <div className={styles.helper}>
                            <img src="/icons/validate.svg" alt="validate"/>
                            {passwordHelper}
                        </div>
                    ) : (
                        ""
                    )}
                </div>

            
            </>
        );
    };

    const renderBottomFields = () => {
        return (
            <>
                <div className={styles.field} style={{gap: "16px"}}>
                    <CustomCheckbox
                        id={"checkboxAge"}
                        defaultLabel={t('auth.signup_13_years_old')}
                        onChange={setAgeCheck}
                        defaultChecked={ageCheck}
                    />
                    <CustomCheckbox
                        id={"checkboxAgree"}
                        defaultLabel={t('auth.signup_terms_and_conditions')}
                        onChange={setTermsCheck}
                    />
                </div>

                <div className={`${styles.formSubmit} ${styles.formSignUpSubmit}`}>
                    <PrimaryButton
                       disabled={handleDisable()}
                        onClick={handleSubmit}
                        label={type === "user" ? t('common.create_student_account') : t('common.create_tutor_account')}
                        loading={creating}
                        size={'greenBtn'}
                    />
                </div>

                <div className={sgStyles.login} style={{color: '#777777'}}>
                    {t('auth.already_have_account')} {" "}
                    <span
                        className={sgStyles.loginLink}
                        onClick={() => {
                            if (redirectTo) {
                                router.push(`/${locale}/login?redirect_to=${redirectTo}`);
                            } else {
                                router.push(`/${locale}/login`);
                            }
                        }}
                    >
                    {t('common.login_here')}
                    </span>
                </div>
            </>
        )
    }

    if (type && type === 'user') {
        return (
            <div className={styles.loginPage}>
                <img className={styles.loginTopImage} src={'/images/login-body-bg.svg'} alt={'login-body-bg'}/>
                <img className={`${styles.loginTopImage} ${styles.loginTopMobile}`}
                     src={'/images/login-body-mobile-bg.svg'} alt={'login-body-bg'}/>
                <div className="mainContainer">
                    <TopBarWithLogo
                        buttonLabel={t(type === 'user' ? 'navbar_legal.login' : 'homepage.become_student')}
                        buttonUrl={type === 'user' ?  `/${locale}/login` : `/${locale}/become-a-student`}
                    />
                </div>

                <div className={`${styles.loginSection} ${styles.studentSignupBox}`} onKeyDown={(e) => e.key === "Enter" && !isLoading && handleSubmit()}>
                    <div className={`${styles.loginFormSection} ${styles.studentSignup}`}
                         style={{paddingBottom: '25px'}}>
                        {renderSignupFields()}
                        {renderBottomFields()}
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className={`${styles.signupPage}`}>
            <div className="mainContainer">
                <TopBarWithLogo
                    buttonLabel={t('homepage.become_student')}
                    buttonUrl={`/${locale}/become-a-student`}
                />
                <div className={styles.signupSection} onKeyDown={(e) => e.key === "Enter" && !isLoading && handleSubmit()}>
                    <div className={styles.hiwSection}>
                        <div className={styles.hiwToggleHeader} onClick={toggleContent}>
                            <h2>{t('auth.signup_at_least_13')}</h2>
                            <span className={styles.hiwToggleIcon}><img className={isOpen ? '' : styles.hiwRotateIcon} src={'/icons/home/<USER>'} alt={'arrow-down'}/></span>
                        </div>

                        <div className={`${styles.hiwSteps} ${isOpen ? styles.showSteps : styles.hideSteps}`}>
                            {[1, 2, 3, 4].map((step, index) => {
                                const titles = [
                                    t('auth.earn_per_answer'),
                                    t('auth.set_own_hours'),
                                    t('auth.easy_qualifications'),
                                    t('auth.how_it_works'),
                                ];
                                const texts = [
                                    t('auth.earn_per_answer_text'),
                                    t('auth.set_own_hours_text'),
                                    t('auth.easy_qualifications_text'),
                                    t('auth.how_it_works_text'),
                                ];

                                return (
                                    <div key={step} className={styles.hiwStep}>
                                        <div className={styles.hiwStepNum}>{step}</div>
                                        <div className={styles.hiwStepInfo}>
                                            <div className={styles.hiwStepTitle}>{titles[index]}</div>
                                            <div className={styles.hiwStepText}>{texts[index]}</div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>

                    <div className={styles.signupFormSection}>
                        <div className={styles.formFieldsWrapper}>
                            {renderSignupFields()}
                            {renderBottomFields()}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Signup;