"use client";
import React, {useContext, useEffect, useState} from "react";
import {useRouter, useSearchParams} from "next/navigation";
import {
    getAllDocs,
    isStudentAuthUser,
    signInAuthUserWithEmailAndPassword,
    signOutUser,
} from "@/src/Firebase/firebase.utils";
import {UserContext} from "@/src/Contexts/UserContext";
import {useTranslation} from "@/src/Utils/i18n";
import {validateEmail} from "@/src/Utils/helpers";
import TopBarWithLogo from "@/src/Components/Widgets/TopBarWithLogo";
import {AppNotify} from "@/src/Utils/AppNotify";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import InputTextField from "@/src/Components/Form/InputTextField";
import PasswordField from "@/src/Components/Form/PasswordField";
import styles from "@/src/styles/login.module.css";
import sgStyles from "@/src/styles/signup.module.css";

const Login = () => {
    const {user, fetching} = useContext(UserContext);
    const {t, locale} = useTranslation();
    const [redirectUser, setRedirectUser] = useState(false);
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [emailHelper, setEmailHelper] = useState("");
    const [submitPressed, setSubmitPressed] = useState(false);
    const [passwordHelper, setPasswordHelper] = useState("");
    const [creating, setCreating] = useState(false);
    const router = useRouter();
    const searchParams = useSearchParams();
    const redirectTo = searchParams.get("redirect_to");

    const getRedirectPath = React.useCallback(async () => {
        const lang = locale === "en" || !locale ? "en" : locale;
        if (redirectTo) {
            return `/${lang}/${redirectTo}`;
        }

        if (user && user.type === 'admin') {
            return `/${lang}/admin/dashboard`;
        }

        if (user && user.type === 'tutor') {
            return `/${lang}/profile`;
        }

        // students - payment success
        if (user && user.type === 'user') {
            try {
                const isPaid = await isStudentAuthUser(user.id);
                if (!isPaid) {
                    return `/${lang}/payment`;
                }
                // paid go to dashboard /ask
                return `/${lang}/ask`;
            } catch (error) {
                console.error("Error checking payment status:", error);
                return `/${lang}/payment`;
            }
        }

        return `/${lang}`;
    }, [user, locale, redirectTo]);

    useEffect(() => {
        const handleRedirect = async () => {
            if (user.id !== "" && redirectUser && user.type) {
                const redirectPath = await getRedirectPath();
                router.push(redirectPath);
            }
        };
        handleRedirect();
    }, [redirectUser, user, locale, redirectTo, router, getRedirectPath]);

    useEffect(() => {
        const handleExistingUser = async () => {
            if (!submitPressed && !fetching && user.id !== "" && user.type) {
                const redirectPath = await getRedirectPath();
                router.push(redirectPath);
            }
        };
        handleExistingUser();
    }, [user, fetching, locale, submitPressed, router, getRedirectPath]);

    const handleSignOut = async () => {
        await signOutUser();
        router.push(`/${locale}/login`);
    };

    const handleSubmit = async () => {
        setEmailHelper("");
        setPasswordHelper("");

        // Validation checks
        if (email === "") {
            setEmailHelper(t('auth.email_required'));
            return null;
        }

        if (password === "") {
            setPasswordHelper(t('auth.password_required'));
            return null;
        }

        if (!validateEmail(email)) {
            setEmailHelper(t('auth.invalid_email'));
            return null;
        }

        if (password.length < 8) {
            setPasswordHelper(t('auth.password_too_short'));
            return null;
        }

        setCreating(true);
        setSubmitPressed(true);

        try {
            const resp = await signInAuthUserWithEmailAndPassword(email, password, true);

            if (resp?.user) {
                const respUsers: any = await getAllDocs("users");

                if (respUsers.status && respUsers.fullData.length > 0) {
                    const users = respUsers.fullData.map((option: any) => option);
                    const user: any = users.find(
                        (item: any) => item.docId === resp?.user.uid
                    );

                    if (user) {
                        if (user.is_archived) {
                            AppNotify(
                                t('auth.login_blocked_contact_support'),
                                "error"
                            );
                            setTimeout(() => {
                                handleSignOut();
                            }, 500);
                        } else {
                            setRedirectUser(true);
                        }
                    } else {
                        AppNotify(t('auth.user_deleted'), "error");
                    }
                } else {
                    AppNotify(t('auth.unable_to_verify_account'), "error");
                }
            } else {
                throw new Error(t('auth.authentication_failed'));
            }
        } catch (error: any) {
            console.error("Login error:", error);

            // Specific Firebase auth error codes
            if (error?.code) {
                switch (error.code) {
                    case 'auth/user-not-found':
                    case 'auth/wrong-password':
                    case 'auth/invalid-credential':
                    case 'auth/invalid-login-credentials':
                        AppNotify(t('auth.wrong_email_password'), "error");
                        break;
                    case 'auth/user-disabled':
                        AppNotify(t('auth.account_disabled_contact_support'), "error");
                        break;
                    case 'auth/too-many-requests':
                        AppNotify(t('auth.too_many_failed_attempts'), "error");
                        break;
                    case 'auth/network-request-failed':
                        AppNotify(t('errors.network_error'), "error");
                        break;
                    default:
                        AppNotify(t('auth.login_failed_try_again'), "error");
                }
            } else {
                // fallback
                AppNotify(t('auth.wrong_email_password'), "error");
            }
        } finally {
            setCreating(false);
            setSubmitPressed(false); 
        }
    };

    return (
        <div className={styles.loginPage}>
            <img className={styles.loginTopImage} src={'/images/login-body-bg.svg'} alt={'login-body-bg'}/>
            <img className={`${styles.loginTopImage} ${styles.loginTopMobile}`} src={'/images/login-body-mobile-bg.svg'}
                 alt={'login-body-bg'}/>
            <div className="mainContainer">
                <TopBarWithLogo
                    buttonLabel={t('homepage.become_tutor')}
                    buttonUrl={`/${locale}/signup`}
                />

                <div className={styles.loginSection} onKeyDown={(e) => e.key === "Enter" && handleSubmit()}>
                    <div className={styles.loginFormSection}>

                        <div className={styles.formTitle}>{t('auth.login_title')}</div>

                        <div className={styles.formFieldsWrapper}>
                            <div className={styles.field}>
                                <div className={styles.label}>{t('common.email')}</div>
                                <InputTextField
                                    icon={"envelope-icon"}
                                    placeholder={t('auth.enter_email')}
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                />
                                {emailHelper ? (
                                    <div className={styles.helper}>
                                        <img src="/icons/validate.svg" alt="validate"/>
                                        {emailHelper}
                                    </div>
                                ) : (
                                    ""
                                )}
                            </div>

                            <div className={styles.field}>
                                <div className={styles.label}>{t('common.password')}</div>
                                <PasswordField
                                    showIcon={true}
                                    placeholder={t('auth.enter_password')}
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    fullWidth
                                />
                                {passwordHelper ? (
                                    <div className={styles.helper}>
                                        <img src="/icons/validate.svg" alt="validate"/>
                                        {passwordHelper}
                                    </div>
                                ) : (
                                    ""
                                )}
                            </div>

                            <div className={styles.formSubmit}>
                                <PrimaryButton
                                    size={'greenBtn'}
                                    disabled={!(email && password)}
                                    onClick={handleSubmit}
                                    label={t('common.login')}
                                    loading={creating}
                                />
                            </div>
                            <div className={sgStyles.login} style={{cursor: 'pointer'}}
                                 onClick={() => router.push(`/${locale}/forgot-password`)}>{t('auth.forgot_password')}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Login;