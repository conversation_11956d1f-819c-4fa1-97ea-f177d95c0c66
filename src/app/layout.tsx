import React from "react";
import type { Metada<PERSON>, Viewport } from "next";
import { ToastContainer } from "react-toastify";

import UserProvider from "@/src/Contexts/UserContext";
import { ImageModalProvider } from "@/src/Contexts/ImageModalContext";
import { I18nProvider } from "@/src/Utils/i18n";
import "react-toastify/dist/ReactToastify.css";
import "react-datepicker/dist/react-datepicker.css";
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import "@/src/styles/globals.css";
import { NavigationProvider } from "../Contexts/LayoutContext";
import ThemeClient from "../Components/ThemeClient";
import CrowdinScripts from "../Components/CrowdinScripts";
import Head from "next/head";

export const viewport: Viewport = {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
};

export const metadata: Metadata = {
    title: {
        template: '%s | Odevly',
        default: 'Odevly | Ask a question, Get an answer'
    },
    description: "Our expert tutors are ready 24/7 to answer any homework question. Just upload a photo of your homework and get an answer in minutes!",
    icons: {
        icon: [
            { url: '/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
            { url: '/favicon.svg', type: 'image/svg+xml' },
        ],
        shortcut: '/favicon.ico',
        apple: '/apple-touch-icon.png',
    },
    manifest: '/site.webmanifest',
    openGraph: {
        title: 'Odevly | Ask a question, Get an answer',
        description: 'Our expert tutors are ready 24/7 to answer any homework question.',
        type: 'website',
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Odevly | Ask a question, Get an answer',
        description: 'Our expert tutors are ready 24/7 to answer any homework question.',
    },
};

const Providers = React.memo(({ children }: { children: React.ReactNode }) => (
    <NavigationProvider>
        <I18nProvider defaultLocale="en">
            <ThemeClient>
                <UserProvider>
                    <ImageModalProvider>
                        {children}
                        <ToastContainer
                            position="top-right"
                            autoClose={5000}
                            hideProgressBar={false}
                            newestOnTop={false}
                            closeOnClick
                            rtl={false}
                            pauseOnFocusLoss
                            draggable
                            pauseOnHover
                            theme="colored"
                            toastClassName="custom-toast"
                            bodyClassName="custom-toast-body"
                        />
                    </ImageModalProvider>
                </UserProvider>
            </ThemeClient>
        </I18nProvider>
    </NavigationProvider>
));

Providers.displayName = 'Providers';

interface RootLayoutProps {
    children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
    return (
        <html lang="en" suppressHydrationWarning>
            <Head>
                <title>
                   Odevly
                </title>
                <meta
                    name="description"
                    content="Homework help for students in grade 1-8"
                    key="desc"
                />
                <link rel="preload" href="/images/girl-desk.png" as="image" />
                <link rel="dns-prefetch" href="//fonts.googleapis.com" />
                <link rel="dns-prefetch" href="//fonts.gstatic.com" />
                <CrowdinScripts />
                <script type="application/ld+json">
                    {`
            {
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "Odevly",
              "url": "https://odevly.com/",
              "logo": "https://odevly.com/favicon-96x96.png",
              "sameAs": [
                "https://www.facebook.com/odevly",
                "https://twitter.com/odevly"
              ]
            }
          `}
                </script>
            </Head>
            <body>
                <Providers>
                    <div id="root">{children}</div>
                </Providers>
            </body>
        </html>
    );
}