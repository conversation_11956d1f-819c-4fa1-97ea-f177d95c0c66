import React from "react";
import type { Metada<PERSON> } from "next";
import "@/src/styles/globals.css";
import {LayoutProps} from "@/src/Types";

export const metadata: Metadata = {
    title: "Help | Odevly",
    description: "Help, terms of service, privacy policy and other important info for Odevly users.",
    robots: {
        index: true,
        follow: true,
    },

    openGraph: {
        title: "Help | Odevly",
        description: "Help and policy info for Odevly users.",
        type: "website",
    },
};

export default function LegalLayout({ children }: LayoutProps) {
    return <div>{children}</div>;
}