"use client";
import React from 'react';
import StaticPageNavbar from '@/src/Components/Navigation/NavbarLegal';
import TabsLegal from '@/src/Components/Navigation/TabsLegal';
import Footer from '@/src/Components/Navigation/Footer';
import styles from '@/src/styles/legal.module.css';
import { useTranslation } from '@/src/Utils/i18n';

const PrivacyPage: React.FC = () => {
    const {t} = useTranslation();
    return (
        <>
            <StaticPageNavbar/>
            <TabsLegal/>

            <div className={'mainContainer'}>
                <div className={styles.contentArea}>
                    <h1>{t('legal_privacy.title')}</h1>
                    <div className={styles.noteText}>{t('legal_privacy.note')}</div>

                    <div className={styles.contentWrapper}>
                        <h2>{t('legal_privacy.general_title')}</h2>
                        <p>{t('legal_privacy.general_1')}</p>
                        <p>{t('legal_privacy.general_2')}</p>

                        <h2>{t('legal_privacy.test_rules_title')}</h2>
                        <p>{t('legal_privacy.test_rules_1')}</p>
                        <p>{t('legal_privacy.test_rules_2')}</p>
                        <p>{t('legal_privacy.test_rules_3')}</p>
                        <p>{t('legal_privacy.test_rules_4')}</p>

                        <h2>{t('legal_privacy.expiration_title')}</h2>
                        <p>{t('legal_privacy.expiration_1')}</p>
                    </div>
                </div>
            </div>
            <Footer isLegalPage={true} t={t}/>
        </>
    );
};

export default PrivacyPage;