"use client";
import React from 'react';
import StaticPageNavbar from '@/src/Components/Navigation/NavbarLegal';
import TabsLegal from '@/src/Components/Navigation/TabsLegal';
import Footer from '@/src/Components/Navigation/Footer';
import styles from '@/src/styles/legal.module.css';
import { useTranslation } from '@/src/Utils/i18n';

const TermsPage: React.FC = () => {
  const {t} = useTranslation();
  return (
      <>
        <StaticPageNavbar />
        <TabsLegal />

        <div className={'mainContainer'}>
          <div className={styles.contentArea}>
            <h1>{t('legal_terms.title')}</h1>
            <div className={styles.noteText}>{t('legal_terms.note')}</div>

            <div className={styles.contentWrapper}>
                <h2>{t('legal_terms.general_title')}</h2>
                <p>{t('legal_terms.general_1')}</p>
                <p>{t('legal_terms.general_2')}</p>

                <h2>{t('legal_terms.test_rules_title')}</h2>
                <p>{t('legal_terms.test_rules_1')}</p>
                <p>{t('legal_terms.test_rules_2')}</p>
                <p>{t('legal_terms.test_rules_3')}</p>
                <p>{t('legal_terms.test_rules_4')}</p>

                <h2>{t('legal_terms.expiration_title')}</h2>
                <p>{t('legal_terms.expiration_1')}</p>
            </div>
          </div>
        </div>
        <Footer isLegalPage={true} t={t} />
      </>
  );
};

export default TermsPage;