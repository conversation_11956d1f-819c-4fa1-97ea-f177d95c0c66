"use client";
import React, {useState} from 'react';
import StaticPageNavbar from '@/src/Components/Navigation/NavbarLegal';
import TabsLegal from '@/src/Components/Navigation/TabsLegal';
import Footer from '@/src/Components/Navigation/Footer';
import styles from '@/src/styles/legal.module.css';
import { useTranslation } from '@/src/Utils/i18n';

type FaqItem = {
    question: string;
    answer: string;
    color?: string;
};

const HelpPage: React.FC = () => {
    const [openIndex, setOpenIndex] = useState<number | null>(null);
    const {t} = useTranslation();
    const toggle = (index: number) => {
        if (index === 0) return;

        setOpenIndex(openIndex === index ? null : index);
    };

    const helpContent: FaqItem[] = [
        {
            question: t('legal_help.q0'),
            answer: t('legal_help.a0'),
            color: '#45B0F6'
        },
        {
            question: t('legal_help.q1'),
            answer: t('legal_help.a1'),
        },
        {
            question: t('legal_help.q2'),
            answer: t('legal_help.a2'),
        },
        {
            question: t('legal_help.q3'),
            answer: t('legal_help.a3'),
        },
        {
            question: t('legal_help.q4'),
            answer: t('legal_help.a4'),
        },
        {
            question: t('legal_help.q5'),
            answer: t('legal_help.a5'),
        },
        {
            question: t('legal_help.q6'),
            answer: t('legal_help.a6'),
        },
    ];

    return (
        <>
            <StaticPageNavbar/>
            <TabsLegal/>

            <div className={'mainContainer'}>
                <div className={styles.contentArea}>

                    <h1 className={styles.textCenter}>Frequently Asked Questions</h1>

                    <div className={styles.contentWrapper} style={{paddingTop: '1px'}}>
                        <div className={styles.faqListing}>
                            {helpContent.map((item, index) => (
                                <div key={index} className={styles.faqItem}>
                                    {index === 0 ? (
                                        <div
                                            style={{color: item.color}}
                                            className={`${styles.faqQuestion} ${styles.faqQuesBold}`}
                                        >
                                            {item.question}
                                        </div>
                                    ) : (
                                        <>
                                            <div
                                                style={{color: item.color}}
                                                className={openIndex === index ? `${styles.faqQuestion} ${styles.faqQuesBold}` : styles.faqQuestion}
                                                onClick={() => toggle(index)}
                                            >
                                                {item.question}
                                                <img
                                                    src={'/icons/faq-static-toggle-icon.svg'}
                                                    alt={'toggle'}
                                                    className={`${styles.faqChevron} ${openIndex === index ? styles.faqChevronOpen : ''}`}
                                                />
                                            </div>
                                            {openIndex === index && item.answer && (
                                                <div className={styles.faqAnswer}>{item.answer}</div>
                                            )}
                                        </>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            <Footer isLegalPage={true} t={t}/>
        </>
    );
};

export default HelpPage;