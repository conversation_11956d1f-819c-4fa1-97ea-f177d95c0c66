import React from "react";
import type {Metada<PERSON>} from "next";
import "@/src/styles/globals.css";
import {LayoutProps} from "@/src/Types";

export const metadata: Metadata = {
    title: "Welcome",
    description: "Sign in to your Odevly account for 24/7 homework help from expert tutors.",
    robots: {
        index: false,
        follow: false,
        nocache: true,
    },
};

export default function AuthLayout({children}: LayoutProps) {
    return <div>{children}</div>;
}