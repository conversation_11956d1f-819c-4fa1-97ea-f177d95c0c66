"use client";
import React, {useContext, useEffect, useState} from "react";
import {useTranslation} from "@/src/Utils/i18n";
import styles from "@/src/styles/login.module.css";
import {useRouter} from "next/navigation";
import Spinner from "@/src/Components/Widgets/Spinner";
import {UserContext} from "@/src/Contexts/UserContext";
import TopBarWithLogo from "@/src/Components/Widgets/TopBarWithLogo";
import {validateEmail} from "@/src/Utils/helpers";
import {checkAccountExists, sendPasswordResetEmailFunc} from "@/src/Firebase/firebase.utils";
import {ToastContainer} from "react-toastify";
import {AppNotify} from "@/src/Utils/AppNotify";
import InputTextField from "@/src/Components/Form/InputTextField";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import BorderButton from "@/src/Components/Buttons/BorderButton";

const ForgotPassword = () => {
    const {t,locale} = useTranslation();
    const router = useRouter();
    const {user, fetching} = useContext(UserContext);
    const [email, setEmail] = useState("");
    const [emailHelper, setEmailHelper] = useState("");
    const [mailSent, setMailSent] = useState(false);

    useEffect(() => {
    });

    const handleSubmit = async (resend: boolean) => {
        if (email === "") {
            setEmailHelper(t('auth.email_required'));
            return null;
        }

        if (!validateEmail(email)) {
            setEmailHelper(t('auth.invalid_email'));
            return null;
        }

        const resp: any = await checkAccountExists(email);

        if (resp.status && resp.exists) {
            await sendPasswordResetEmailFunc(email);
            if (resend) {
                AppNotify(t('auth.password_reset_sent'), "success");
            }
            setMailSent(true);
        } else if (resp.status && !resp.exists) {
            AppNotify(t('auth.account_doesnt_exist'), "error");
            // eslint-disable-next-line no-useless-return
            return;
        }
        else if (!resp.status) {
            AppNotify(t('auth.error_occurred'), "error");
            // eslint-disable-next-line no-useless-return
            return;
        }
    };

    const forgotPasswordForm = () => {
        return (
            <>
                <div className={styles.formTitle}>{t('auth.forgot_password')}</div>
                <div className={styles.subtitle}>{t('auth.forgot_pass_text1')}</div>
                <div className={styles.formFieldsWrapper}>
                    <ToastContainer/>

                    <div className={`${styles.field} mt10`}>
                        <div className={styles.label}>{t('common.email')}</div>
                        <InputTextField
                            label={''}
                            icon={"envelope-icon"}
                            placeholder={t('auth.enter_your_email')}
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                        />
                        {emailHelper ? (
                            <div className={styles.helper}>
                                <img src="/icons/validate.svg" alt="validate"/>
                                {emailHelper}
                            </div>
                        ) : (
                            ""
                        )}
                    </div>

                    <div className={`${styles.formSubmit} ${styles.formForgotSubmit}`}>
                        <PrimaryButton
                            size={'greenBtn'}
                            disabled={!email}
                            onClick={() => handleSubmit(false)}
                            label={t('auth.reset_password')}
                        />

                        <BorderButton
                            type={'green'}
                            label={t('common.go_back')}
                            onClick={() => router.push(`/${locale}/login`)}
                        />
                    </div>
                </div>
            </>
        );
    };

    const checkYourMailContent = () => {
        return (
            <div>
                <ToastContainer/>
                <div className={styles.heading}>{t('auth.forgot_pass_text2')}</div>

                <div className={styles.forgotSentHeader}>
                    <div>{t('auth.forgot_pass_text3')}</div>
                    <span>{email}</span>
                </div>

                <div className={styles.formHeader}>
                    <div className={`${styles.didNtReceive} mt20`}>
                        {t('auth.forgot_pass_text5')}
                    </div>
                </div>

                <div className={styles.formSubmit}>
                    <PrimaryButton
                        size={'greenBtn'}
                        onClick={() => handleSubmit(true)}
                        label={t('auth.forgot_pass_text6')}
                    />
                </div>
            </div>
        );
    };

    return (
        <div className={styles.loginPage}>
            <img className={styles.loginTopImage} src={'/images/login-body-bg.svg'} alt={'login-body-bg'}/>
            <img className={`${styles.loginTopImage} ${styles.loginTopMobile}`} src={'/images/login-body-mobile-bg.svg'}
                 alt={'login-body-bg'}/>
            <div className="mainContainer">
                <TopBarWithLogo
                    buttonLabel={'Register as a Tutor'}
                    buttonUrl={`/${locale}/signup`}
                />
                <div className={styles.loginSection}>
                    <div className={styles.loginFormSection} style={{paddingBottom: '25px'}}>
                        {fetching ? (
                            <Spinner/>
                        ) : (
                            <>
                                {!fetching && user.id === ""
                                    ? mailSent
                                        ? checkYourMailContent()
                                        : forgotPasswordForm()
                                    : null}
                            </>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;
