"use client";
import React from 'react';
import Footer from '@/src/Components/Navigation/Footer';
import styles from '@/src/styles/public.module.css';
import Navbar from "@/src/Components/Navigation/Navbar";
import { useTranslation } from '@/src/Utils/i18n';
import PricingSection from '@/src/Components/Homepage/sections/PricingSection';

const BecomeStudent: React.FC = () =>
{
    const { t } = useTranslation();

    return (
        <>
            <Navbar isPublicPage={true} />

            <div className={'mainContainer'}>
                <div className={styles.pageHeader}>
                     <h1 className={styles.headTitle}>Become A Student</h1>
                    <div className={styles.headText}>Need help with homework or a tricky question? Our friendly tutors are here to guide you.<br/> Get clear answers and learn something new every time. Just ask, and we’ll help you figure it out!</div>
                </div>

                <div className={styles.pageContent}>
                    <PricingSection onlyPlans={true} t={t} />
                </div>
            </div>
            <Footer isPublicPage={true} t={t} />
        </>
    );
};

export default BecomeStudent;