"use client";
import React, {useContext, useState} from "react";
import {useRouter} from "next/navigation";
import Link from "next/link";
import Spinner from "@/src/Components/Widgets/Spinner";
import {UserContext} from "@/src/Contexts/UserContext";
import styles from "@/src/styles/login.module.css";
import TopBarWithLogo from "@/src/Components/Widgets/TopBarWithLogo";
import {useTranslation} from "@/src/Utils/i18n";
import PasswordField from "@/src/Components/Form/PasswordField";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import {EmailAuthProvider, getAuth, reauthenticateWithCredential, updatePassword} from "firebase/auth";
import {AppNotify} from "@/src/Utils/AppNotify";

const ResetPassword = () => {
    const {t} = useTranslation();
    const router = useRouter();
    const {user, fetching} = useContext(UserContext);
    const [password, setPassword] = useState("");
    const [passwordHelper, setPasswordHelper] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [confirmPasswordHelper, setConfirmPasswordHelper] = useState("");
    const [resetConfirm, setResetConfirm] = useState(false);
    const [currentPassword, setCurrentPassword] = useState("");
    const [currentPasswordHelper, setCurrentPasswordHelper] = useState("");

    // Function to change password
    const handleChangePassword = async (currentPassword, newPassword) => {
        const auth = getAuth();
        const currentUser = auth.currentUser;

        if (!currentUser || !currentUser.email) {
            AppNotify(t('auth.must_be_signed_in'), "error");
            return false;
        }

        try {
            const credential = EmailAuthProvider.credential(currentUser.email, currentPassword);
            await reauthenticateWithCredential(currentUser, credential);

            await updatePassword(currentUser, newPassword);
            AppNotify(t('auth.password_updated_successfully'), "success");
            return true;
        } catch (error) {
            AppNotify((error as Error).message || t('auth.failed_to_update_password'), "error");

            return false;
        }
    };

    const handleSubmit = async () => {
        setPasswordHelper("");
        setConfirmPasswordHelper("");
        setCurrentPasswordHelper("");

        // Validate current password
        if (currentPassword === "") {
            setCurrentPasswordHelper(t('auth.old_password_required'));
            return null;
        }

        // Validate new password
        if (password === "") {
            setPasswordHelper(t('auth.password_required'));
            return null;
        }

        if (password.length < 8) {
            setPasswordHelper(t('auth.password_too_short'));
            return null;
        }

        // Validate confirmation password
        if (confirmPassword === "") {
            setConfirmPasswordHelper(t('auth.password_required'));
            return null;
        }

        if (confirmPassword.length < 8) {
            setConfirmPasswordHelper(t('auth.password_too_short'));
            return null;
        }

        if (password !== confirmPassword) {
            setConfirmPasswordHelper(t('auth.passwords_dont_match'));
            return null;
        }

        // Attempt to change password
        const success = await handleChangePassword(currentPassword, password);
        if (success) {
            setResetConfirm(true);
        }
    };

    const handleGotoLogin = () => {
        router.push("/login");
    };

    const resetPasswordForm = () => {
        return (
            <div>
                <div className={styles.formHeader}>
                    <div className={styles.heading}>{t('auth.create_new_password')}</div>
                    <div className={styles.subtitle}>
                        {t('auth.password_different')}
                    </div>
                </div>

                <div className={`${styles.field} mt10`}>
                    <div className={styles.label}>{t('auth.old_password')}</div>
                    <PasswordField
                        showIcon={true}
                        placeholder={t('auth.enter_old_password')}
                        value={currentPassword}
                        onChange={(e) => setCurrentPassword(e.target.value)}
                        fullWidth
                    />
                    {currentPasswordHelper ? (
                        <div className={styles.helper}>
                            <img src="/icons/validate.svg" alt="validate"/>
                            {currentPasswordHelper}
                        </div>
                    ) : (
                        ""
                    )}
                </div>

                <div className={`${styles.field} mt10`}>
                    <div className={styles.label}>{t('auth.new_password')}</div>
                    <PasswordField
                        showIcon={true}
                        placeholder={t('auth.enter_new_password')}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        fullWidth
                    />
                    {passwordHelper ? (
                        <div className={styles.helper}>
                            <img src="/icons/validate.svg" alt="validate"/>
                            {passwordHelper}
                        </div>
                    ) : (
                        ""
                    )}
                </div>

                <div className={`${styles.field}`}>
                    <div className={styles.label}>{t('auth.confirm_password')}</div>
                    <PasswordField
                        showIcon={true}
                        placeholder={t('auth.enter_confirm_password')}
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        fullWidth
                    />
                    {confirmPasswordHelper ? (
                        <div className={styles.helper}>
                            <img src="/icons/validate.svg" alt="validate"/>
                            {confirmPasswordHelper}
                        </div>
                    ) : (
                        ""
                    )}
                </div>

                <div className={styles.formSubmit}>
                    <PrimaryButton
                        size={'greenBtn'}
                        disabled={!(currentPassword && password && confirmPassword)}
                        onClick={() => handleSubmit()}
                        label={t('auth.reset_password')}
                    />
                    <Link
                        className={styles.backToLogin}
                        href={"/login"}
                        passHref={true}
                    >
                        {t('auth.back_to_login')}
                    </Link>
                </div>
            </div>
        );
    };

    const resetConfirmContent = () => {
        return (
            <div>
                <div className={styles.formHeader}>
                    <div className={styles.heading}>{t('auth.password_reset')}</div>
                    <div className={styles.subtitle}>
                        {t('auth.reset_pass_text1')}
                        <br/>
                        {t('auth.reset_pass_text2')}
                    </div>
                </div>

                <div className={styles.formSubmit}>
                    <PrimaryButton
                        size={'greenBtn'}
                        onClick={() => handleGotoLogin()}
                        label={t('auth.login')}
                    />
                </div>
            </div>
        );
    };

    return (
        <div className={styles.loginPage}>
            <img className={styles.loginTopImage} src={'/images/login-body-bg.svg'} alt={'login-body-bg'}/>
            <img className={`${styles.loginTopImage} ${styles.loginTopMobile}`} src={'/images/login-body-mobile-bg.svg'}
                 alt={'login-body-bg'}/>
            <div className="mainContainer">
                <TopBarWithLogo
                    buttonLabel={'Login'}
                    buttonUrl={'/login'}
                />
                <div className={styles.loginSection}>
                    <div className={styles.loginFormSection} style={{paddingBottom: '25px'}}>
                        {fetching ? (
                            <Spinner/>
                        ) : (
                            <>
                                {!fetching && user.id === ""
                                    ? resetConfirm
                                        ? resetConfirmContent()
                                        : resetPasswordForm()
                                    : null}
                            </>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ResetPassword;