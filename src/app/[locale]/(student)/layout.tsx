'use client';

import React, {useCallback, useContext, useEffect, useMemo, useState} from "react";
import {UserContext} from "@/src/Contexts/UserContext";
import styles from "@/src/styles/home.module.css";
import {usePathname, useRouter} from "next/navigation";
import Spinner from "@/src/Components/Widgets/Spinner";
import Navbar from "@/src/Components/Navigation/Navbar";
import StudentTabs from "@/src/Components/Navigation/TabsStudent";
import Footer from "@/src/Components/Navigation/Footer";
import {isStudentAuthUser} from "@/src/Firebase/firebase.utils";
import "@/src/styles/globals.css";
import {STUDENT_PATH_TO_TAB_INDEX, STUDENT_PATHS} from "@/src/Constants/tabNavigation";
import { Box } from "@mui/material";
import { useTranslation } from "@/src/Utils/i18n";

interface StudentDashboardLayoutProps {
    children: React.ReactNode;
}

export default function StudentDashboardLayout({children}: StudentDashboardLayoutProps) {
    const {user, fetching, initialLoad, isLoggingOut} = useContext(UserContext);
    const {t} = useTranslation();
    const [activeTab, setActiveTab] = useState(0);
    const [isChecking, setChecking] = useState(true);
    const [isPaidUser, setIsPaidUser] = useState(false);

    const router = useRouter();
    const pathname = usePathname();

    const protectedStudentPaths = useMemo(() =>
            STUDENT_PATHS.filter(path => path !== '/'),
        []
    );

    const setActiveTabCallback = useCallback((tab: React.SetStateAction<number>) => {
        setActiveTab(tab);
    }, []);

    const shouldShowSpinner = initialLoad || (fetching && !user.id) || isLoggingOut;

    useEffect(() => {
        if (sessionStorage.getItem('reloadOnBack') === 'true') {
            sessionStorage.removeItem('reloadOnBack');
            router.refresh();
        }
    }, [router]);

    useEffect(() => {
        if (initialLoad || fetching || isLoggingOut || !protectedStudentPaths.includes(pathname)) return;

        if (!user.id) {
            router.push("/login");
            return;
        }

        if (user.type !== "user") {
            console.log('🔒 Redirecting non-student user');
            const redirectMap = {
                tutor: "/",
                admin: "/admin/dashboard"
            };
            router.push(redirectMap[user.type] || "/login");
            // eslint-disable-next-line no-useless-return
            return;
        }

    }, [user, fetching, initialLoad, isLoggingOut, router, pathname, protectedStudentPaths]);

    useEffect(() => {
        if (initialLoad || isLoggingOut || !user.id || user.type !== 'user') {
            if (!initialLoad && !isLoggingOut) {
                setChecking(false);
            }
            return;
        }

        const checkPaidStatus = async () => {
            try {
                const isPaid = await isStudentAuthUser(user.id);
                setIsPaidUser(isPaid);

                if (!isPaid && protectedStudentPaths.includes(pathname)) {
                    router.push("/payment");
                } else {
                    setChecking(false);
                }
            } catch (error) {
                setChecking(false);
            }
        };

        checkPaidStatus();

        if (user.type) {
            localStorage.setItem("HMWK_DEFAULT_USER_TYPE", user.type);
        }
    }, [user, pathname, router, protectedStudentPaths, initialLoad, isLoggingOut]);

    useEffect(() => {
        const tabIndex = STUDENT_PATH_TO_TAB_INDEX[pathname];
        if (tabIndex !== undefined) {
            setActiveTab(tabIndex);
        }
    }, [pathname]);

    const remQuestions = useMemo(() => user?.questions || 0, [user?.questions]);

    if (shouldShowSpinner || (isChecking && user?.type === 'user' && !initialLoad && !isLoggingOut)) {
        return (
            <div className={styles.page}>
                <div className={styles.pageTopSpace}>
                    <Spinner/>
                </div>
            </div>
        );
    }

    return (
        <div className={styles.page}>
            <Box sx={{ py: 10 }}>
            {!shouldShowSpinner && (
                <>
                    <Navbar
                        logo="/images/logo.svg"
                        id="en"
                        active={activeTab}
                        setActiveTab={setActiveTabCallback}
                        isPaidUser={isPaidUser}
                        isPublicPage={false}
                    />
                    <StudentTabs
                        active={activeTab}
                        setActiveTab={setActiveTabCallback}
                        remQuestions={remQuestions}
                    />
                </>
            )}

            <div className={styles.pageTopSpace}>
                {shouldShowSpinner ? <Spinner/> : children}
            </div>

            </Box>
            {!shouldShowSpinner && user?.id && (
                <Footer
                    isPublicPage={false}
                    setActiveTab={setActiveTabCallback}
                    userType={user.type || ''}
                    t={t}
                />
            )}
        </div>
    );
}
