'use client';

import React, {useCallback, useContext, useEffect, useMemo, useState} from "react";
import UserProvider, {UserContext} from "@/src/Contexts/UserContext";
import styles from "@/src/styles/home.module.css";
import {usePathname} from "next/navigation";
import Navbar from "@/src/Components/Navigation/Navbar";
import StudentTabs from "@/src/Components/Navigation/TabsStudent";
import Footer from "@/src/Components/Navigation/Footer";
import TutorTabs from "@/src/Components/Navigation/TabsTutor";
import {isStudentAuthUser} from "@/src/Firebase/firebase.utils";
import Spinner from "@/src/Components/Widgets/Spinner";
import "../../../styles/globals.css";
import ThemeClient from "@/src/Components/ThemeClient";

interface HomeLayoutProps {
    children: React.ReactNode;
    params: { locale: string };
}

function RootPageWrapper({children, params}: { children: React.ReactNode; params: { locale: string } }) {
    const {user, fetching, initialLoad, isLoggingOut} = useContext(UserContext);
    const [activeTab, setActiveTab] = useState(0);
    const [isPaidUser, setIsPaidUser] = useState(false);
    const [isClient, setIsClient] = useState(false);
    const pathname = usePathname();

    // Handle client-side mounting
    useEffect(() => {
        setIsClient(true);
    }, []);

    const setActiveTabCallback = useCallback((tab: React.SetStateAction<number>) => {
        if (typeof tab === 'function') {
            setActiveTab(prev => tab(prev));
        } else {
            setActiveTab(tab);
        }
    }, []);

    const shouldShowSpinner = initialLoad || (fetching && !user.id) || isLoggingOut;

    useEffect(() => {
        if (isClient && user.id !== "" && user.type === 'user' && !initialLoad && !isLoggingOut) {
            const checkPaidStatus = async () => {
                const value = await isStudentAuthUser(user.id);
                setIsPaidUser(value);
            };
            checkPaidStatus();
        }
    }, [user, initialLoad, isLoggingOut, isClient]);

    const remQuestions = useMemo(() => user?.questions || 0, [user?.questions]);

    if (!isClient || (pathname === '/' && shouldShowSpinner)) {
        return (
            <div className={styles.page}>
                <div className={styles.pageTopSpace}>
                    <Spinner/>
                </div>
            </div>
        );
    }

    if (pathname === '/' && user.id !== "" && user.type !== 'admin' && !fetching && !isLoggingOut) {
        const childrenWithProps = React.Children.map(children, (child) => {
            if (React.isValidElement(child)) {
                return React.cloneElement(child as React.ReactElement<any>, {
                    setActiveTab: setActiveTabCallback,
                    params // Pass params to children
                });
            }
            return child;
        });

        return (
            <div className={styles.page}>
                <div>
                    <Navbar
                        logo="/images/logo.svg"
                        active={activeTab}
                        setActiveTab={setActiveTabCallback}
                        isPaidUser={isPaidUser}
                        isPublicPage={false}
                    />
                    {user?.type === "user" && (
                        <StudentTabs
                            active={activeTab}
                            setActiveTab={setActiveTabCallback}
                            remQuestions={remQuestions}
                        />
                    )}
                    {user?.type === "tutor" && (
                        <TutorTabs
                            user={user}
                            active={activeTab}
                            setActiveTab={setActiveTabCallback}
                        />
                    )}

                    <div className={styles.pageTopSpace}>
                        {childrenWithProps}
                    </div>

                    <Footer
                        isPublicPage={false}
                        setActiveTab={setActiveTabCallback}
                        userType={user?.type || ''}
                        t={(key: string) => key} 
                    />
                </div>
            </div>
        );
    }

    return children;
}

const Providers = React.memo(({children, params}: { children: React.ReactNode; params: { locale: string } }) => (
    <ThemeClient>
        <UserProvider>
            <RootPageWrapper params={params}>
                {children}
            </RootPageWrapper>
        </UserProvider>
    </ThemeClient>
));

Providers.displayName = 'HomeProviders';

export default function HomeLayout({children, params}: HomeLayoutProps) {
    return <Providers params={params}>{children}</Providers>;
}