"use client";


import React, { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { Box, Typography } from "@mui/material";
import Navbar from "@/src/Components/Navigation/Navbar";
import Spinner from "@/src/Components/Widgets/Spinner";
import { UserContext } from "@/src/Contexts/UserContext";
import styles from "@/src/styles/home.module.css";
import "@/src/styles/globals.css";
import Footer from "@/src/Components/Navigation/Footer";
import AdminTabs from "@/src/Components/Navigation/TabsAdmin";

import { I18nProvider, Locale, useTranslation as useLocale, useTranslation,  } from "@/src/Utils/i18n";
export const dynamic = "force-dynamic";


interface TabPanelProps {
    children?: React.ReactNode;
}

const TabPanel = React.memo(({ children }: TabPanelProps) => {
    return (
        <Box>
            <Typography>{children}</Typography>
        </Box>
    );
});

TabPanel.displayName = 'TabPanel';

interface AdminLayoutProps {
    children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
    const { user, fetching, initialLoad, isLoggingOut } = useContext(UserContext);
    const {t} = useTranslation();
    const router = useRouter();
    const pathname = usePathname();

    const pathsToNotCheck = useMemo(() => ["/login", "/signup", "/"], []);
    const { locale } = useLocale();
    const tabsPaths = useMemo(() => [
        `/${locale}/admin/dashboard`,
        `/${locale}/admin/users`,
        `/${locale}/admin/applicant-questions`,
        `/${locale}/admin/user-questions`,
        `/${locale}/admin/settings`,
        `/${locale}/admin/history`,
    ], [locale]);

    // States
    const [authUser, setAuthUser] = useState(false);
    const [selectedTab, setSelectedTab] = useState(0);

    const setActiveTab = useCallback((tab: number) => {
        setSelectedTab(tab);
    }, []);

    const shouldShowSpinner = initialLoad || (fetching && !user.id) || isLoggingOut;

    useEffect(() => {
        if (initialLoad || fetching || isLoggingOut) return;

        if (user.id === "" && !pathsToNotCheck.includes(pathname)) {
            router.push("/login");
        } else if (user.id !== "" && user.type !== "admin") {
            router.push("/");
        } else if (user.id !== "" && user.type === "admin") {
            setAuthUser(true);
        }
    }, [user, fetching, initialLoad, isLoggingOut, pathname, pathsToNotCheck, router]);

    useEffect(() => {
        if (pathname) {
            const tabIndex = tabsPaths.indexOf(pathname);
            setSelectedTab(tabIndex);
        }
    }, [pathname, tabsPaths]);

    const userType = useMemo(() => user?.type || '', [user?.type]);

    if (shouldShowSpinner) {
        return <Spinner />;
    }

    return authUser ? (
        <I18nProvider defaultLocale={locale as Locale}>
            <Box sx={{ py: 10 }}>
                <Navbar
                    isAdmin={true}
                    active={selectedTab}
                    setActiveTab={setActiveTab}
                />
                <AdminTabs />
                <div className="containerWidth adminContainer">
                    <Box textAlign={{ xs: "left", md: "left" }}>
                        <div className={styles.pageTopSpace}>
                            <TabPanel>{children}</TabPanel>
                        </div>
                        <Footer
                            isPublicPage={true}
                            userType={userType}
                            t={t}
                        />
                    </Box>
                </div>
            </Box>
        </I18nProvider>
    ) : null;
}