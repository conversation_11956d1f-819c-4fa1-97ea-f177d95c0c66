"use client";
import React, {useEffect, useState} from "react";
import Accordion from "@mui/material/Accordion";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import styles from "@/src/styles/questionsBank.module.css";
import AddQuestion from "@/src/Components/Admin/AddQuestion";
import {deleteDocument, getAllDocs, listenToDocument} from "@/src/Firebase/firebase.utils";
import PrimaryButton from "@/src/Components/Buttons/PrimaryButton";
import TableCustomPagination from "@/src/Components/Table/TableCustomPagination";
import {zeroPadId} from "@/src/Utils/helpers";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import CancelButton from "@/src/Components/Buttons/CancelButton";
import SetPriceModal from "@/src/Components/Modals/SetPriceModal";
import {accordionStyle} from "@/src/Utils/styles";
import BorderButton from "@/src/Components/Buttons/BorderButton";
import NotificationModal from "@/src/Components/Modals/NotificationModal";
import { useTranslation } from "@/src/Utils/i18n";

const QuestionBankPage = () => {
    const { t } = useTranslation();

    // States
    const [first, setFirst] = useState(true);
    const [page, setPage] = React.useState(0);
    const [questions, setQuestions] = useState([]);
    const [currentQuestion, setCurrentQuestion] = useState({});
    const [edit, setEdit] = useState(false);
    const [rowsPerPage, setRowsPerPage] = React.useState(10);
    const [addQuestionActive, setAddQuestionActive] = useState(false);
    const [openPriceModal, setOpenPriceModal] = useState(false);
    const [expanded, setExpanded] = useState<string | null>(null);
    const [adminSettings, setAdminSettings] = useState<any>([]);
    const [openDeleteQuesModal, setOpenDeleteQuesModal] = useState(false);
    const [questionToDelete, setQuestionToDelete] = useState<any>(null);

    useEffect(() => {
        if (first) {
            getQuestions();
            getAdminSettings();
            setFirst(false);
        }
    }, [first]);

    const getAdminSettings = async () => {
        await listenToDocument('test_question_pay', "admin_config", setAdminSettings);
    };

    const getQuestions = async () => {
        const resp: any = await getAllDocs("applicant_questions");
        if (resp.status && resp.fullData.length > 0) {
            const arr: any = [];
            resp.fullData.forEach((question: any) => {
                arr.push(question);
            });
            setQuestions(arr);
        }
    };

    const handleDeleteQuestion = async () => {
        if (!questionToDelete) {
            console.log("No question to delete");
            return;
        }

        try {
            const response = await deleteDocument("applicant_questions", questionToDelete.docId);
            if (response.status) {
                getQuestions();
                setOpenDeleteQuesModal(false);
                setQuestionToDelete(null);
            }
        } catch (error) {
            console.error("Error deleting question:", error);
        }
    };

    const handleModalAction = () => {
        handleDeleteQuestion();
    };

    let filteredQuestions = questions;
    const questionsToShow = filteredQuestions;

    return (
        <div className={styles.questionsWrapper} style={{padding: '0 15px'}}>
            <div className={styles.questionTitle}>
                {addQuestionActive ? edit ? t('admin_applicant_questions.title_edit') : t('admin_applicant_questions.title_add') : t('admin_applicant_questions.title_main')}
            </div>
            {!addQuestionActive && <div
                className={styles.totalQues}>{t(filteredQuestions.length === 1 ? 'admin_applicant_questions.total_question' : 'admin_applicant_questions.total_questions')}: <span>{filteredQuestions.length}</span>
            </div>}
            {addQuestionActive ? (
                <AddQuestion
                    totalQuestions={questions.length}
                    setAddQuestionActive={setAddQuestionActive}
                    getQuestions={getQuestions}
                    edit={edit}
                    currentQuestion={currentQuestion}
                />
            ) : (
                <div>
                    <>
                        <div className={styles.addNewQuesBox}>
                            <BorderButton
                                label={t('admin_applicant_questions.set_price')}
                                type={'dark'}
                                onClick={() => setOpenPriceModal(true)}
                            />
                            <PrimaryButton
                                label={t('admin_applicant_questions.add_a_question')}
                                size={'greenBtn'}
                                icon={'plus-white-icon.svg'}
                                onClick={() => {
                                    setAddQuestionActive(true);
                                    setEdit(false);
                                }}
                            />
                        </div>

                        <div className={styles.questionsList} style={{marginTop: '12px'}}>
                            {questionsToShow && questionsToShow.length ? (
                                questionsToShow.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                    .map((question: any, index: number) => {
                                        return (
                                            <Accordion
                                                classes={{root: "quesAccordion"}}
                                                key={index}
                                                sx={accordionStyle}
                                                expanded={expanded === question.docId}
                                            >
                                                <AccordionSummary
                                                    expandIcon={
                                                        <ExpandMoreIcon
                                                            sx={{ color: "#335CFF", width: { xs: "16px", md: "22px" } }}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                setExpanded((prev) => (prev === question.docId ? null : question.docId));
                                                            }}
                                                        />
                                                    }
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        setExpanded((prev) => prev === question.docId ? null : question.docId);
                                                    }}
                                                    aria-controls="panel1-content"
                                                    id="panel1-header"
                                                    sx={{
                                                        color: "#335CFF",
                                                        width: { xs: "50%", md: "100%" },
                                                        minHeight: 'inherit !important',
                                                        padding: { xs: "10px", md: "16px" },
                                                        "& .MuiAccordionSummary-content": {
                                                            margin: "0 !important",
                                                        },
                                                    }}
                                                >
                                                    <div className={questionStyles.accQuesTop}
                                                        style={{ flexDirection: 'column' }}>
                                                        <span
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                setExpanded((prev) => prev === question.docId ? null : question.docId);
                                                            }}
                                                            className={questionStyles.accExpText}
                                                            style={{ marginRight: '25px' }}
                                                        >
                                                            {expanded === question.docId ? t('admin_applicant_questions.collapse') : t('admin_applicant_questions.expand')}
                                                        </span>

                                                        <div className={questionStyles.accQuesId}>{t('admin_applicant_questions.question_id')}
                                                            #{question.guid && zeroPadId(question.guid)}</div>
                                                        <div
                                                            style={{
                                                                fontSize: "14px",
                                                                maxWidth: "100%",
                                                                fontWeight: "500",
                                                                color: "#0E121B",
                                                                marginTop: '3px'
                                                            }}
                                                        >
                                                            {question.question}
                                                        </div>
                                                    </div>
                                                </AccordionSummary>

                                                <AccordionDetails sx={{ padding: '18px 0 0 0 !important' }}>
                                                    <div className={questionStyles.accQuesAnswerBox} style={{ padding: '15px' }}>
                                                        <div className={questionStyles.accAnswerLabel}>{t('admin_applicant_questions.answer')}:</div>
                                                        <div className={questionStyles.accAnswerData}>
                                                            <div className={styles.quesOptions}>
                                                                <div className={styles.quesOption}>
                                                                    <span>A)</span> {question.answer1}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className={questionStyles.accAnswerLabel}
                                                            style={{ marginTop: 5 }}>{t('admin_applicant_questions.correct_answer')}:
                                                        </div>
                                                        <div className={questionStyles.accAnswerData}>
                                                            <div className={styles.quesOptions} style={{ paddingBottom: 0 }}>
                                                                <div className={styles.quesOption}>
                                                                    <span>{question.correctAnswer})</span> {question[`answer${question.correctAnswer.toLowerCase()}`]}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className={styles.quesAnswerBox}>
                                                            <div className={styles.quesAnswerActions}
                                                                style={{
                                                                    width: '100%',
                                                                    display: 'flex',
                                                                    justifyContent: 'end',
                                                                    gap: '10px'
                                                                }}>
                                                                <CancelButton
                                                                    onClick={() => {
                                                                        setQuestionToDelete(question);
                                                                        setOpenDeleteQuesModal(true);
                                                                    }}
                                                                    label={t('admin_applicant_questions.delete_question')}
                                                                    style={{ width: '140px' }}
                                                                />
                                                                <CancelButton
                                                                    onClick={() => {
                                                                        setCurrentQuestion(question);
                                                                        setAddQuestionActive(true);
                                                                        setEdit(true);
                                                                    }}
                                                                    label={t('admin_applicant_questions.edit_question')}
                                                                    style={{ width: '130px' }}
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </AccordionDetails>
                                            </Accordion>
                                        );
                                    })
                            ) : null}
                        </div>
                        <TableCustomPagination
                            filteredRowsLength={questionsToShow.length}
                            page={page}
                            rowsPerPage={rowsPerPage}
                            setPage={setPage}
                            setRowsPerPage={setRowsPerPage}
                        />
                    </>
                </div>
            )}

            {openPriceModal ?
                <SetPriceModal
                    open={openPriceModal}
                    title={t('admin_applicant_questions.set_price_title')}
                    actionLabel={t('admin_applicant_questions.set_price_action')}
                    defaultPrice={adminSettings && adminSettings.data && adminSettings.data.amount ? adminSettings.data.amount : 0}
                    handleClose={() => setOpenPriceModal(false)}
                /> : ''
            }

            <NotificationModal
                isDelete={true}
                open={openDeleteQuesModal}
                handleClose={() => setOpenDeleteQuesModal(false)}
                title={t('admin_applicant_questions.delete_confirm_title')}
                content={t('admin_applicant_questions.delete_confirm_content')}
                cancelLabel={t('admin_applicant_questions.delete_cancel')}
                actionLabel={t('admin_applicant_questions.delete_confirm_action')}
                onConfirm={handleModalAction}
            />
        </div>
    );
};

export default QuestionBankPage;