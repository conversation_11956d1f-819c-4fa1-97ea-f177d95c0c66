"use client";
import React, {useContext, useEffect, useState} from "react";
import {UserContext} from "@/src/Contexts/UserContext";
import styles from "@/src/styles/home.module.css";
import questionStyles from "@/src/styles/addUserQuestions.module.css";
import {usePathname, useSearchParams} from "next/navigation";
import Spinner from "@/src/Components/Widgets/Spinner";
import {Accordion, AccordionDetails, AccordionSummary, Typography} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import {getAllDocs, queryData} from "@/src/Firebase/firebase.utils";
import NotificationModal from "@/src/Components/Modals/NotificationModal";
import {zeroPadId} from "@/src/Utils/helpers";
import UserProfile from "@/src/Components/Admin/UserProfile";
import CancelButton from "@/src/Components/Buttons/CancelButton";
import moment from "moment/moment";
import {useImageModal} from "@/src/Contexts/ImageModalContext";
import { useTranslation } from "@/src/Utils/i18n";

interface Question {
    docId: string;
    id: string;
    imageUrl: string;
    question: string;
    userId: string;
    name: string;
    email: string;
    date: number;
    isAnswered: boolean;
    answeredByUsername?: string;
    answeredUserId?: string;
}

export default function HistoryPage() {
    const { t } = useTranslation();
    const {openImage} = useImageModal();
    const pathname = usePathname();
    const isAdminPage = pathname?.includes('/admin');
    const {user, langSwitch} = useContext(UserContext);
    const [open, setOpen] = useState(false);
    const [openDeleteQuesModal, setOpenDeleteQuesModal] = useState(false);
    const [questions, setQuestions] = useState<Question[]>([]);
    const [questionStatus, setQuestionStatus] = useState("answered");
    const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
    const [fetchingAnswer, setFetchingAnswer] = useState(false);
    const [fetchingQuestions, setFetchingQuestions] = useState(false);
    const [answerImage, setAnswerImage] = useState<any>(null);
    const [expanded, setExpanded] = useState<string | null>(null);
    const [answer, setAnswer] = useState("");

    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    const searchParams = useSearchParams();
    const quesId = searchParams.get("id");

    useEffect(() => {
        if (user.id !== "") {
            getQuestions();
            setQuestionStatus('answered');
        }
    }, [user]);

    useEffect(() => {
        getQuestions();
    }, [questionStatus]);

    const getQuestions = async () => {
        setFetchingQuestions(true);
        let resp;
        if (isAdminPage) {
            resp = await getAllDocs("users_questions");
        } else {
            resp = await queryData("users_questions", "userId", user.id);
        }

        setFetchingQuestions(false);
        if (resp.status && resp.fullData.length > 0) {
            setQuestions(resp.fullData);
        }
    };

    const handleOpenAnswer = async (question: Question) => {
        if (!question.isAnswered) {
            return;
        }
        setFetchingAnswer(true);
        const resp: any = await queryData("tutor_answers", "questionId", question.docId);
        setFetchingAnswer(false);
        if (resp.status && resp.fullData.length > 0) {
            setAnswer(resp.fullData[0].answer);
            setAnswerImage(resp.fullData[0].imgUrl || null);
        }
    };

    const currentQuestions = quesId ? questions.filter(item => item.docId === quesId) : questions;

    if (selectedUserId) {
        return (
            <UserProfile
                selectedUserId={selectedUserId}
                backToTitle="Back to Questions"
                handleGotoBack={() => {
                    setSelectedUserId("");
                }}
            />
        );
    }

    return (
        <div className={styles.page}>
            <div className={styles.englishPage}>
                <div className={questionStyles.addQuestionTitle}>
                    <h1>{t('admin_history.title')}</h1>
                </div>
                {langSwitch || fetchingQuestions ? (
                    <Spinner/>
                ) : questions.length > 0 ? (
                    <div className={questionStyles.addQuestionBox} style={{paddingBottom: "40px", height: "auto"}}>
                        <div className={`${questionStyles.studentRoundBox}`}>
                            <div className={questionStyles.questions}>
                                {currentQuestions.map((question: Question) => {
                                    let quesDate = question && question.date ? moment(Number(question.date)).format("MM/DD/YYYY") : '';
                                    return (
                                        <Accordion
                                            key={question.docId}
                                            expanded={expanded === question.docId}
                                            sx={{
                                                maxWidth: "950px",
                                                width: "100%",
                                                border: "1px solid #E1E4EA !important",
                                                borderRadius: "16px !important",
                                                boxShadow: "none !important",
                                                "&::before": {
                                                    display: "none",
                                                },
                                            }}
                                        >
                                            <AccordionSummary
                                                expandIcon={
                                                    questionStatus === "answered" && (
                                                        <ExpandMoreIcon
                                                            sx={{
                                                                color: "#335CFF",
                                                                width: {xs: "16px", md: "22px"},
                                                            }}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleOpenAnswer(question);
                                                                setExpanded((prev) =>
                                                                    prev === question.docId
                                                                        ? null
                                                                        : question.docId
                                                                );
                                                            }}
                                                        />
                                                    )
                                                }
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                }}
                                                aria-controls="panel1-content"
                                                id="panel1-header"
                                                sx={{
                                                    padding: {xs: "10px", md: "16px"},
                                                    "& .MuiAccordionSummary-content": {
                                                        margin: "0 !important",
                                                    },
                                                }}
                                            >
                                                <div className={questionStyles.accordionContainer}>
                                                    <div className={questionStyles.imgQuestionContainer}>
                                                        {questionStatus === "answered" ? (
                                                            <span
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    handleOpenAnswer(question);
                                                                    setExpanded((prev) => prev === question.docId ? null : question.docId);
                                                                }}
                                                                className={questionStyles.accExpText}
                                                            >
                              {expanded === question.docId ? t('admin_history.collapse') : t('admin_history.expand')}
                            </span>
                                                        ) : (
                                                            ""
                                                        )}

                                                        <div className={questionStyles.accQuesDataBox}>
                                                            <img
                                                                src={question.imageUrl || "/images/placeholder.png"}
                                                                height={76}
                                                                width={76}
                                                                alt="Question"
                                                                onClick={() => openImage(question.imageUrl || "/images/placeholder.png")}
                                                            />

                                                            <div className={questionStyles.accQuesHeader}>
                                                                <div className={questionStyles.accQuesTop}>
                                                                    <div className={questionStyles.accQuesId}>
                                                                        {t('admin_history.question_id')}
                                                                        #{question.id && zeroPadId(question.id)}
                                                                    </div>
                                                                    <Typography
                                                                        className={questionStyles.accQuesTitle}
                                                                        variant="body2"
                                                                        sx={{
                                                                            fontSize: "14px",
                                                                            maxWidth: "100%",
                                                                            fontWeight: "500",
                                                                            color: "#0E121B",
                                                                        }}
                                                                    >
                                                                        {question.question}
                                                                    </Typography>
                                                                </div>

                                                                <div className={questionStyles.questionInfo}>
                                                                    {isAdminPage && (
                                                                        <>
                                    <span>
                                      {t('admin_history.asked_by')} {" "}
                                        <span
                                            className={questionStyles.linkName}
                                            onClick={() => {
                                                setSelectedUserId(question.userId);
                                            }}
                                        >
                                        {question.name && (question.name.trim() !== "") ? question.name : question.email}
                                      </span>
                                        {quesDate}
                                    </span>
                                                                            {question.answeredByUsername && (
                                                                                <span>
                                        {t('admin_history.answered_by')} {" "}
                                                                                    <span
                                                                                        className={questionStyles.linkName}
                                                                                        onClick={() => {
                                                                                            setSelectedUserId(question.answeredUserId ?? null);
                                                                                        }}
                                                                                    >
                                          {question.answeredByUsername}
                                        </span>
                                      </span>
                                                                            )}
                                                                        </>
                                                                    )}
                                                                    {!isAdminPage && (
                                                                        <span
                                                                            onClick={() => handleOpen()}>{t('admin_history.feedback')}</span>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </AccordionSummary>

                                            <AccordionDetails style={{paddingTop: 0}}>
                                                {fetchingAnswer ? (
                                                    <h4
                                                        style={{
                                                            fontWeight: 400,
                                                            margin: 0,
                                                            color: "#335CFF",
                                                            fontSize: "14px",
                                                        }}
                                                    >
                                                        {t('admin_history.loading')}
                                                    </h4>
                                                ) : (
                                                    <>
                                                        <div className={questionStyles.accQuesAnswerBox}>
                                                            <div className={questionStyles.accAnswerLabel}>
                                                                {t('admin_history.answer')}
                                                            </div>
                                                            <div className={questionStyles.accAnswerData}>
                                                                {answerImage && (
                                                                    <div className={questionStyles.accAnswerImage}>
                                                                        <img
                                                                            src={answerImage}
                                                                            height={76}
                                                                            width={76}
                                                                            alt="Answer"
                                                                            onClick={() => openImage(answerImage)}
                                                                        />
                                                                    </div>
                                                                )}

                                                                <div className={questionStyles.accAnswerText}>
                                                                    {answer}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        {isAdminPage && (
                                                            <div className={questionStyles.accQuesDelete}>
                                                                <CancelButton
                                                                    onClick={() => setOpenDeleteQuesModal(true)}
                                                                    label={t('admin_history.delete_question')}
                                                                />
                                                            </div>
                                                        )}
                                                    </>
                                                )}
                                            </AccordionDetails>
                                        </Accordion>
                                    )
                                })}
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className={questionStyles.addQuestionBox}>
                        <h3>{t('admin_history.no_questions')}</h3>
                    </div>
                )}
            </div>

            <NotificationModal
                open={open}
                handleClose={handleClose}
                icon="/icons/flag-icon.svg"
                title={t('admin_history.help_title')}
                content={t('admin_history.help_content')}
            />

            <NotificationModal
                isDelete={true}
                open={openDeleteQuesModal}
                handleClose={() => setOpenDeleteQuesModal(false)}
                title={t('admin_history.delete_confirm_title')}
                content={t('admin_history.delete_confirm_content')}
                cancelLabel={t('admin_history.delete_cancel')}
                actionLabel={t('admin_history.delete_confirm_action')}
            />
        </div>
    );
}