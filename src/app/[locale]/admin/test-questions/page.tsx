"use client";
import React, {useEffect, useState} from "react";
import {But<PERSON>} from "@mui/material";
import TablePagination from "@mui/material/TablePagination";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import styles from "@/src/styles/questionsBank.module.css";
import SearchBox from "@/src/Components/Widgets/SearchBox";
import AddQuestion from "@/src/Components/Admin/AddQuestion";
import {deleteDocument, getAllDocs} from "@/src/Firebase/firebase.utils";
import ConfirmModal from "@/src/Components/Modals/ConfirmModal";
import { useTranslation } from "@/src/Utils/i18n";

const ExpandMoreIcon = () => {
    return (
        <svg
            width="28"
            height="28"
            viewBox="0 0 28 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect width="28" height="28" rx="4" fill="#E3E8EF"/>
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.29289 11.2929C9.68342 10.9024 10.3166 10.9024 10.7071 11.2929L14 14.5858L17.2929 11.2929C17.6834 10.9024 18.3166 10.9024 18.7071 11.2929C19.0976 11.6834 19.0976 12.3166 18.7071 12.7071L14.7071 16.7071C14.3166 17.0976 13.6834 17.0976 13.2929 16.7071L9.29289 12.7071C8.90237 12.3166 8.90237 11.6834 9.29289 11.2929Z"
                fill="#3F3F3F"
            />
        </svg>
    );
};

const QuestionBankPage = () => {
    const { t } = useTranslation();
    // States
    const [first, setFirst] = useState(true);
    const [page, setPage] = React.useState(0);
    const [questions, setQuestions] = useState([]);
    const [currentQuestion, setCurrentQuestion] = useState<any>({});
    const [edit, setEdit] = useState(false);
    const [deleteQuestion, setDeleteQuestion] = useState(false);
    const [searchVal, setSearchVal] = useState("");
    const [rowsPerPage, setRowsPerPage] = React.useState(10);
    const [addQuestionActive, setAddQuestionActive] = useState(false);

    useEffect(() => {
        if (first) {
            getQuestions();
            setFirst(false);
        }
    }, [first]);

    const getQuestions = async () => {
        const resp: any = await getAllDocs("applicant_questions");
        if (resp.status && resp.fullData.length > 0) {
            const arr: any = [];
            resp.fullData.forEach((question: any) => {
                arr.push(question);
            });
            setQuestions(arr);
        } else {
            setQuestions([]);
        }
    };

    const handleChangePage = (event: unknown, newPage: number) => {
        setPage(newPage);
    };

    const deleteQuestionFunc = async (id) => {
        const resp = await deleteDocument("applicant_questions", id);
        if (resp.status) {
            getQuestions();
            setCurrentQuestion({});
            setDeleteQuestion(false);
        }
    };

    const handleChangeRowsPerPage = (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        setRowsPerPage(+event.target.value);
        setPage(0);
    };

    let filteredQuestions = questions;

    if (searchVal !== "") {
        filteredQuestions = filteredQuestions.filter((question: any) =>
            question.question.toLowerCase().includes(searchVal.toLowerCase())
        );
    }

    const questionsToShow = filteredQuestions;

    return (
        <div className={styles.questionsWrapper} style={{padding: '0 15px'}}>
            <div className={styles.questionTitle}>
                {addQuestionActive ? t('admin_test_questions.title_add') : t('admin_test_questions.title_main')}
            </div>
            {addQuestionActive ? (
                <AddQuestion
                    setAddQuestionActive={setAddQuestionActive}
                    getQuestions={getQuestions}
                    edit={edit}
                    currentQuestion={currentQuestion}
                />
            ) : (
                <div>
                    <>
                        <div className={styles.addNewQuesBox}>
                            <div></div>
                            <Button
                                variant="contained"
                                sx={{
                                    width: "193px",
                                    height: "38px",
                                    boxShadow: "none",
                                    backgroundColor: "#3f3f3f",
                                    fontSize: "14px",
                                    fontWeight: 500,
                                    textTransform: "none",
                                    display: "flex",
                                    justifyContent: "start",
                                    gap: "10px",
                                    borderRadius: "6px",
                                    "&.MuiButtonBase-root:hover": {
                                        backgroundColor: "#878b93",
                                    },
                                }}
                                onClick={() => {
                                    setAddQuestionActive(true);
                                    setEdit(false);
                                }}
                            >
                                <img src={"/icons/add-icon.svg"} alt={"add"}/>
                                {t('admin_test_questions.add_new_question')}
                            </Button>
                        </div>
                        <div className={styles.totalQues}>
                            {t('admin_test_questions.total')} {filteredQuestions.length} {filteredQuestions.length === 1 ? t('admin_test_questions.question') : t('admin_test_questions.questions')}
                        </div>
                        <div className={styles.quesSearchBox}>
                            <SearchBox onInputChange={setSearchVal}/>
                        </div>
                        <div className={styles.questionsList}>
                            {questionsToShow.map((question: any, index: number) => {
                                return (
                                    <Accordion classes={{root: "quesAccordion"}} key={index}>
                                        <AccordionSummary
                                            expandIcon={<ExpandMoreIcon/>}
                                            aria-controls="panel1-content"
                                            id="panel1-header"
                                        >
                                            <div
                                                style={{
                                                    display: "flex",
                                                    gap: "10px",
                                                    flexDirection: "column",
                                                }}
                                            >
                                                <div className={styles.questionId}>{question.guid}</div>
                                                <div className={styles.question}>
                                                    {question.question}
                                                </div>
                                            </div>
                                        </AccordionSummary>
                                        <AccordionDetails>
                                            <div className={styles.quesOptions}>
                                                <div className={styles.quesOption}>
                                                    <span>A)</span> {question.answer1}
                                                </div>
                                                <div className={styles.quesOption}>
                                                    <span>B)</span> {question.answer2}
                                                </div>
                                                <div className={styles.quesOption}>
                                                    <span>C)</span> {question.answer3}
                                                </div>
                                                <div className={styles.quesOption}>
                                                    <span>D)</span> {question.answer1}
                                                </div>
                                            </div>
                                            <div className={styles.quesAnswerBox}>
                                                <div className={styles.quesAnswer}>
                                                    <b>{t('admin_test_questions.answer')}</b> {question[question.correctAnswer]}
                                                </div>

                                                <div className={styles.quesAnswerActions}>
                                                    <a
                                                        href={`javascript:void(0)`}
                                                        onClick={() => {
                                                            setCurrentQuestion(question);
                                                            setAddQuestionActive(true);
                                                            setEdit(true);
                                                        }}
                                                    >
                                                        {t('admin_test_questions.edit')}
                                                    </a>
                                                    <a
                                                        href={`javascript:void(0)`}
                                                        onClick={() => {
                                                            setCurrentQuestion(question);
                                                            setDeleteQuestion(true);
                                                        }}
                                                    >
                                                        {t('admin_test_questions.delete')}
                                                    </a>
                                                </div>
                                            </div>
                                        </AccordionDetails>
                                    </Accordion>
                                );
                            })}
                        </div>
                        <div className={styles.quesPagination}>
                            <TablePagination
                                rowsPerPageOptions={[10, 20, 30]}
                                component="div"
                                count={10}
                                colSpan={5}
                                rowsPerPage={rowsPerPage}
                                page={page}
                                onPageChange={handleChangePage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                labelRowsPerPage={t('admin_test_questions.entries_per_page')}
                            />
                        </div>
                    </>
                </div>
            )}
            <ConfirmModal
                open={deleteQuestion}
                title={t('admin_test_questions.confirm_delete_title')}
                content={t('admin_test_questions.confirm_delete_content')}
                actionLabel={t('admin_test_questions.confirm_delete_action')}
                handleClose={() => setDeleteQuestion(false)}
                onConfirm={() => deleteQuestionFunc(currentQuestion.docId)}
            />
        </div>
    );
};

export default QuestionBankPage;
