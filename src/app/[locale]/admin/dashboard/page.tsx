"use client";
import React, { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import styles from "@/src/styles/questionsBank.module.css";
import { getAllDocs, queryData, } from "@/src/Firebase/firebase.utils";
import { Paper, Table, TableBody, TableCell, TableContainer, TableRow, } from "@mui/material";
import EnhancedTableHead from "@/src/Components/Table/EnhancedTableHead";
import { formatDate, getComparator, stableSort } from "@/src/Utils/helpers";
import UserProfile from "@/src/Components/Admin/UserProfile";
import TableCustomPagination from "@/src/Components/Table/TableCustomPagination";
import { useTranslation } from "@/src/Utils/i18n";
import { DashCreateData, DashTableColumn } from "@/src/Types";

interface Column {
    id: "requestDate" | "profile" | "amount";
    label: string;
    maxWidth?: number;
    sorting?: boolean;
    align?: "right";
    format?: (value: number) => string;
}

const columns: readonly Column[] = [
    { id: "requestDate", label: "Request Date", maxWidth: 200, sorting: true },
    { id: "amount", label: "Amount", maxWidth: 100, sorting: true },
    { id: "profile", label: "Profile Link", maxWidth: 150, sorting: false },
];

function createData(requestDate: any, amount: string, profile: any): DashCreateData {
    return {
        requestDate,
        amount,
        profile,
    };
}

const DashboardPage = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const userId = searchParams.get("id");
    const [totalUsers, setTotalUsers] = useState(0);
    const [applicants, setApplicants] = useState(0);
    const [rows, setRows] = useState<any>([]);
    const [page, setPage] = useState(0);
    const [isLoading, setLoading] = useState(true);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [order, setOrder] = useState("desc");
    const [orderBy, setOrderBy] = useState("date");
    const [selectedUserId, setSelectedUserId] = useState(null as any);
    const [transactionData, setTransactionData] = useState<any>(null);
    const [totalTransactions, setTotalTransactions] = useState(0);
    const [notApprovedTutors, setNotApprovedTutors] = useState<any>([]);
    const { locale ,t} = useTranslation();

    const handleRequestSort = (event, property) => {
        const isAsc = orderBy === property && order === "asc";
        setOrder(isAsc ? "desc" : "asc");
        setOrderBy(property);
    };

    const getTransactions = async () => {
        const resp: any = await getAllDocs("transactions");
        if (resp.status && resp.fullData.length > 0) {
            setTransactionData(resp.fullData);
        }
    };

    useEffect(() => {
        if (userId) {
            setSelectedUserId(userId);
        } else {
            resetSelectedUser();
        }
    }, [userId]);

    const handleSelectUser = (id) => {
        router.push(`/${locale}/admin/dashboard?id=${id}`);
    };

    const resetSelectedUser = () => {
        setSelectedUserId(null);
        getPendingWithdrawals();
    };

    const getUsers = async () => {
        const resp: any = await queryData("users", "isApplicant", true);
        const resp2: any = await getAllDocs("users");
        let applicantsFetched = 0;
        if (resp.status && resp.fullData.length > 0) {
            applicantsFetched = resp.fullData.length;
            let applicantsList = resp.fullData;
            const filteredTutors = applicantsList.filter(item => item.type === "tutor");
            setNotApprovedTutors(filteredTutors);
        }
        if (resp2.status && resp2.fullData.length > 0) {
            setTotalUsers(resp2.fullData.length);
            setApplicants(applicantsFetched);
        }
    };

    const renderNotApprovedApplicants = () => {
        let bufferData: any = [];
        let filteredTutors = notApprovedTutors.filter(item => item.applicantQuestionAnswered === 2);
        filteredTutors = [...filteredTutors].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        if (filteredTutors && filteredTutors.length) {
            filteredTutors.forEach(tutor => {
                let tutorFName = tutor.firstName ? tutor.firstName : '';
                let tutorLName = tutor.lastName ? tutor.lastName : '';
                let tutorImage = tutor.imgUrl ? tutor.imgUrl : '/images/upload-placeholder.png';

                bufferData.push(
                    <div className={styles.tutorItem}>
                        <div className={styles.tutorImage}><img src={tutorImage} alt={'tutor-image'} /></div>
                        <div className={styles.tutorInfo}>
                            <div className={styles.tutorName}>{`${tutorFName} ${tutorLName}`}</div>
                            <div className={styles.tutorStatus}>Applicant</div>
                        </div>
                        <div className={styles.tutorView} onClick={() => handleSelectUser(tutor.docId)}>View</div>
                    </div>
                )
            });
        }
        return bufferData;
    }

    const getPendingWithdrawals = async () => {
        setLoading(true);
        const resp: any = await getAllDocs("pending_withdrawals");

        if (resp.status && resp.fullData.length > 0) {
            const rowData: any = [];
            resp.fullData.forEach((tutor: any) => {
                const createdData = {
                    ...createData(
                        formatDate(Number(tutor.date)),
                        "$" + tutor.amount.toFixed(2),
                        "View Profile"
                    ),
                    docId: tutor.userId,
                };
                rowData.push(createdData);
            });
            setRows(rowData);
            setLoading(false);
        } else {
            setLoading(false);
        }
    };

    useEffect(() => {
        getUsers();
        getPendingWithdrawals();
        getTransactions();
    }, []);

    useEffect(() => {
        if (transactionData && transactionData.length) {
            const obj: any = {
                1: 0,
                2: 0,
                3: 0,
                4: 0,
                5: 0,
                6: 0,
                7: 0,
                8: 0,
                9: 0,
                10: 0,
                11: 0,
                12: 0,
            };

            transactionData.forEach((row: any) => {
                const transactionMonth = row.month;
                obj[transactionMonth] = obj[transactionMonth] + row.amount;
            });

            const arr: any = Object.values(obj);
            const total = arr.reduce((a: number, b: number) => a + b, 0);

            setTotalTransactions(total.toFixed(2));
        }
    }, [transactionData]);

    if (selectedUserId) {
        return (
            <UserProfile
                backToTitle={"Back to Dashboard"}
                selectedUserId={selectedUserId}
                handleGotoBack={resetSelectedUser}
                withdrawalPage={true}
            />
        );
    }

    return (
        <div className={`${styles.questionsWrapper} ${styles.dashboardWrapper}`}>
            <div className={styles.questionTitle}>{t('navigation.dashboard')}</div>

            <div className={styles.InfoRow}>
                <div className={styles.infoBox}>
                    <div className={styles.infoBoxInner}>
                        <div className={styles.infoImage}>
                            <img
                                src={"/icons/admin/admin-users-icon.svg"}
                                alt={"admin-users-icon"}
                            />
                        </div>
                        <div className={styles.infoText}>
                            <div>{t('admin.total_users')}</div>
                            <h2> {totalUsers ? totalUsers : 0} </h2>
                        </div>
                    </div>
                </div>

                <div className={styles.infoBox}>
                    <div className={styles.infoBoxInner}>
                        <div className={styles.infoImage}>
                            <img
                                src={"/icons/admin/admin-applicant-icon.svg"}
                                alt={"admin-applicant-icon"}
                            />
                        </div>
                        <div className={styles.infoText}>
                            <div>{t('admin.total_applicants')}</div>
                            <h2> {applicants ? applicants : 0} </h2>
                        </div>
                    </div>
                </div>

                <div className={styles.infoBox}>
                    <div className={styles.infoBoxInner}>
                        <div className={styles.infoImage}>
                            <img
                                src={"/icons/admin/admin-dollar-icon.svg"}
                                alt={"admin-dollar-icon"}
                            />
                        </div>
                        <div className={styles.infoText}>
                            <div>{t('admin.total_earnings')}</div>
                            <h2> {totalTransactions ? "$" + Math.ceil(totalTransactions) : 0} </h2>
                        </div>
                    </div>
                </div>
            </div>

            <div className={styles.tableTwoCols}>
                <div className={`${styles.tableHolder} ${styles.payoutRequests}`}>
                    <div className={styles.tableTitle}>{t('admin_user_profile.payout_request')}</div>
                    <Paper
                        sx={{
                            marginTop: "18px",
                            width: "100%",
                            borderRadius: "12px",
                            boxShadow: "0px 8px 18px rgba(123, 135, 156, 0.08)",
                        }}
                    >
                        <TableContainer
                            className={"tableContainer"}
                            sx={{
                                maxHeight: "400px", // Set the max height to enable scrolling
                                overflowY: "auto",
                                border: '1px solid #eeeef2'
                            }}
                        >
                            <Table stickyHeader>
                                <EnhancedTableHead
                                    columns={columns as DashTableColumn[]}
                                    order={order}
                                    orderBy={orderBy}
                                    onRequestSort={handleRequestSort}
                                />
                                <TableBody sx={{ height: rows && rows.length ? "auto" : "350px" }}>
                                    {rows && rows.length ? (
                                        stableSort(rows, getComparator(order, orderBy)).slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((row, index) => {
                                            return (
                                                <TableRow
                                                    hover
                                                    sx={{
                                                        overflowY: "scroll",
                                                    }}
                                                    role="checkbox"
                                                    tabIndex={-1}
                                                    key={`table-row-${index}`}
                                                >
                                                    {columns.map((column) => {
                                                        const value = row[column.id];
                                                        return (
                                                            <TableCell key={column.id} align={column.align}
                                                                width={column.maxWidth}>
                                                                {column.id === "profile" ? (
                                                                    <span
                                                                        className={"actionLink"}
                                                                        onClick={() => handleSelectUser(row.docId)}
                                                                        style={{ textDecoration: 'none' }}
                                                                    >
                                                                        {value}
                                                                    </span>
                                                                ) : (
                                                                    value
                                                                )}
                                                            </TableCell>
                                                        );
                                                    })}
                                                </TableRow>
                                            );
                                        })
                                    ) : !isLoading ? (
                                        <TableRow className="tableNoDataFound">
                                            <TableCell colSpan={3}>
                                                No payout requests at the moment
                                            </TableCell>
                                        </TableRow>
                                    ) : <TableRow className="tableNoDataFound"><TableCell
                                        colSpan={3}>Loading...</TableCell></TableRow>}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </Paper>
                    <TableCustomPagination
                        filteredRowsLength={rows.length}
                        page={page}
                        rowsPerPage={rowsPerPage}
                        setPage={setPage}
                        setRowsPerPage={setRowsPerPage}
                    />
                </div>

                <div className={styles.tableHolder} style={{ width: '100%' }}>
                    <div className={styles.tableTitle}>Applicants</div>
                    <div className={`${styles.tutorList} webkitScrollbar`}>
                        {renderNotApprovedApplicants()}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DashboardPage;
