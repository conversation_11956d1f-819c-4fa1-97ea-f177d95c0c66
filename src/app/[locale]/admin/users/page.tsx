"use client";
import React, {useEffect, useState} from "react";
import moment from "moment";
import {useRouter, useSearchParams} from "next/navigation";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableRow from "@mui/material/TableRow";
import TableCell from "@mui/material/TableCell";
import TableBody from "@mui/material/TableBody";
import TableContainer from "@mui/material/TableContainer";
import SearchBox from "@/src/Components/Widgets/SearchBox";
import {db} from "@/src/Firebase/firebase.utils";
import EnhancedTableHead from "@/src/Components/Table/EnhancedTableHead";
import {getComparator, stableSort} from "@/src/Utils/helpers";
import Spinner from "@/src/Components/Widgets/Spinner";
import UserProfile from "@/src/Components/Admin/UserProfile";
import {collection, onSnapshot, query} from "firebase/firestore";
import styles from "@/src/styles/questionsBank.module.css";
import Filters from "@/src/Components/Widgets/Filters";
import TableCustomPagination from "@/src/Components/Table/TableCustomPagination";
import {UserCreateData, UserTableColumn} from "@/src/Types";

const columns: UserTableColumn[] = [
    {id: "user", label: "User", minWidth: 120, sorting: true},
    {id: "email", label: "Email", minWidth: 100, sorting: true},
    {id: "type", label: "User Type", minWidth: 100, sorting: true},
    {id: "answers", label: "Answers", minWidth: 100, sorting: true},
    {id: "dateJoined", label: "Date Joined", minWidth: 100, sorting: true},
    {id: "action", label: "Action", minWidth: 100, sorting: false, align: 'center'},
];

function createData(
    user: string,
    email: string,
    docId: string,
    answers: any,
    dateJoined: string,
    type: string,
    action: string,
    isAdmin: boolean,
    status: string
): UserCreateData {
    return {
        user,
        email,
        docId,
        answers,
        dateJoined,
        type,
        action,
        isAdmin,
        status,
    };
}

const UsersPage = () => {
    // Router
    const router = useRouter();
    const searchParams = useSearchParams();
    const userId = searchParams.get("id");

    // States
    const [isLoading, setLoading] = React.useState(true);
    const [filterValue, setFilterValue] = useState("");
    const [page, setPage] = React.useState(0);
    const [rowsPerPage, setRowsPerPage] = React.useState(10);
    const [order, setOrder] = React.useState("desc");
    const [orderBy, setOrderBy] = React.useState("dateJoined");
    const [allFilters, setAllFilters] = useState([] as any);
    const [usersFetched, setUsersFetched] = useState([] as any);
    const [showArchived, setShowArchived] = useState(false);
    const [rows, setRows] = useState([] as any);
    const [selectedUserId, setSelectedUserId] = useState(null as any);

    const handleRequestSort = (event: React.MouseEvent<unknown>, property: string) => {
        const isAscending = orderBy === property && order === "asc";
        const newOrder = isAscending ? "desc" : "asc";

        const sortedData = [...rows].sort((a, b) => {
            let valueA = a[property];
            let valueB = b[property];

            // Handle blank values (null, undefined, empty string "")
            const isValueABlank = valueA == null || valueA === "";
            const isValueBBlank = valueB == null || valueB === "";

            if (isValueABlank && isValueBBlank) return 0; // Both blank → keep order
            if (isValueABlank) return 1; // Blank values go to the end
            if (isValueBBlank) return -1; // Blank values go to the end

            // Convert strings that represent numbers to actual numbers
            if (!isNaN(Number(valueA)) && !isNaN(Number(valueB))) {
                valueA = Number(valueA);
                valueB = Number(valueB);
            }

            // Numeric sorting
            if (typeof valueA === "number" && typeof valueB === "number") {
                return isAscending ? valueA - valueB : valueB - valueA;
            }

            // String sorting (case-insensitive)
            if (typeof valueA === "string" && typeof valueB === "string") {
                return isAscending
                    ? valueA.toLowerCase() > valueB.toLowerCase() ? 1 : -1
                    : valueA.toLowerCase() < valueB.toLowerCase() ? 1 : -1;
            }

            return 0; // Keep order if types are mixed or unknown
        });

        setOrder(newOrder);
        setOrderBy(property);
        setRows(sortedData);
    };

    const getAllUsers = async () => {
        setShowArchived(false);
        const q = query(collection(db, "users"));
        onSnapshot(q, (querySnapshot) => {
            const data: any = [];
            querySnapshot.forEach((doc) => {
                data.push({...doc.data(), docId: doc.id});
            });

            setUsersFetched(data);
            setLoading(false);
        });
    };

    useEffect(() => {
        getAllUsers();
    }, []);

    useEffect(() => {
        if (usersFetched && usersFetched.length) {
            const tableRows: any[] = [];

            for (let i = 0; i < usersFetched.length; i++) {
                const userItem: any = usersFetched[i];
                const isAdmin = userItem.type && userItem.type === "admin";
                const isArchived = userItem.is_archived ? userItem.is_archived : false;
                const createdAt = userItem && userItem.createdAt ? userItem.createdAt : "";
                const dateJoined = createdAt ? createdAt.seconds : "";
                const firstName = userItem && userItem.firstName ? userItem.firstName : "";
                const lastName = userItem && userItem.lastName ? userItem.lastName : "";
                const userName = `${firstName} ${lastName}`;
                const type = userItem.type === "user" ? "Student" : userItem.type === "tutor" ? userItem.isApplicant ? "Applicant" : userItem.denied ? 'Denied' : "Tutor" : userItem.type === "admin" ? "Admin" : "Unknown";

                let totalAnswers = 0;
                if (type === 'Applicant') {
                    totalAnswers = userItem.applicantQuestionAnswered;
                } else if (type === 'Tutor') {
                    totalAnswers = userItem.answers;
                } else if (type === 'Student') {
                    totalAnswers = 0;
                }

                if (isArchived) {
                    const createdData = createData(
                        userName,
                        userItem.email,
                        userItem.docId,
                        totalAnswers ? totalAnswers : 0,
                        dateJoined,
                        type,
                        "Visit Profile",
                        isAdmin,
                        'Archived'
                    );
                    tableRows.push(createdData);
                } else if (!isArchived) {
                    const createdData = createData(
                        userName,
                        userItem.email,
                        userItem.docId,
                        totalAnswers ? totalAnswers : 0,
                        dateJoined,
                        type,
                        "Visit Profile",
                        isAdmin,
                        'Active'
                    );
                    tableRows.push(createdData);
                }
            }
            setRows(tableRows);
        }
    }, [usersFetched, showArchived]);

    useEffect(() => {
        if (userId) {
            setSelectedUserId(userId);
        } else {
            resetSelectedUser();
        }
    }, [userId]);

    const handleSelectUser = (id) => {
        router.push(`/admin/users?id=${id}`);
    };

    const resetSelectedUser = () => {
        setSelectedUserId(null);
        setFilterValue("");
    };

    if (selectedUserId) {
        return (
            <UserProfile
                isAdmin={true}
                selectedUserId={selectedUserId}
                handleGotoBack={resetSelectedUser}
            />
        );
    }

    let filteredRows: any = [];

    // if (showApplicants) {
    //   rows.forEach((row: any) => {
    //     if (row.type === "Applicant") {
    //       filteredRows.push(row);
    //     }
    //   });
    // } else

    if (filterValue !== "" && rows.length > 0) {
        rows.forEach((row: any) => {
            if (
                (typeof row.email === "string" &&
                    row.email.toLowerCase().includes(filterValue.toLowerCase())) ||
                (typeof row.user === "string" &&
                    row.user.toLowerCase().includes(filterValue.toLowerCase())) ||
                (typeof row.certificateNo === "string" &&
                    row.certificateNo.toLowerCase().includes(filterValue.toLowerCase()))
            ) {
                filteredRows.push(row);
            }
        });
    } else {
        filteredRows = rows;
    }

    if (allFilters && Object.keys(allFilters).length) {
        filteredRows = filteredRows.filter(user => {
            return (allFilters.answered === "" || ("answers" in user && user.answers === allFilters.answered)) && (allFilters.userType === "" || user.type === allFilters.userType) && (allFilters.status === "" || user.status === allFilters.status);
        });
    }

    return (
        <div className={`${styles.questionsWrapper} ${styles.usersWrapper}`}>
            <div className={styles.questionTitle}>All Users</div>
            <div className={`${styles.quesSearchBox} ${styles.userSearchBox}`}>
                <SearchBox
                    placeholder={"Search by name, email"}
                    onInputChange={(value) => {
                        setFilterValue(value);
                    }}
                    style={{maxWidth: '400px'}}
                />
                <Filters onApplyFilters={setAllFilters}/>
            </div>
            {isLoading ? (
                <Spinner/>
            ) : (
                <div className={"tableWrapper multiColsTable"}>
                    <Paper
                        sx={{
                            marginTop: "10px",
                            width: "100%",
                            borderRadius: "12px",
                            boxShadow: "0px 8px 18px rgba(123, 135, 156, 0.08)",
                        }}
                    >
                        <TableContainer className={"tableContainer adminUsersTable"}>
                            <Table>
                                <EnhancedTableHead
                                    columns={columns}
                                    order={order}
                                    orderBy={orderBy}
                                    onRequestSort={handleRequestSort}
                                    rowCount={filteredRows.length}
                                />
                                <TableBody>
                                    {filteredRows && filteredRows.length ? (
                                        stableSort(filteredRows, getComparator(order, orderBy))
                                            .slice(
                                                page * rowsPerPage,
                                                page * rowsPerPage + rowsPerPage
                                            )
                                            .map((row) => {
                                                return (
                                                    <TableRow
                                                        hover
                                                        role="checkbox"
                                                        tabIndex={-1}
                                                        key={row.docId}
                                                        className={row.isAdmin ? "blueColorRow" : ""}
                                                    >
                                                        {columns.map((column) => {
                                                            const value = row[column.id];
                                                            return (
                                                                <TableCell key={column.id} align={column.align}
                                                                           className={`table-col-${column.id}`}>
                                                                    {column.id === "action" ? (
                                                                        <span
                                                                            className={"actionLink"}
                                                                            onClick={() =>
                                                                                handleSelectUser(row.docId)
                                                                            }
                                                                        >
                                      {value}
                                    </span>
                                                                    ) : column.id === "user" ? (
                                                                        value
                                                                    ) : column.id === 'dateJoined' ? moment.unix(value).format("DD MMM, YYYY") : (
                                                                        value
                                                                    )}
                                                                </TableCell>
                                                            );
                                                        })}
                                                    </TableRow>
                                                );
                                            })
                                    ) : (
                                        <TableRow className="tableNoDataFound">
                                            <TableCell colSpan={6}>
                                                No users found.
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </Paper>

                    <TableCustomPagination
                        filteredRowsLength={filteredRows.length}
                        page={page}
                        rowsPerPage={rowsPerPage}
                        setPage={setPage}
                        setRowsPerPage={setRowsPerPage}
                    />
                </div>
            )}
        </div>
    );
};

export default UsersPage;
