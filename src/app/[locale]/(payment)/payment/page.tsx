// @ts-nocheck
"use client";
import React, { useContext, useEffect, useMemo, useState } from "react";
import {useTranslation} from "@/src/Utils/i18n";
import Navbar from "@/src/Components/Navigation/Navbar";
import PaymentForm from "@/src/Components/Payment/paymentForm";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import styles from "@/src/styles/home.module.css";
import "@/src/styles/globals.css";
import TabsStudent from "@/src/Components/Navigation/TabsStudent";
import { UserContext } from "@/src/Contexts/UserContext";
import Spinner from "@/src/Components/Widgets/Spinner";
import { pricingPlans } from "@/src/Utils/helpers";
import {
    getUserById,
    isStudentAuthUser,
    updateCollection,
} from "@/src/Firebase/firebase.utils";

const stripePromise = loadStripe(
    process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
);

const Payment = () =>
{
    const {t} = useTranslation();
    const { user, fetching } = useContext(UserContext);
    const [purchasing, setPurchasing] = useState(false);
    const [buyClicked, setBuyClicked] = useState(false);
    const [btnDisable, setBtnDisable] = useState(true);
    const [isPaidUser, setIsPaidUser] = useState(false);
    const [planPack, setPlanPack] = useState("");

    useEffect(() =>
    {
        const localPlanPack = typeof window !== "undefined" ? localStorage.getItem("HMWK_SELECTED_PACK") : null;

        if (localPlanPack) {
            setPlanPack(localPlanPack);
        }

        if (user.id && user.type === "user")
        {
            const updatePack = async () => {
                await updateCollection(
                    "users",
                    user.id,
                    {
                        hmwkSelectedPack: planPack,
                    },
                    true
                );
            };

            const checkPaidStatus = async () =>
            {
                const value = await isStudentAuthUser(user.id);
                const resp: any = await getUserById(user.id);

                if (!localPlanPack && resp?.hmwkSelectedPack) {
                    setPlanPack(resp.hmwkSelectedPack);
                } else if (!resp?.hmwkSelectedPack && localPlanPack) {
                    await updatePack();
                }

                setIsPaidUser(value);
            };

            checkPaidStatus();
        }
    }, [user, planPack]);

    const selectedPlan = useMemo(() => {
        return planPack ? pricingPlans.find((plan) => plan.id === planPack) : null;
    }, [planPack]);

    const suffix = useMemo(() => {
        return selectedPlan && selectedPlan.questions > 1 ? t('admin.question') : t('homepage.questions');
    }, [selectedPlan, t]);

    const options = useMemo(
        () => ({
            mode: "payment",
            amount: selectedPlan?.price || 0,
            currency: "usd",
            appearance: {
                variables: {
                    colorText: "#3F3F3F",
                },
            },
        }),
        [selectedPlan]
    );

    return (
        <div className={styles.paymentPage}>
            {!fetching && planPack ? (
                <>
                    <Navbar
                        isPublicPage={!(user && user.id)}
                        isPaidUser={isPaidUser}
                    />
                    {user?.id && isPaidUser && (
                        <TabsStudent
                            active={2}
                            remQuestions={user.questions || 0}
                        />
                    )}

                    <div className={styles.pageTopSpace}>
                        <div className="mainContainer">
                            <h2>{t('payment.payment_info')}</h2>
                            <div className={styles.paymentContainer}>
                                <div className={styles.payment}>
                                    <Elements
                                        stripe={stripePromise}
                                        options={options}
                                    >
                                        <PaymentForm
                                            setBuyClicked={setBuyClicked}
                                            buyClicked={buyClicked}
                                            btnDisable={btnDisable}
                                            setBtnDisable={setBtnDisable}
                                            purchasing={purchasing}
                                            setPurchasing={setPurchasing}
                                            price={selectedPlan?.price}
                                            packInfo={selectedPlan}
                                            packPage={true}
                                        />
                                    </Elements>
                                </div>
                                <div className={styles.rightCards}>
                                    <div className={styles.card1}>
                                        <div className={styles.row}>
                                            <div className={styles.title}>
                                                {selectedPlan?.questions}{" "}
                                                {suffix} {t('student.pack')}
                                            </div>
                                            <div className={styles.price}>
                                                ${selectedPlan?.price}
                                            </div>
                                        </div>
                                        <div className={styles.divider}></div>
                                        <div className={styles.row}>
                                            <div className={styles.price}>
                                                {t('common.total')}
                                            </div>
                                            <div className={styles.price}>
                                                ${selectedPlan?.price}
                                            </div>
                                        </div>
                                    </div>
                                    <div className={styles.card2}>
                                        <h3>{t('homepage.features')}</h3>
                                        <ul>
                                            <li>{t('payment.credits_never_expire')}</li>
                                            <li>{t('payment.satisfaction_guaranteed')}</li>
                                            <li>{t('payment.tutor_access_24_7')}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            ) : (
                <Spinner />
            )}
        </div>
    );
};

export default Payment;
