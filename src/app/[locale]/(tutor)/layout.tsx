'use client';
import React, { useCallback, useContext, useEffect, useMemo, useState } from "react";
import styles from "@/src/styles/navbar.module.css";
import {SPECIAL_ROUTES, TUTOR_PATH_TO_TAB_INDEX, TUTOR_PATHS} from "@/src/Constants/tabNavigation";
import { useTranslation } from "@/src/Utils/i18n";
import Navbar from "@/src/Components/Navigation/Navbar";
import TabsTutor from "@/src/Components/Navigation/TabsTutor";
import Footer from "@/src/Components/Navigation/Footer";
import { usePathname, useRouter } from "next/navigation";
import { UserContext } from "@/src/Contexts/UserContext";
import Spinner from "@/src/Components/Widgets/Spinner";


interface TutorDashboardLayoutProps {
    children: React.ReactNode;
}

export default function TutorDashboardLayout({children}: TutorDashboardLayoutProps) {
    const {user, fetching, initialLoad, isLoggingOut} = useContext(UserContext);
    const [activeTab, setActiveTab] = useState(0);
    const {t} = useTranslation();

    const router = useRouter();
    const pathname = usePathname();

    const allTutorPaths = useMemo(() => [
        ...TUTOR_PATHS,
        SPECIAL_ROUTES.PROFILE,
        SPECIAL_ROUTES.PAYOUT_METHODS
    ], []);

    const setActiveTabCallback = useCallback((tab: React.SetStateAction<number>) => {
        setActiveTab(tab);
    }, []);

    const shouldShowSpinner = initialLoad || (fetching && !user.id) || isLoggingOut;

    useEffect(() => {
        if (sessionStorage.getItem('reloadOnBack') === 'true') {
            sessionStorage.removeItem('reloadOnBack');
            router.refresh();
        }
    }, [router]);

    useEffect(() => {
        if (initialLoad || fetching || isLoggingOut || !allTutorPaths.includes(pathname)) return;

        if (!user.id) {
            router.push("/login");
            return;
        }

        if (user.type !== "tutor") {
            const redirectMap = {
                user: "/",
                admin: "/admin/dashboard"
            };
            router.push(redirectMap[user.type] || "/login");
            return;
        }

        if (user.type) {
            localStorage.setItem("HMWK_DEFAULT_USER_TYPE", user.type);
        }

        console.log('🔒 Tutor access granted');
    }, [user, fetching, initialLoad, isLoggingOut, router, pathname, allTutorPaths]);

    useEffect(() => {
        const tabIndex = TUTOR_PATH_TO_TAB_INDEX[pathname];
        if (tabIndex !== undefined) {
            setActiveTab(tabIndex);
        } else if (pathname === SPECIAL_ROUTES.PROFILE) {
            setActiveTab(5);
        } else if (pathname === SPECIAL_ROUTES.PAYOUT_METHODS) {
            setActiveTab(2);
        }
    }, [pathname]);

    const navbarActiveTab = useMemo(() => {
        return pathname === SPECIAL_ROUTES.PAYOUT_METHODS ? 2 : activeTab;
    }, [activeTab, pathname]);

    return (
        <div className={styles.pageTopMargin}>
            {!shouldShowSpinner && (
                <>
                    <Navbar
                        // logo="/images/logo.svg"
                        id="en"
                        active={navbarActiveTab}
                        setActiveTab={setActiveTabCallback}
                        isPaidUser={true}
                        isPublicPage={false}
                    />
                    <div  style={{ paddingTop: '1%'}}>
                    <TabsTutor
                        user={user}
                        active={activeTab}
                        setActiveTab={setActiveTabCallback}
                    />
                    </div>
                </>
            )}

            <div className={styles.pageTopMargin}>
                {shouldShowSpinner ? <Spinner/> : children}
            </div>

            {!shouldShowSpinner && user?.id && (
                <Footer
                    isPublicPage={false}
                    setActiveTab={setActiveTabCallback}
                    userType={user.type || ''}
                    t={t}
                />
            )}
        </div>
    );
}