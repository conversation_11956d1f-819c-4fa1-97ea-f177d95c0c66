import {NextRequest, NextResponse} from 'next/server';
import {cert, getApps, initializeApp} from 'firebase-admin/app';
import {getFirestore} from 'firebase-admin/firestore';

if (!getApps().length) {
    initializeApp({
        credential: cert({
            projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
            clientEmail: process.env.FB_CLIENT_EMAIL!,
            privateKey: process.env.FB_PRIVATE_KEY?.replace(/\\n/g, '\n')!,
        }),
    });
}

const db = getFirestore();

export async function GET(
    request: NextRequest,
    {params}: { params: { uid: string } }
) {
    try {
        // auth check
        const authHeader = request.headers.get('x-internal-auth');
        const expectedSecret = process.env.INTERNAL_API_SECRET || 'default-secret';

        if (authHeader !== expectedSecret) {
            return NextResponse.json({error: 'Unauthorized'}, {status: 401});
        }

        const {uid} = params;

        // Get user document from Firestore
        const userDoc = await db.collection('users').doc(uid).get();

        if (!userDoc.exists) {
            return NextResponse.json({type: 'user'});
        }

        const userData = userDoc.data();

        return NextResponse.json({
            type: userData?.type || 'user',
        });

    } catch (error) {
        console.error('Error fetching user type:', error);
        return NextResponse.json({type: 'user'});
    }
}