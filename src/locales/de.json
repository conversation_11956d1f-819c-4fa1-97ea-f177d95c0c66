{"common": {"loading": "crwdns1338:0crwdne1338:0", "error": "crwdns1340:0crwdne1340:0", "success": "crwdns1342:0crwdne1342:0", "cancel": "crwdns1344:0crwdne1344:0", "continue": "crwdns1346:0crwdne1346:0", "save": "crwdns1348:0crwdne1348:0", "edit": "crwdns1350:0crwdne1350:0", "delete": "crwdns1352:0crwdne1352:0", "back": "crwdns1354:0crwdne1354:0", "next": "crwdns1356:0crwdne1356:0", "previous": "crwdns1358:0crwdne1358:0", "close": "crwdns1360:0crwdne1360:0", "confirm": "crwdns1362:0crwdne1362:0", "yes": "crwdns1364:0crwdne1364:0", "no": "crwdns1366:0crwdne1366:0", "ok": "crwdns1368:0crwdne1368:0", "submit": "crwdns1370:0crwdne1370:0", "reset": "crwdns1372:0crwdne1372:0", "search": "crwdns1374:0crwdne1374:0", "filter": "crwdns1376:0crwdne1376:0", "sort": "crwdns1378:0crwdne1378:0", "refresh": "crwdns1380:0crwdne1380:0", "download": "crwdns1382:0crwdne1382:0", "upload": "crwdns1384:0crwdne1384:0", "view": "crwdns1386:0crwdne1386:0", "add": "crwdns1388:0crwdne1388:0", "remove": "crwdns1390:0crwdne1390:0", "update": "crwdns1392:0crwdne1392:0", "create": "crwdns1394:0crwdne1394:0", "select": "crwdns1396:0crwdne1396:0", "choose": "crwdns1398:0crwdne1398:0", "browse": "crwdns1400:0crwdne1400:0", "clear": "crwdns1402:0crwdne1402:0", "apply": "crwdns1404:0crwdne1404:0", "done": "crwdns1406:0crwdne1406:0", "finish": "crwdns1408:0crwdne1408:0", "start": "crwdns1410:0crwdne1410:0", "stop": "crwdns1412:0crwdne1412:0", "pause": "crwdns1414:0crwdne1414:0", "resume": "crwdns1416:0crwdne1416:0", "retry": "crwdns1418:0crwdne1418:0", "skip": "crwdns1420:0crwdne1420:0", "help": "crwdns1422:0crwdne1422:0", "settings": "crwdns1424:0crwdne1424:0", "profile": "crwdns1426:0crwdne1426:0", "logout": "crwdns1428:0crwdne1428:0", "logging_out": "crwdns1430:0crwdne1430:0", "login": "crwdns1432:0crwdne1432:0", "go": "crwdns1434:0crwdne1434:0", "view_less": "crwdns1436:0crwdne1436:0", "view_all": "crwdns1438:0crwdne1438:0", "default": "crwdns1440:0crwdne1440:0", "congratulations": "crwdns1442:0crwdne1442:0", "signup": "crwdns1444:0crwdne1444:0", "email": "crwdns1446:0crwdne1446:0", "password": "crwdns1448:0crwdne1448:0", "name": "crwdns1450:0crwdne1450:0", "first_name": "crwdns1452:0crwdne1452:0", "last_name": "crwdns1454:0crwdne1454:0", "phone": "crwdns1456:0crwdne1456:0", "address": "crwdns1458:0crwdne1458:0", "city": "crwdns1460:0crwdne1460:0", "state": "crwdns1462:0crwdne1462:0", "country": "crwdns1464:0crwdne1464:0", "zip_code": "crwdns1466:0crwdne1466:0", "date": "crwdns1468:0crwdne1468:0", "time": "crwdns1470:0crwdne1470:0", "status": "crwdns1472:0crwdne1472:0", "price": "crwdns1474:0crwdne1474:0", "total": "crwdns1476:0crwdne1476:0", "amount": "crwdns1478:0crwdne1478:0", "currency": "crwdns1480:0crwdne1480:0", "description": "crwdns1482:0crwdne1482:0", "details": "crwdns1484:0crwdne1484:0", "information": "crwdns1486:0crwdne1486:0", "notes": "crwdns1488:0crwdne1488:0", "comments": "crwdns1490:0crwdne1490:0", "message": "crwdns1492:0crwdne1492:0", "notification": "crwdns1494:0crwdne1494:0", "alert": "crwdns1496:0crwdne1496:0", "warning": "crwdns1498:0crwdne1498:0", "info": "crwdns1500:0crwdne1500:0", "processing": "crwdns1502:0crwdne1502:0", "please_wait": "crwdns1504:0crwdne1504:0", "no_data": "crwdns1506:0crwdne1506:0", "no_results": "crwdns1508:0crwdne1508:0", "not_found": "crwdns1510:0crwdne1510:0", "unauthorized": "crwdns1512:0crwdne1512:0", "forbidden": "crwdns1514:0crwdne1514:0", "server_error": "crwdns1516:0crwdne1516:0", "network_error": "crwdns1518:0crwdne1518:0", "connection_error": "crwdns1520:0crwdne1520:0", "timeout_error": "crwdns1522:0crwdne1522:0", "validation_error": "crwdns1524:0crwdne1524:0", "required_field": "crwdns1526:0crwdne1526:0", "invalid_format": "crwdns1528:0crwdne1528:0", "invalid_email": "crwdns1530:0crwdne1530:0", "password_too_short": "crwdns1532:0crwdne1532:0", "passwords_dont_match": "crwdns1534:0crwdne1534:0", "file_too_large": "crwdns1536:0crwdne1536:0", "unsupported_file_type": "crwdns1538:0crwdne1538:0", "max_file_size": "crwdns1540:0{{size}}crwdne1540:0", "international": "crwdns1542:0crwdne1542:0", "attention": "crwdns1544:0crwdne1544:0", "type": "crwdns1546:0crwdne1546:0", "go_back": "crwdns1548:0crwdne1548:0", "select_date": "crwdns1550:0crwdne1550:0", "questions_answered": "crwdns1552:0crwdne1552:0", "user_type": "crwdns1554:0crwdne1554:0", "account_status": "crwdns1556:0crwdne1556:0", "active": "crwdns1558:0crwdne1558:0", "archived": "crwdns1560:0crwdne1560:0", "applicant": "crwdns1562:0crwdne1562:0", "student": "crwdns1564:0crwdne1564:0", "tutor": "crwdns1566:0crwdne1566:0", "updating": "crwdns1568:0crwdne1568:0", "statistics": "crwdns1570:0crwdne1570:0", "acceptance_rate": "crwdns1572:0crwdne1572:0", "students_helped": "crwdns1574:0crwdne1574:0", "top_subject": "crwdns1576:0crwdne1576:0", "daily_streak": "crwdns1578:0crwdne1578:0", "daily_challenges": "crwdns1580:0crwdne1580:0", "login_to_your_account": "crwdns1582:0crwdne1582:0", "answer_50_questions": "crwdns1584:0crwdne1584:0", "keep_answer": "crwdns1586:0crwdne1586:0", "ranks": "crwdns1588:0crwdne1588:0", "rewards": "crwdns1590:0crwdne1590:0", "less_than_500_answers": "crwdns1592:0crwdne1592:0", "bronze_frame": "crwdns1594:0crwdne1594:0", "500_answers": "crwdns1596:0crwdne1596:0", "50_Bonus": "crwdns1598:0crwdne1598:0", "silver_frame": "crwdns1600:0crwdne1600:0", "1500_answers": "crwdns1602:0crwdne1602:0", "gold_frame": "crwdns1604:0crwdne1604:0", "100_Bonus": "crwdns1606:0crwdne1606:0", "3000_answers": "crwdns1608:0crwdne1608:0", "diamond_frame": "crwdns1610:0crwdne1610:0", "300_Bonus": "crwdns1612:0crwdne1612:0", "achievements": "crwdns1614:0crwdne1614:0", "completed_registration": "crwdns1616:0crwdne1616:0", "champion_novice": "crwdns1618:0crwdne1618:0", "answered_100_questions": "crwdns1620:0crwdne1620:0", "answered_1000_questions": "crwdns1622:0crwdne1622:0", "answered_5000_questions": "crwdns1624:0crwdne1624:0", "answered_10000_questions": "crwdns1626:0crwdne1626:0", "champion_adept": "crwdns1628:0crwdne1628:0", "champion_expert": "crwdns1630:0crwdne1630:0", "champion_master": "crwdns1632:0crwdne1632:0", "streak_starter": "crwdns1634:0crwdne1634:0", "achieved_day_streak": "crwdns1636:0crwdne1636:0", "achieved_30_day_streak": "crwdns1638:0crwdne1638:0", "streak_pro": "crwdns1640:0crwdne1640:0", "top_Subject_Math": "crwdns1642:0crwdne1642:0", "become_top_in_Math": "crwdns1644:0crwdne1644:0", "top_Subject_Science": "crwdns1646:0crwdne1646:0", "Become_top_in_Science": "crwdns1648:0crwdne1648:0", "helping_hand": "crwdns1650:0crwdne1650:0", "helped_50_students": "crwdns1652:0crwdne1652:0", "helped_200_students": "crwdns1654:0crwdne1654:0", "super_helper": "crwdns1656:0crwdne1656:0", "perfect_rate": "crwdns1658:0crwdne1658:0", "acceptance_rate_month": "crwdns1660:0crwdne1660:0", "monthly_rank": "crwdns1662:0crwdne1662:0", "monthly_answers": "crwdns1664:0crwdne1664:0", "maths": "crwdns1666:0crwdne1666:0", "reading": "crwdns1668:0crwdne1668:0", "writing": "crwdns1670:0crwdne1670:0", "science": "crwdns1672:0crwdne1672:0", "answer_apprentice": "crwdns1674:0crwdne1674:0", "homework_hero": "crwdns1676:0crwdne1676:0", "knowledge_knight": "crwdns1678:0crwdne1678:0", "wisdom_wizard": "crwdns1680:0crwdne1680:0", "edit_profile": "crwdns1682:0crwdne1682:0", "edit_user_details": "crwdns1684:0crwdne1684:0", "archive_account": "crwdns1686:0crwdne1686:0", "unarchive_account": "crwdns1688:0crwdne1688:0", "delete_account": "crwdns1690:0crwdne1690:0", "approve": "crwdns1692:0crwdne1692:0", "deny": "crwdns1694:0crwdne1694:0", "all_questions": "crwdns1696:0crwdne1696:0", "answers": "crwdns1698:0crwdne1698:0", "back_to_questions": "crwdns1700:0crwdne1700:0", "history": "crwdns1702:0crwdne1702:0", "student_questions": "crwdns1704:0crwdne1704:0", "withdrawal_approved": "crwdns1706:0crwdne1706:0", "withdrawal_approval_failed": "crwdns1708:0crwdne1708:0", "applicant_approved": "crwdns1710:0crwdne1710:0", "applicant_denied": "crwdns1712:0crwdne1712:0", "user_deleted": "crwdns1714:0crwdne1714:0", "user_archived": "crwdns1716:0crwdne1716:0", "user_restored": "crwdns1718:0crwdne1718:0", "create_student_account": "crwdns1720:0crwdne1720:0", "create_tutor_account": "crwdns1722:0crwdne1722:0", "login_here": "crwdns1724:0crwdne1724:0", "set_your_own_hours": "crwdns1726:0crwdne1726:0", "easy_qualifications": "crwdns1728:0crwdne1728:0", "login_blocked_contact_support": "crwdns1730:0crwdne1730:0", "this_user_has_been_deleted": "crwdns1732:0crwdne1732:0", "unable_to_verify_account": "crwdns1734:0crwdne1734:0", "authentication_failed": "crwdns1736:0crwdne1736:0", "account_disabled_contact_support": "crwdns1738:0crwdne1738:0", "too_many_failed_attempts": "crwdns1740:0crwdne1740:0", "login_failed_try_again": "crwdns1742:0crwdne1742:0", "please_select_valid_date": "crwdns1744:0crwdne1744:0", "please_select_valid_date_range": "crwdns1746:0crwdne1746:0", "default_user": "crwdns1748:0crwdne1748:0", "preview": "crwdns1750:0crwdne1750:0", "add_icon": "crwdns1752:0crwdne1752:0", "edit_icon": "crwdns1754:0crwdne1754:0", "change_image": "crwdns1756:0crwdne1756:0", "choose_files": "crwdns1758:0crwdne1758:0", "uploaded": "crwdns1760:0crwdne1760:0", "max_file_size_100mb": "crwdns1762:0crwdne1762:0", "menu_icon": "crwdns1764:0crwdne1764:0", "toggle_menu": "crwdns1766:0crwdne1766:0", "logout_icon": "crwdns1768:0crwdne1768:0", "user_profile": "crwdns1770:0crwdne1770:0", "search_questions": "crwdns1772:0crwdne1772:0", "apply_filter": "crwdns1774:0crwdne1774:0", "select_language": "crwdns3702:0crwdne3702:0"}, "auth": {"signup_at_least_13": "crwdns1776:0crwdne1776:0", "signup_13_years_old": "crwdns1778:0crwdne1778:0", "signup_terms_and_conditions": "crwdns1780:0crwdne1780:0", "earn_per_answer": "crwdns1782:0crwdne1782:0", "set_own_hours": "crwdns1784:0crwdne1784:0", "easy_qualifications": "crwdns1786:0crwdne1786:0", "how_it_works": "crwdns1788:0crwdne1788:0", "earn_per_answer_text": "crwdns1790:0crwdne1790:0", "set_own_hours_text": "crwdns1792:0crwdne1792:0", "easy_qualifications_text": "crwdns1794:0crwdne1794:0", "how_it_works_text": "crwdns1796:0crwdne1796:0", "sign_in": "crwdns1798:0crwdne1798:0", "login_title": "crwdns1800:0crwdne1800:0", "login_subtitle": "crwdns1802:0crwdne1802:0", "signup_title": "crwdns1804:0crwdne1804:0", "signup_subtitle": "crwdns1806:0crwdne1806:0", "forgot_password": "crwdns1808:0crwdne1808:0", "reset_password": "crwdns1810:0crwdne1810:0", "create_new_password": "crwdns1812:0crwdne1812:0", "confirm_password": "crwdns1814:0crwdne1814:0", "old_password": "crwdns1816:0crwdne1816:0", "new_password": "crwdns1818:0crwdne1818:0", "change_password": "crwdns1820:0crwdne1820:0", "enter_email": "crwdns1822:0crwdne1822:0", "enter_password": "crwdns1824:0crwdne1824:0", "enter_name": "crwdns1826:0crwdne1826:0", "enter_first_name": "crwdns1828:0crwdne1828:0", "enter_last_name": "crwdns1830:0crwdne1830:0", "enter_old_password": "crwdns1832:0crwdne1832:0", "enter_new_password": "crwdns1834:0crwdne1834:0", "enter_confirm_password": "crwdns1836:0crwdne1836:0", "continue_with_google": "crwdns1838:0crwdne1838:0", "signup_with_google": "crwdns1840:0crwdne1840:0", "dont_have_account": "crwdns1842:0crwdne1842:0", "already_have_account": "crwdns1844:0crwdne1844:0", "create_one": "crwdns1846:0crwdne1846:0", "create_account": "crwdns1848:0crwdne1848:0", "signup_for_free": "crwdns1850:0crwdne1850:0", "terms_conditions": "crwdns1852:0crwdne1852:0", "privacy_policy": "crwdns1854:0crwdne1854:0", "agree_to_terms": "crwdns1856:0crwdne1856:0", "and_our": "crwdns1858:0crwdne1858:0", "password_reset_sent": "crwdns1860:0crwdne1860:0", "check_email": "crwdns1862:0crwdne1862:0", "verification_link_sent": "crwdns1864:0crwdne1864:0", "click_verification_link": "crwdns1866:0crwdne1866:0", "didnt_receive_email": "crwdns1868:0crwdne1868:0", "resend_link": "crwdns1870:0crwdne1870:0", "back_to_login": "crwdns1872:0crwdne1872:0", "no_worries_reset": "crwdns1874:0crwdne1874:0", "password_reset_success": "crwdns1876:0crwdne1876:0", "click_to_login": "crwdns1878:0crwdne1878:0", "account_doesnt_exist": "crwdns1880:0crwdne1880:0", "wrong_email_password": "crwdns1882:0crwdne1882:0", "email_exists": "crwdns1884:0crwdne1884:0", "google_auth_error": "crwdns1886:0crwdne1886:0", "profile_updated": "crwdns1888:0crwdne1888:0", "error_sending_reset": "crwdns1890:0crwdne1890:0", "old_password_required": "crwdns1892:0crwdne1892:0", "old_password_incorrect": "crwdns1894:0crwdne1894:0", "password_required": "crwdns1896:0crwdne1896:0", "email_required": "crwdns1898:0crwdne1898:0", "password_different": "crwdns1900:0crwdne1900:0", "first_name_required": "crwdns1902:0crwdne1902:0", "last_name_required": "crwdns1904:0crwdne1904:0", "create_user_failed": "crwdns1906:0crwdne1906:0", "register_as_tutor": "crwdns1908:0crwdne1908:0", "register_as_student": "crwdns1910:0crwdne1910:0", "must_be_13_tutor": "crwdns1912:0crwdne1912:0", "must_be_13_student": "crwdns1914:0crwdne1914:0", "password_reset": "crwdns1916:0crwdne1916:0", "reset_pass_text1": "crwdns1918:0crwdne1918:0", "reset_pass_text2": "crwdns1920:0crwdne1920:0", "password_updated_successfully": "crwdns1922:0crwdne1922:0", "failed_to_update_password": "crwdns1924:0crwdne1924:0", "must_be_signed_in": "crwdns1926:0crwdne1926:0", "forgot_pass_text1": "crwdns1928:0crwdne1928:0", "forgot_pass_text2": "crwdns1930:0crwdne1930:0", "forgot_pass_text3": "crwdns1932:0crwdne1932:0", "forgot_pass_text5": "crwdns1934:0crwdne1934:0", "forgot_pass_text6": "crwdns1936:0crwdne1936:0", "error_occurred": "crwdns1938:0crwdne1938:0", "enter_your_email": "crwdns1940:0crwdne1940:0"}, "navigation": {"home": "crwdns1942:0crwdne1942:0", "dashboard": "crwdns1944:0crwdne1944:0", "my_account": "crwdns1946:0crwdne1946:0", "my_tests": "crwdns1948:0crwdne1948:0", "my_questions": "crwdns1950:0crwdne1950:0", "packs": "crwdns1952:0crwdne1952:0", "settings": "crwdns1954:0crwdne1954:0", "transactions": "crwdns1956:0crwdne1956:0", "history": "crwdns1958:0crwdne1958:0", "earnings": "crwdns1960:0crwdne1960:0", "profile": "crwdns1962:0crwdne1962:0", "payout_method": "crwdns1964:0crwdne1964:0", "tutor_settings": "crwdns1966:0crwdne1966:0", "registration": "crwdns1968:0crwdne1968:0", "admin_area": "crwdns1970:0crwdne1970:0", "users": "crwdns1972:0crwdne1972:0", "applicant_questions": "crwdns1974:0crwdne1974:0", "test_questions": "crwdns1976:0crwdne1976:0", "user_questions": "crwdns1978:0crwdne1978:0", "edit_user": "crwdns1980:0crwdne1980:0", "pricing": "crwdns1982:0crwdne1982:0"}, "homepage": {"title": "crwdns1984:0crwdne1984:0", "subtitle": "crwdns3700:0crwdne3700:0", "start_test": "crwdns1988:0crwdne1988:0", "become_tutor": "crwdns1990:0crwdne1990:0", "become_a_homework_hero": "crwdns3708:0crwdne3708:0", "become_student": "crwdns1992:0crwdne1992:0", "get_started": "crwdns1994:0crwdne1994:0", "learn_more": "crwdns1996:0crwdne1996:0", "features": "crwdns1998:0crwdne1998:0", "testimonials": "crwdns2000:0crwdne2000:0", "faq": "crwdns2002:0crwdne2002:0", "contact": "crwdns2004:0crwdne2004:0", "about": "crwdns2006:0crwdne2006:0", "privacy": "crwdns2008:0crwdne2008:0", "terms": "crwdns2010:0crwdne2010:0", "questions": "crwdns2012:0crwdne2012:0", "get_help_feedback": "crwdns2014:0crwdne2014:0", "contact_us": "crwdns2016:0crwdne2016:0", "copyright": "crwdns2018:0crwdne2018:0", "subjects": "crwdns2020:0crwdne2020:0", "pricing": "crwdns2022:0crwdne2022:0", "how_it_works": "crwdns2024:0crwdne2024:0", "homework_help_for_grades_1-8": "crwdns2026:0crwdne2026:0", "subjects_description": "crwdns2028:0crwdne2028:0", "how_it_works_description": "crwdns2030:0crwdne2030:0", "become_tutor_description": "crwdns2032:0crwdne2032:0", "step_1_title": "crwdns2034:0crwdne2034:0", "step_1_description": "crwdns2036:0crwdne2036:0", "step_2_title": "crwdns2038:0crwdne2038:0", "step_2_description": "crwdns2040:0crwdne2040:0", "step_3_title": "crwdns2042:0crwdne2042:0", "step_3_description": "crwdns2044:0crwdne2044:0", "recent_questions_title": "crwdns2046:0crwdne2046:0", "recent_questions_description": "crwdns2048:0crwdne2048:0", "sample_questions": {"question_1": "crwdns2050:0crwdne2050:0", "answer_1": "crwdns2052:0crwdne2052:0", "subject_1": "crwdns2054:0crwdne2054:0", "question_2": "crwdns2056:0crwdne2056:0", "answer_2": "crwdns2058:0crwdne2058:0", "subject_2": "crwdns2060:0crwdne2060:0", "question_3": "crwdns2062:0crwdne2062:0", "answer_3": "crwdns2064:0crwdne2064:0", "subject_3": "crwdns2066:0crwdne2066:0", "question_4": "crwdns2068:0crwdne2068:0", "answer_4": "crwdns2070:0crwdne2070:0", "subject_4": "crwdns2072:0crwdne2072:0", "question_5": "crwdns2074:0crwdne2074:0", "answer_5": "crwdns2076:0crwdne2076:0", "subject_5": "crwdns2078:0crwdne2078:0", "question_6": "crwdns2080:0crwdne2080:0", "answer_6": "crwdns2082:0crwdne2082:0", "subject_6": "crwdns2084:0crwdne2084:0"}, "meet_tutors_title": "crwdns2086:0crwdne2086:0", "meet_tutors_highlight": "crwdns2088:0crwdne2088:0", "meet_tutors_description": "crwdns2090:0crwdne2090:0", "tutor_info": {"name": "crwdns2092:0crwdne2092:0", "degree": "crwdns2094:0crwdne2094:0", "title": "crwdns2096:0crwdne2096:0", "location": "crwdns2098:0crwdne2098:0", "certification": "crwdns2100:0crwdne2100:0", "experience": "crwdns2102:0crwdne2102:0", "answers_this_month": "crwdns2104:0crwdne2104:0"}}, "certificate": {"verify_certificate": "crwdns2106:0crwdne2106:0", "certificate_heading": "crwdns2108:0crwdne2108:0", "enter_certificate_number": "crwdns2110:0crwdne2110:0", "official_certificate": "crwdns2112:0crwdne2112:0", "certificate_number": "crwdns2114:0crwdne2114:0", "certificate_info": "crwdns2116:0crwdne2116:0", "invalid_certificate": "crwdns2118:0crwdne2118:0", "certificate_verified": "crwdns2120:0crwdne2120:0", "certificate_not_found": "crwdns2122:0crwdne2122:0", "certificate_expired": "crwdns2124:0crwdne2124:0", "certificate_invalid": "crwdns2126:0crwdne2126:0"}, "exam": {"welcome_title": "crwdns2128:0crwdne2128:0", "exam_details": "crwdns2130:0crwdne2130:0", "time_limit": "crwdns2132:0crwdne2132:0", "certificate_available": "crwdns2134:0crwdne2134:0", "things_to_remember": "crwdns2136:0crwdne2136:0", "complete_one_session": "crwdns2138:0crwdne2138:0", "close_window_warning": "crwdns2140:0crwdne2140:0", "retake_available": "crwdns2142:0crwdne2142:0", "quit_test": "crwdns2144:0crwdne2144:0", "quit_test_content": "crwdns2146:0crwdne2146:0", "quit_test_confirm": "crwdns2148:0crwdne2148:0", "invalid_exam": "crwdns2150:0crwdne2150:0", "take_test": "crwdns2152:0crwdne2152:0", "test_language": "crwdns2154:0crwdne2154:0", "score": "crwdns2156:0crwdne2156:0", "action": "crwdns2158:0crwdne2158:0", "date": "crwdns2160:0crwdne2160:0"}, "profile": {"personal_info": "crwdns2162:0crwdne2162:0", "general_info": "crwdns2164:0crwdne2164:0", "user_details": "crwdns2166:0crwdne2166:0", "email_address": "crwdns2168:0crwdne2168:0", "phone_number": "crwdns2170:0crwdne2170:0", "date_of_birth": "crwdns2172:0crwdne2172:0", "education_level": "crwdns2174:0crwdne2174:0", "tutor_experience": "crwdns2176:0crwdne2176:0", "work_title": "crwdns2178:0crwdne2178:0", "billing_address": "crwdns2180:0crwdne2180:0", "certificate_number": "crwdns2182:0crwdne2182:0", "save_changes": "crwdns2184:0crwdne2184:0", "profile_photo": "crwdns2186:0crwdne2186:0", "upload_photo": "crwdns2188:0crwdne2188:0", "upload_image": "crwdns2190:0crwdne2190:0", "take_with_webcam": "crwdns2192:0crwdne2192:0", "webcam": "crwdns2194:0crwdne2194:0", "webcam_content": "crwdns2196:0crwdne2196:0", "capture_photo": "crwdns2198:0crwdne2198:0"}, "payment": {"payment_info": "crwdns2200:0crwdne2200:0", "payment_method": "crwdns2202:0crwdne2202:0", "country_region": "crwdns2204:0crwdne2204:0", "credit_debit_card": "crwdns2206:0crwdne2206:0", "name_on_card": "crwdns2208:0crwdne2208:0", "card_number": "crwdns2210:0crwdne2210:0", "expiration_date": "crwdns2212:0crwdne2212:0", "cvc": "crwdns2214:0crwdne2214:0", "buy_now": "crwdns2216:0crwdne2216:0", "total": "crwdns2218:0crwdne2218:0", "processing": "crwdns2220:0crwdne2220:0", "financial_account_helper": "crwdns2222:0crwdne2222:0", "name_on_card_placeholder": "crwdns2224:0crwdne2224:0", "credits_never_expire": "crwdns2226:0crwdne2226:0", "satisfaction_guaranteed": "crwdns2228:0crwdne2228:0", "tutor_access_24_7": "crwdns2230:0crwdne2230:0"}, "admin": {"dashboard": "crwdns2232:0crwdne2232:0", "users": "crwdns2234:0crwdne2234:0", "questions": "crwdns2236:0crwdne2236:0", "transactions": "crwdns2238:0crwdne2238:0", "settings": "crwdns2240:0crwdne2240:0", "add_question": "crwdns2242:0crwdne2242:0", "edit_question": "crwdns2244:0crwdne2244:0", "delete_question": "crwdns2246:0crwdne2246:0", "view_user": "crwdns2248:0crwdne2248:0", "edit_user": "crwdns2250:0crwdne2250:0", "delete_user": "crwdns2252:0crwdne2252:0", "approve_user": "crwdns2254:0crwdne2254:0", "reject_user": "crwdns2256:0crwdne2256:0", "user_type": "crwdns2258:0crwdne2258:0", "user": "crwdns2260:0crwdne2260:0", "tutor": "crwdns2262:0crwdne2262:0", "admin": "crwdns2264:0crwdne2264:0", "applicant": "crwdns2266:0crwdne2266:0", "status": "crwdns2268:0crwdne2268:0", "active": "crwdns2270:0crwdne2270:0", "inactive": "crwdns2272:0crwdne2272:0", "pending": "crwdns2274:0crwdne2274:0", "approved": "crwdns2276:0crwdne2276:0", "rejected": "crwdns2278:0crwdne2278:0", "date_joined": "crwdns2280:0crwdne2280:0", "last_login": "crwdns2282:0crwdne2282:0", "total_users": "crwdns2284:0crwdne2284:0", "total_tutors": "crwdns2286:0crwdne2286:0", "total_questions": "crwdns2288:0crwdne2288:0", "total_earnings": "crwdns2290:0crwdne2290:0", "total_applicants": "crwdns2292:0crwdne2292:0", "student_questions": "crwdns2294:0crwdne2294:0", "question": "crwdns2296:0crwdne2296:0", "question_id": "crwdns2298:0crwdne2298:0", "answered": "crwdns2300:0crwdne2300:0", "link": "crwdns2302:0crwdne2302:0", "id": "crwdns2304:0crwdne2304:0", "correct": "crwdns2306:0crwdne2306:0", "first_name": "crwdns2308:0crwdne2308:0", "last_name": "crwdns2310:0crwdne2310:0", "enter_your_first_name": "crwdns2312:0crwdne2312:0", "enter_your_last_name": "crwdns2314:0crwdne2314:0", "email_required": "crwdns2316:0crwdne2316:0", "invalid_email": "crwdns2318:0crwdne2318:0"}, "tutor": {"dashboard": "crwdns2320:0crwdne2320:0", "earnings": "crwdns2322:0crwdne2322:0", "earnings_description": "crwdns2324:0crwdne2324:0", "history": "crwdns2326:0crwdne2326:0", "payout_method": "crwdns2328:0crwdne2328:0", "settings": "crwdns2330:0crwdne2330:0", "registration": "crwdns2332:0crwdne2332:0", "profile": "crwdns2334:0crwdne2334:0", "balance": "crwdns2336:0crwdne2336:0", "withdraw": "crwdns2338:0crwdne2338:0", "payout_history": "crwdns2340:0crwdne2340:0", "question_history": "crwdns2342:0crwdne2342:0", "answer_question": "crwdns2344:0crwdne2344:0", "view_question": "crwdns2346:0crwdne2346:0", "submit_answer": "crwdns2348:0crwdne2348:0", "answer_text": "crwdns2350:0crwdne2350:0", "upload_image": "crwdns2352:0crwdne2352:0", "earnings_per_question": "crwdns2354:0crwdne2354:0", "total_questions_answered": "crwdns2356:0crwdne2356:0", "total_earnings": "crwdns2358:0crwdne2358:0", "available_balance": "crwdns2360:0crwdne2360:0", "withdraw_funds": "crwdns2362:0crwdne2362:0", "pending_withdrawals": "crwdns2364:0crwdne2364:0", "transaction": "crwdns2366:0crwdne2366:0", "transactions": "crwdns2368:0crwdne2368:0", "active_withdraw_method": "crwdns2370:0crwdne2370:0", "withdraw_methods": "crwdns2372:0crwdne2372:0", "setup_payout_method": "crwdns2374:0crwdne2374:0", "uk_bank_account": "crwdns2376:0crwdne2376:0", "us_bank_account": "crwdns2378:0crwdne2378:0", "change_method": "crwdns2380:0crwdne2380:0", "add_withdraw_method": "crwdns2382:0crwdne2382:0", "no_earnings_data": "crwdns2384:0crwdne2384:0", "confirm_withdrawal": "crwdns2386:0crwdne2386:0", "confirm_withdrawal_amount": "crwdns2388:0{{amount}}crwdne2388:0", "yes_withdraw": "crwdns2390:0crwdne2390:0", "payment_sent": "crwdns2392:0crwdne2392:0", "payment_available_shortly": "crwdns2394:0crwdne2394:0", "no_payout_method_added": "crwdns2396:0crwdne2396:0", "add_payout_method": "crwdns2398:0crwdne2398:0", "no_balance_to_withdraw": "crwdns2400:0crwdne2400:0", "withdrawal_pending_approval": "crwdns2402:0crwdne2402:0", "withdrawal_submit_failed": "crwdns2404:0crwdne2404:0", "withdrawal_error": "crwdns2406:0crwdne2406:0", "withdrawal_failed_wise": "crwdns2408:0crwdne2408:0", "name_mismatch_error": "crwdns2410:0crwdne2410:0", "withdrawal_failed_support": "crwdns2412:0crwdne2412:0", "pending_withdrawal_amount": "crwdns2414:0crwdne2414:0", "bank_account": "crwdns2416:0crwdne2416:0", "paypal": "crwdns2418:0crwdne2418:0", "wise": "crwdns2420:0crwdne2420:0", "account_number": "crwdns2422:0crwdne2422:0", "routing_number": "crwdns2424:0crwdne2424:0", "iban": "crwdns2426:0crwdne2426:0", "sort_code": "crwdns2428:0crwdne2428:0"}, "student": {"dashboard": "crwdns2430:0crwdne2430:0", "my_questions": "crwdns2432:0crwdne2432:0", "register_as_a_tutor": "crwdns2434:0crwdne2434:0", "ask_question": "crwdns2436:0crwdne2436:0", "ask_question_description": "crwdns2438:0crwdne2438:0", "question_history": "crwdns2440:0crwdne2440:0", "packs": "crwdns2442:0crwdne2442:0", "pack": "crwdns2444:0crwdne2444:0", "question_pack": "crwdns2446:0crwdne2446:0", "buy_pack": "crwdns2448:0crwdne2448:0", "pack_details": "crwdns2450:0crwdne2450:0", "questions_in_pack": "crwdns2452:0crwdne2452:0", "price_per_question": "crwdns2454:0crwdne2454:0", "total_price": "crwdns2456:0crwdne2456:0", "purchase_pack": "crwdns2458:0crwdne2458:0", "purchase_pack_message": "crwdns2460:0crwdne2460:0", "question_text": "crwdns2462:0crwdne2462:0", "upload_image": "crwdns2464:0crwdne2464:0", "what_need_help_with": "crwdns2466:0crwdne2466:0", "question_placeholder": "crwdns2468:0crwdne2468:0", "see_sample_question_see_an": "crwdns3704:0crwdne3704:0", "see_sample_question": "crwdns3706:0crwdne3706:0", "submit_question": "crwdns2472:0crwdne2472:0", "sample_question": "crwdns2474:0crwdne2474:0", "sample_question_description": "crwdns2476:0crwdne2476:0", "subject": "crwdns2478:0crwdne2478:0", "subject_math": "crwdns2480:0crwdne2480:0", "subject_science": "crwdns2482:0crwdne2482:0", "subject_language_arts": "crwdns2484:0crwdne2484:0", "subject_social_studies": "crwdns2486:0crwdne2486:0", "subject_other": "crwdns2488:0crwdne2488:0", "priority": "crwdns2490:0crwdne2490:0", "priority_low": "crwdns2492:0crwdne2492:0", "priority_medium": "crwdns2494:0crwdne2494:0", "priority_high": "crwdns2496:0crwdne2496:0", "priority_urgent": "crwdns2498:0crwdne2498:0", "question_status": "crwdns2500:0crwdne2500:0", "status_pending": "crwdns2502:0crwdne2502:0", "status_answered": "crwdns2504:0crwdne2504:0", "status_cancelled": "crwdns2506:0crwdne2506:0", "asked_by": "crwdns2508:0crwdne2508:0", "answered_by": "crwdns2510:0crwdne2510:0", "asked_date": "crwdns2512:0crwdne2512:0", "answered_date": "crwdns2514:0crwdne2514:0", "ask": "crwdns2516:0crwdne2516:0", "history": "crwdns2518:0crwdne2518:0", "buy": "crwdns2520:0crwdne2520:0", "settings": "crwdns2522:0crwdne2522:0", "transactions": "crwdns2524:0crwdne2524:0"}, "notifications": {"question_answered": "crwdns2526:0crwdne2526:0", "new_question": "crwdns2528:0crwdne2528:0", "payment_successful": "crwdns2530:0crwdne2530:0", "payment_failed": "crwdns2532:0crwdne2532:0", "withdrawal_successful": "crwdns2534:0crwdne2534:0", "withdrawal_failed": "crwdns2536:0crwdne2536:0", "profile_updated": "crwdns2538:0crwdne2538:0", "password_changed": "crwdns2540:0crwdne2540:0", "email_sent": "crwdns2542:0crwdne2542:0", "error_occurred": "crwdns2544:0crwdne2544:0", "success": "crwdns2546:0crwdne2546:0", "warning": "crwdns2548:0crwdne2548:0", "info": "crwdns2550:0crwdne2550:0"}, "errors": {"network_error": "crwdns2552:0crwdne2552:0", "permission_error": "crwdns2554:0crwdne2554:0", "timeout_error": "crwdns2556:0crwdne2556:0", "quota_error": "crwdns2558:0crwdne2558:0", "file_size_error": "crwdns2560:0crwdne2560:0", "file_type_error": "crwdns2562:0crwdne2562:0", "upload_failed": "crwdns2564:0crwdne2564:0", "download_failed": "crwdns2566:0crwdne2566:0", "save_failed": "crwdns2568:0crwdne2568:0", "delete_failed": "crwdns2570:0crwdne2570:0", "operation_failed": "crwdns2572:0crwdne2572:0", "unknown_error": "crwdns2574:0crwdne2574:0"}, "subjects": {"science": "crwdns2576:0crwdne2576:0", "math": "crwdns2578:0crwdne2578:0", "language_arts": "crwdns2580:0crwdne2580:0", "social_studies": "crwdns2582:0crwdne2582:0", "grade_range": "crwdns2584:0crwdne2584:0"}, "meet_tutors": {"title_main": "crwdns2586:0crwdne2586:0", "heroes": "crwdns2588:0crwdne2588:0", "intro_1": "crwdns2590:0crwdne2590:0", "intro_2": "crwdns2592:0crwdne2592:0", "answers_this_month": "crwdns2594:0crwdne2594:0", "degree": "crwdns2596:0{{degree}}crwdne2596:0", "title": "crwdns2598:0{{title}}crwdne2598:0", "location": "crwdns2600:0{{location}}crwdne2600:0", "certification": "crwdns2602:0{{certification}}crwdne2602:0", "experience": "crwdns2604:0{{experience}}crwdne2604:0", "prev_tutor": "crwdns2606:0crwdne2606:0", "next_tutor": "crwdns2608:0crwdne2608:0", "prev": "crwdns2610:0crwdne2610:0", "next": "crwdns2612:0crwdne2612:0"}, "faq_section": {"q1": "crwdns2614:0crwdne2614:0", "a1": "crwdns2616:0crwdne2616:0", "q2": "crwdns2618:0crwdne2618:0", "a2": "crwdns2620:0crwdne2620:0", "q3": "crwdns2622:0crwdne2622:0", "a3": "crwdns2624:0crwdne2624:0", "q4": "crwdns2626:0crwdne2626:0", "a4": "crwdns2628:0crwdne2628:0", "q5": "crwdns2630:0crwdne2630:0", "a5": "crwdns2632:0crwdne2632:0", "q6": "crwdns2634:0crwdne2634:0", "a6": "crwdns2636:0crwdne2636:0"}, "pricing_section": {"title": "crwdns2638:0crwdne2638:0", "subtitle": "crwdns2640:0crwdne2640:0", "questions": "crwdns2642:0crwdne2642:0", "popular": "crwdns2644:0crwdne2644:0", "math": "crwdns2646:0crwdne2646:0", "language_arts": "crwdns2648:0crwdne2648:0", "science": "crwdns2650:0crwdne2650:0", "social_studies": "crwdns2652:0crwdne2652:0", "buy_now": "crwdns2654:0crwdne2654:0", "feature_1": "crwdns2656:0crwdne2656:0", "feature_2": "crwdns2658:0crwdne2658:0", "feature_3": "crwdns2660:0crwdne2660:0"}, "top_grade_section": {"study_smarter_not_harder": "crwdns2662:0crwdne2662:0", "ask_experts_not_robots": "crwdns2664:0crwdne2664:0", "ai_bots_unreliable_description": "crwdns2666:0crwdne2666:0", "blast_roadblocks": "crwdns2668:0crwdne2668:0", "waste_hours_description": "crwdns2670:0crwdne2670:0", "peak_time_power": "crwdns2672:0crwdne2672:0", "brain_ready_description": "crwdns2674:0crwdne2674:0", "get_started_button": "crwdns2676:0crwdne2676:0"}, "WhyLoveSection": {"sectionTitle": "crwdns2678:0crwdne2678:0", "sectionTitleHighlight": "crwdns2680:0crwdne2680:0", "items": [{"title": "crwdns2682:0crwdne2682:0"}, {"title": "crwdns2684:0crwdne2684:0"}, {"title": "crwdns2686:0crwdne2686:0"}, {"title": "crwdns2688:0crwdne2688:0"}]}, "admin_add_question": {"question_label": "crwdns2690:0crwdne2690:0", "answer_1_label": "crwdns2692:0crwdne2692:0", "answer_2_label": "crwdns2694:0crwdne2694:0", "answer_3_label": "crwdns2696:0crwdne2696:0", "answer_4_label": "crwdns2698:0crwdne2698:0", "error_no_answer": "crwdns2700:0crwdne2700:0", "cancel_button": "crwdns2702:0crwdne2702:0", "add_question_button": "crwdns2704:0crwdne2704:0", "save_question_button": "crwdns2706:0crwdne2706:0", "confirm_modal_title": "crwdns2708:0crwdne2708:0", "confirm_modal_content": "crwdns2710:0{{count}}crwdne2710:0", "close_button": "crwdns2712:0crwdne2712:0", "add_another_button": "crwdns2714:0crwdne2714:0"}, "admin_add_question_notify": {"question_counter_invalid": "crwdns2716:0crwdne2716:0", "question_required": "crwdns2718:0crwdne2718:0", "answer1_required": "crwdns2720:0crwdne2720:0", "answer2_required": "crwdns2722:0crwdne2722:0", "answer3_required": "crwdns2724:0crwdne2724:0", "answer4_required": "crwdns2726:0crwdne2726:0", "failed_update_counter": "crwdns2728:0crwdne2728:0"}, "admin_question": {"student_questions_title": "crwdns2730:0crwdne2730:0", "history_title": "crwdns2732:0crwdne2732:0", "help_description": "crwdns2734:0crwdne2734:0", "search_placeholder_admin": "crwdns2736:0crwdne2736:0", "search_placeholder_user": "crwdns2738:0crwdne2738:0", "answered_button": "crwdns2740:0crwdne2740:0", "not_answered_button": "crwdns2742:0crwdne2742:0", "asked_by_label": "crwdns2744:0crwdne2744:0", "date_asked_label": "crwdns2746:0crwdne2746:0", "asked_label": "crwdns2748:0crwdne2748:0", "answered_label": "crwdns2750:0crwdne2750:0", "feedback_button": "crwdns2752:0crwdne2752:0", "answer_label": "crwdns2754:0crwdne2754:0", "loading_text": "crwdns2756:0crwdne2756:0", "delete_question_button": "crwdns2758:0crwdne2758:0", "no_question_found": "crwdns2760:0crwdne2760:0", "no_questions_submitted": "crwdns2762:0crwdne2762:0", "help_email_title": "crwdns2764:0crwdne2764:0", "help_email_content": "crwdns2766:0crwdne2766:0", "delete_confirm_title": "crwdns2768:0crwdne2768:0", "delete_confirm_content": "crwdns2770:0crwdne2770:0", "cancel_button": "crwdns2772:0crwdne2772:0", "yes_delete_button": "crwdns2774:0crwdne2774:0"}, "admin_edit_user_profile": {"date_column": "crwdns2776:0crwdne2776:0", "id_column": "crwdns2778:0crwdne2778:0", "correct_column": "crwdns2780:0crwdne2780:0", "amount_column": "crwdns2782:0crwdne2782:0", "link_column": "crwdns2784:0crwdne2784:0", "acceptance_rate_label": "crwdns2786:0crwdne2786:0", "students_helped_label": "crwdns2788:0crwdne2788:0", "top_subject_label": "crwdns2790:0crwdne2790:0", "daily_streak_label": "crwdns2792:0crwdne2792:0", "monthly_rank_label": "crwdns2794:0crwdne2794:0", "monthly_answers_label": "crwdns2796:0crwdne2796:0", "acceptance_rate_placeholder": "crwdns2798:0crwdne2798:0", "students_helped_placeholder": "crwdns2800:0crwdne2800:0", "top_subject_placeholder": "crwdns2802:0crwdne2802:0", "daily_streak_placeholder": "crwdns2804:0crwdne2804:0", "monthly_rank_placeholder": "crwdns2806:0crwdne2806:0", "monthly_answers_placeholder": "crwdns2808:0crwdne2808:0", "maths_option": "crwdns2810:0crwdne2810:0", "reading_option": "crwdns2812:0crwdne2812:0", "writing_option": "crwdns2814:0crwdne2814:0", "science_option": "crwdns2816:0crwdne2816:0", "answer_apprentice_option": "crwdns2818:0crwdne2818:0", "homework_hero_option": "crwdns2820:0crwdne2820:0", "knowledge_knight_option": "crwdns2822:0crwdne2822:0", "wisdom_wizard_option": "crwdns2824:0crwdne2824:0", "updating_button": "crwdns2826:0crwdne2826:0", "update_button": "crwdns2828:0crwdne2828:0", "locked_option": "crwdns2830:0crwdne2830:0", "unlocked_option": "crwdns2832:0crwdne2832:0", "approve_button": "crwdns2834:0crwdne2834:0", "deny_button": "crwdns2836:0crwdne2836:0", "update_user_details_title": "crwdns2838:0crwdne2838:0", "first_name_placeholder": "crwdns2840:0crwdne2840:0", "last_name_placeholder": "crwdns2842:0crwdne2842:0", "email_address_label": "crwdns2844:0crwdne2844:0", "email_placeholder": "crwdns2846:0crwdne2846:0", "tutor_certification_label": "crwdns2848:0crwdne2848:0", "certification_placeholder": "crwdns2850:0crwdne2850:0", "credit_amount_label": "crwdns2852:0crwdne2852:0", "deduct_amount_label": "crwdns2854:0crwdne2854:0", "update_profile_button": "crwdns2856:0crwdne2856:0", "approve_applicant_title": "crwdns2858:0crwdne2858:0", "deny_applicant_title": "crwdns2860:0crwdne2860:0", "irreversible_action_content": "crwdns2862:0crwdne2862:0", "delete_user_title": "crwdns2864:0crwdne2864:0", "delete_user_content": "crwdns2866:0crwdne2866:0", "delete_user_button": "crwdns2868:0crwdne2868:0", "unarchive_user_title": "crwdns2870:0crwdne2870:0", "archive_user_title": "crwdns2872:0crwdne2872:0", "unarchive_user_content": "crwdns2874:0crwdne2874:0", "archive_user_content": "crwdns2876:0crwdne2876:0", "unarchive_button": "crwdns2878:0crwdne2878:0", "archive_button": "crwdns2880:0crwdne2880:0"}, "admin_edit_user_profile_notify": {"user_not_found": "crwdns2882:0crwdne2882:0", "error_fetching_user": "crwdns2884:0crwdne2884:0", "error_fetching_applicant_answers": "crwdns2886:0crwdne2886:0", "no_changes": "crwdns2888:0crwdne2888:0", "profile_updated": "crwdns2890:0crwdne2890:0", "failed_update": "crwdns2892:0crwdne2892:0", "error_updating": "crwdns2894:0crwdne2894:0", "applicant_approved": "crwdns2896:0crwdne2896:0", "failed_approve": "crwdns2898:0crwdne2898:0", "error_approving": "crwdns2900:0crwdne2900:0", "applicant_denied": "crwdns2902:0crwdne2902:0", "failed_deny": "crwdns2904:0crwdne2904:0", "error_denying": "crwdns2906:0crwdne2906:0", "archived": "crwdns2908:0crwdne2908:0", "failed_archive": "crwdns2910:0crwdne2910:0", "error_archiving": "crwdns2912:0crwdne2912:0", "restored": "crwdns2914:0crwdne2914:0", "failed_restore": "crwdns2916:0crwdne2916:0", "error_restoring": "crwdns2918:0crwdne2918:0", "deleted": "crwdns2920:0crwdne2920:0", "failed_delete": "crwdns2922:0crwdne2922:0", "error_deleting": "crwdns2924:0crwdne2924:0", "error_uploading_profile_image": "crwdns2926:0crwdne2926:0", "failed_to_upload_profile_image": "crwdns2928:0crwdne2928:0", "statistics_updated": "crwdns2930:0crwdne2930:0", "failed_update_statistics": "crwdns2932:0crwdne2932:0", "achievements_updated": "crwdns2934:0crwdne2934:0", "failed_update_achievements": "crwdns2936:0crwdne2936:0"}, "admin_user_profile": {"admin_label": "crwdns2938:0crwdne2938:0", "user_name": "crwdns2940:0crwdne2940:0", "approve": "crwdns2942:0crwdne2942:0", "deny": "crwdns2944:0crwdne2944:0", "edit_profile": "crwdns2946:0crwdne2946:0", "edit_user_details": "crwdns2948:0crwdne2948:0", "unarchive_account": "crwdns2950:0crwdne2950:0", "archive_account": "crwdns2952:0crwdne2952:0", "delete_account": "crwdns2954:0crwdne2954:0", "email_address": "crwdns2956:0crwdne2956:0", "total_earnings": "crwdns2958:0crwdne2958:0", "joined_date": "crwdns2960:0crwdne2960:0", "total_answers": "crwdns2962:0crwdne2962:0", "question_credits": "crwdns2964:0crwdne2964:0", "total_questions": "crwdns2966:0crwdne2966:0", "payout_request": "crwdns2968:0crwdne2968:0", "date_of_request": "crwdns2970:0crwdne2970:0", "amount_requested": "crwdns2972:0crwdne2972:0", "all_questions": "crwdns2974:0crwdne2974:0", "test_question_answers": "crwdns2976:0crwdne2976:0", "search_by_question_id": "crwdns2978:0crwdne2978:0", "view": "crwdns2980:0crwdne2980:0", "update_user_details": "crwdns2982:0crwdne2982:0", "first_name": "crwdns2984:0crwdne2984:0", "last_name": "crwdns2986:0crwdne2986:0", "phone_number": "crwdns2988:0crwdne2988:0", "tutor_certification_number": "crwdns2990:0crwdne2990:0", "birthday": "crwdns2992:0crwdne2992:0", "current_balance": "crwdns2994:0crwdne2994:0", "credit_amount": "crwdns2996:0crwdne2996:0", "deduct_amount": "crwdns2998:0crwdne2998:0", "unarchive": "crwdns3000:0crwdne3000:0", "archive": "crwdns3002:0crwdne3002:0", "delete": "crwdns3004:0crwdne3004:0", "update": "crwdns3006:0crwdne3006:0", "confirm_delete": "crwdns3008:0crwdne3008:0", "confirm_archive": "crwdns3010:0crwdne3010:0", "confirm_unarchive": "crwdns3012:0crwdne3012:0", "approve_payout_request": "crwdns3014:0crwdne3014:0", "deny_payout_request": "crwdns3016:0crwdne3016:0", "approve_applicant": "crwdns3018:0crwdne3018:0", "deny_applicant": "crwdns3020:0crwdne3020:0", "irreversible_action": "crwdns3022:0crwdne3022:0", "restore_user": "crwdns3024:0crwdne3024:0", "archive_user": "crwdns3026:0crwdne3026:0", "delete_user": "crwdns3028:0crwdne3028:0", "the_user_has_not_yet_answered_a_question": "crwdns3030:0crwdne3030:0", "the_user_has_not_yet_asked_a_question": "crwdns3032:0crwdne3032:0"}, "admin_user_profile_notify": {"withdrawal_approved": "crwdns3034:0crwdne3034:0", "withdrawal_approval_failed": "crwdns3036:0crwdne3036:0", "user_deleted": "crwdns3038:0crwdne3038:0", "user_archived": "crwdns3040:0crwdne3040:0", "user_restored": "crwdns3042:0crwdne3042:0", "applicant_approved": "crwdns3044:0crwdne3044:0", "applicant_denied": "crwdns3046:0crwdne3046:0"}, "setting": {"first_name": "crwdns3048:0crwdne3048:0", "last_name": "crwdns3050:0crwdne3050:0", "email_address": "crwdns3052:0crwdne3052:0", "phone_number": "crwdns3054:0crwdne3054:0", "date_of_birth": "crwdns3056:0crwdne3056:0", "education_level": "crwdns3058:0crwdne3058:0", "tutor_experience": "crwdns3060:0crwdne3060:0", "current_work_title": "crwdns3062:0crwdne3062:0", "country_of_residence": "crwdns3064:0crwdne3064:0", "tutor_certification_number": "crwdns3066:0crwdne3066:0", "current_password": "crwdns3068:0crwdne3068:0", "new_password": "crwdns3070:0crwdne3070:0", "update": "crwdns3072:0crwdne3072:0", "first_name_placeholder": "crwdns3074:0crwdne3074:0", "last_name_placeholder": "crwdns3076:0crwdne3076:0", "email_address_placeholder": "crwdns3078:0crwdne3078:0", "phone_number_placeholder": "crwdns3080:0crwdne3080:0", "work_title_placeholder": "crwdns3082:0crwdne3082:0", "country_placeholder": "crwdns3084:0crwdne3084:0", "education_level_placeholder": "crwdns3086:0crwdne3086:0", "tutor_experience_placeholder": "crwdns3088:0crwdne3088:0", "certificate_number_placeholder": "crwdns3090:0crwdne3090:0", "current_password_placeholder": "crwdns3092:0crwdne3092:0", "new_password_placeholder": "crwdns3094:0crwdne3094:0", "month_placeholder": "crwdns3096:0crwdne3096:0", "day_placeholder": "crwdns3098:0crwdne3098:0", "year_placeholder": "crwdns3100:0crwdne3100:0", "lets_get_started": "crwdns3102:0crwdne3102:0", "keep_going": "crwdns3104:0crwdne3104:0", "making_progress": "crwdns3106:0crwdne3106:0", "almost_done": "crwdns3108:0crwdne3108:0", "all_set": "crwdns3110:0crwdne3110:0", "current_student": "crwdns3112:0crwdne3112:0", "high_school": "crwdns3114:0crwdne3114:0", "bachelors_degree": "crwdns3116:0crwdne3116:0", "masters_degree": "crwdns3118:0crwdne3118:0", "phd": "crwdns3120:0crwdne3120:0", "0_years": "crwdns3122:0crwdne3122:0", "1_year": "crwdns3124:0crwdne3124:0", "2_years": "crwdns3126:0crwdne3126:0", "3_years": "crwdns3128:0crwdne3128:0", "4_years": "crwdns3130:0crwdne3130:0", "5_plus_years": "crwdns3132:0crwdne3132:0", "january": "crwdns3134:0crwdne3134:0", "february": "crwdns3136:0crwdne3136:0", "march": "crwdns3138:0crwdne3138:0", "april": "crwdns3140:0crwdne3140:0", "may": "crwdns3142:0crwdne3142:0", "june": "crwdns3144:0crwdne3144:0", "july": "crwdns3146:0crwdne3146:0", "august": "crwdns3148:0crwdne3148:0", "september": "crwdns3150:0crwdne3150:0", "october": "crwdns3152:0crwdne3152:0", "november": "crwdns3154:0crwdne3154:0", "december": "crwdns3156:0crwdne3156:0", "tutor_certificate": "crwdns3158:0crwdne3158:0", "tutor_certificate_notification_message": "crwdns3160:0crwdne3160:0"}, "setting_notify": {"invalid_certificate": "crwdns3162:0crwdne3162:0", "failed_validate_certificate": "crwdns3164:0crwdne3164:0", "failed_update_profile": "crwdns3166:0crwdne3166:0", "profile_updated": "crwdns3168:0crwdne3168:0", "failed_update": "crwdns3170:0crwdne3170:0", "error_updating": "crwdns3172:0crwdne3172:0", "no_changes": "crwdns3174:0crwdne3174:0", "required_field": "crwdns3176:0crwdne3176:0", "invalid_email": "crwdns3178:0crwdne3178:0", "error_occurred": "crwdns3180:0crwdne3180:0", "new_password_must_be_at_least_8_characters": "crwdns3182:0crwdne3182:0", "current_password_is_required": "crwdns3184:0crwdne3184:0", "please_enter_a_new_password_or_clear_the_current_password_field": "crwdns3186:0crwdne3186:0", "the_current_password_you_entered_is_incorrect": "crwdns3188:0crwdne3188:0"}, "password_field": {"must_8_characters": "crwdns3190:0crwdne3190:0"}, "modal_sample_question_modal": {"sample_text": "crwdns3192:0crwdne3192:0", "close": "crwdns3194:0crwdne3194:0"}, "modal_set_price_modal": {"title": "crwdns3196:0crwdne3196:0", "current_price": "crwdns3198:0{{price}}crwdne3198:0", "new_price_label": "crwdns3200:0crwdne3200:0", "cancel": "crwdns3202:0crwdne3202:0", "save": "crwdns3204:0crwdne3204:0", "success": "crwdns3206:0crwdne3206:0", "error_no_price": "crwdns3208:0crwdne3208:0"}, "modal_view_question_modal": {"title": "crwdns3210:0crwdne3210:0", "question": "crwdns3212:0crwdne3212:0", "applicants_answer": "crwdns3214:0crwdne3214:0", "close": "crwdns3216:0crwdne3216:0"}, "error": {"signout_failed": "crwdns3218:0crwdne3218:0", "unknown": "crwdns3220:0crwdne3220:0"}, "navbar_legal": {"login": "crwdns3222:0crwdne3222:0", "terms": "crwdns3224:0crwdne3224:0", "privacy": "crwdns3226:0crwdne3226:0", "help": "crwdns3228:0crwdne3228:0", "registerAsTutor": "crwdns3230:0crwdne3230:0", "back": "crwdns3232:0crwdne3232:0"}, "navigation_tabs_admin": {"dashboard": "crwdns3234:0crwdne3234:0", "users": "crwdns3236:0crwdne3236:0", "test_questions": "crwdns3238:0crwdne3238:0", "student_questions": "crwdns3240:0crwdne3240:0", "settings": "crwdns3242:0crwdne3242:0"}, "navigation_tabs_legal": {"terms": "crwdns3244:0crwdne3244:0", "privacy": "crwdns3246:0crwdne3246:0", "help": "crwdns3248:0crwdne3248:0"}, "payment_form": {"country_region": "crwdns3250:0crwdne3250:0", "select_region": "crwdns3252:0crwdne3252:0", "helper_text": "crwdns3254:0crwdne3254:0", "credit_or_debit_card": "crwdns3256:0crwdne3256:0", "payment_method": "crwdns3258:0crwdne3258:0", "name_on_card": "crwdns3260:0crwdne3260:0", "card_number": "crwdns3262:0crwdne3262:0", "expiration_date": "crwdns3264:0crwdne3264:0", "cvc": "crwdns3266:0crwdne3266:0", "processing": "crwdns3268:0crwdne3268:0", "buy_now": "crwdns3270:0crwdne3270:0", "error_adding_transaction": "crwdns3272:0crwdne3272:0", "user_object_missing": "crwdns3274:0crwdne3274:0", "user_error_notify": "crwdns3276:0crwdne3276:0", "payment_failed": "crwdns3278:0crwdne3278:0", "payment_processing_error": "crwdns3280:0crwdne3280:0", "payment_error_notify": "crwdns3282:0crwdne3282:0"}, "student_payment_billing": {"title": "crwdns3284:0crwdne3284:0", "subtitle": "crwdns3286:0crwdne3286:0", "tab_purchases": "crwdns3288:0crwdne3288:0", "tab_questions": "crwdns3290:0crwdne3290:0", "no_packs_purchased": "crwdns3292:0crwdne3292:0", "no_questions_or_credits": "crwdns3294:0crwdne3294:0", "no_data_found": "crwdns3296:0crwdne3296:0"}, "tutor_history": {"title": "crwdns3298:0crwdne3298:0", "subtitle": "crwdns3300:0crwdne3300:0", "no_questions": "crwdns3302:0crwdne3302:0", "question_id": "crwdns3304:0crwdne3304:0", "asked": "crwdns3306:0crwdne3306:0", "answered": "crwdns3308:0crwdne3308:0", "feedback": "crwdns3310:0crwdne3310:0", "answer": "crwdns3312:0crwdne3312:0", "loading": "crwdns3314:0crwdne3314:0", "notify_title": "crwdns3316:0crwdne3316:0", "notify_content": "crwdns3318:0crwdne3318:0"}, "tutor_payout": {"title": "crwdns3320:0crwdne3320:0", "subtitle": "crwdns3322:0crwdne3322:0", "billing_country": "crwdns3324:0crwdne3324:0", "billing_country_helper": "crwdns3326:0crwdne3326:0", "wire_transfer": "crwdns3328:0crwdne3328:0", "wire_transfer_fee": "crwdns3330:0crwdne3330:0", "wire_transfer_time": "crwdns3332:0crwdne3332:0", "disconnect": "crwdns3334:0crwdne3334:0", "connect_bank": "crwdns3336:0crwdne3336:0", "connect_bank_sub": "crwdns3338:0crwdne3338:0", "checking": "crwdns3340:0crwdne3340:0", "saving": "crwdns3342:0crwdne3342:0", "iban": "crwdns3344:0crwdne3344:0", "iban_helper": "crwdns3346:0crwdne3346:0", "account_number": "crwdns3348:0crwdne3348:0", "account_number_helper_uk": "crwdns3350:0crwdne3350:0", "account_number_helper_us": "crwdns3352:0crwdne3352:0", "sort_code": "crwdns3354:0crwdne3354:0", "routing_number": "crwdns3356:0crwdne3356:0", "account_type": "crwdns3358:0crwdne3358:0", "city": "crwdns3360:0crwdne3360:0", "country_code": "crwdns3362:0crwdne3362:0", "state": "crwdns3364:0crwdne3364:0", "postal_code": "crwdns3366:0crwdne3366:0", "postcode": "crwdns3368:0crwdne3368:0", "zipcode": "crwdns3370:0crwdne3370:0", "first_line": "crwdns3372:0crwdne3372:0", "agree_msg": "crwdns3374:0crwdne3374:0", "cancel": "crwdns3376:0crwdne3376:0", "update": "crwdns3378:0crwdne3378:0", "agree_and_link": "crwdns3380:0crwdne3380:0", "confirm_disconnect_title": "crwdns3382:0crwdne3382:0", "confirm_disconnect_content": "crwdns3384:0crwdne3384:0", "confirm_disconnect_action": "crwdns3386:0crwdne3386:0", "notify_iban_required": "crwdns3388:0crwdne3388:0", "notify_account_number_required": "crwdns3390:0crwdne3390:0", "notify_sort_code_required": "crwdns3392:0crwdne3392:0", "notify_routing_number_required": "crwdns3394:0crwdne3394:0", "notify_account_type_required": "crwdns3396:0crwdne3396:0", "notify_city_required": "crwdns3398:0crwdne3398:0", "notify_country_code_required": "crwdns3400:0crwdne3400:0", "notify_state_required": "crwdns3402:0crwdne3402:0", "notify_postcode_required": "crwdns3404:0crwdne3404:0", "notify_first_line_required": "crwdns3406:0crwdne3406:0", "notify_withdrawal_updated": "crwdns3408:0crwdne3408:0", "notify_withdrawal_added": "crwdns3410:0crwdne3410:0", "notify_withdrawal_disconnected": "crwdns3412:0crwdne3412:0"}, "TutorQuestions": {"answer_questions_title": "crwdns3414:0crwdne3414:0", "answer_questions_desc": "crwdns3416:0crwdne3416:0", "class_dismissed_title": "crwdns3418:0crwdne3418:0", "class_dismissed_desc": "crwdns3420:0crwdne3420:0", "my_profile": "crwdns3422:0crwdne3422:0", "applicant_thank_you": "crwdns3424:0crwdne3424:0", "no_applicant_questions": "crwdns3426:0crwdne3426:0", "no_applicant_questions_desc": "crwdns3428:0crwdne3428:0", "complete_registration": "crwdns3430:0crwdne3430:0", "complete_registration_desc": "crwdns3432:0crwdne3432:0", "first_name": "crwdns3434:0crwdne3434:0", "last_name": "crwdns3436:0crwdne3436:0", "phone_number": "crwdns3438:0crwdne3438:0", "date_of_birth": "crwdns3440:0crwdne3440:0", "certificate_number": "crwdns3442:0crwdne3442:0", "complete_now": "crwdns3444:0crwdne3444:0", "complete_registration_modal_title": "crwdns3446:0crwdne3446:0", "complete_registration_modal_content": "crwdns3448:0crwdne3448:0", "question_id": "crwdns3450:0crwdne3450:0", "feedback": "crwdns3452:0crwdne3452:0", "answer": "crwdns3454:0crwdne3454:0", "write_answer_placeholder": "crwdns3456:0crwdne3456:0", "upload_answer_photo": "crwdns3458:0crwdne3458:0", "skip_question": "crwdns3460:0crwdne3460:0", "submitting": "crwdns3462:0crwdne3462:0", "skipping": "crwdns3464:0crwdne3464:0", "answer_for": "crwdns3466:0{{amount}}crwdne3466:0", "answer_cannot_be_empty": "crwdns3468:0crwdne3468:0", "invalid_question_data": "crwdns3470:0crwdne3470:0", "please_select_answer": "crwdns3472:0crwdne3472:0", "counter": "crwdns3474:0{{count}}crwdne3474:0", "question_cost": "crwdns3476:0{{amount}}crwdne3476:0", "question_id_number": "crwdns3478:0{{id}}crwdne3478:0", "if_you_need_help": "crwdns3480:0crwdne3480:0", "email_support": "crwdns3482:0crwdne3482:0", "skip_question_modal_title": "crwdns3484:0crwdne3484:0", "skip_question_action": "crwdns3486:0crwdne3486:0", "cancel": "crwdns3488:0crwdne3488:0"}, "admin_applicant_questions": {"title_add": "crwdns3490:0crwdne3490:0", "title_edit": "crwdns3492:0crwdne3492:0", "title_main": "crwdns3494:0crwdne3494:0", "total_questions": "crwdns3496:0crwdne3496:0", "total_question": "crwdns3498:0crwdne3498:0", "set_price": "crwdns3500:0crwdne3500:0", "add_a_question": "crwdns3502:0crwdne3502:0", "expand": "crwdns3504:0crwdne3504:0", "collapse": "crwdns3506:0crwdne3506:0", "question_id": "crwdns3508:0crwdne3508:0", "answer": "crwdns3510:0crwdne3510:0", "correct_answer": "crwdns3512:0crwdne3512:0", "delete_question": "crwdns3514:0crwdne3514:0", "edit_question": "crwdns3516:0crwdne3516:0", "delete_confirm_title": "crwdns3518:0crwdne3518:0", "delete_confirm_content": "crwdns3520:0crwdne3520:0", "delete_confirm_action": "crwdns3522:0crwdne3522:0", "delete_cancel": "crwdns3524:0crwdne3524:0", "set_price_title": "crwdns3526:0crwdne3526:0", "set_price_action": "crwdns3528:0crwdne3528:0", "set_price_default": "crwdns3530:0crwdne3530:0", "notify_delete_error": "crwdns3532:0crwdne3532:0", "notify_no_question": "crwdns3534:0crwdne3534:0"}, "admin_edit_user": {"title": "crwdns3536:0crwdne3536:0", "loading": "crwdns3538:0crwdne3538:0", "no_user_selected": "crwdns3540:0crwdne3540:0", "no_user_selected_desc": "crwdns3542:0crwdne3542:0", "go_to_users": "crwdns3544:0crwdne3544:0"}, "admin_edit_user_notify": {"user_not_found": "crwdns3546:0crwdne3546:0", "error_loading": "crwdns3548:0crwdne3548:0", "update_success": "crwdns3550:0crwdne3550:0", "update_error": "crwdns3552:0crwdne3552:0"}, "admin_history": {"title": "crwdns3554:0crwdne3554:0", "expand": "crwdns3556:0crwdne3556:0", "collapse": "crwdns3558:0crwdne3558:0", "question_id": "crwdns3560:0crwdne3560:0", "asked_by": "crwdns3562:0crwdne3562:0", "answered_by": "crwdns3564:0crwdne3564:0", "answer": "crwdns3566:0crwdne3566:0", "delete_question": "crwdns3568:0crwdne3568:0", "no_questions": "crwdns3570:0crwdne3570:0", "loading": "crwdns3572:0crwdne3572:0", "feedback": "crwdns3574:0crwdne3574:0", "back_to_questions": "crwdns3576:0crwdne3576:0", "help_title": "crwdns3578:0crwdne3578:0", "help_content": "crwdns3580:0crwdne3580:0", "delete_confirm_title": "crwdns3582:0crwdne3582:0", "delete_confirm_content": "crwdns3584:0crwdne3584:0", "delete_cancel": "crwdns3586:0crwdne3586:0", "delete_confirm_action": "crwdns3588:0crwdne3588:0"}, "admin_history_notify": {"error_loading": "crwdns3590:0crwdne3590:0", "error_deleting": "crwdns3592:0crwdne3592:0"}, "admin_test_questions": {"title_add": "crwdns3594:0crwdne3594:0", "title_main": "crwdns3596:0crwdne3596:0", "add_new_question": "crwdns3598:0crwdne3598:0", "edit": "crwdns3600:0crwdne3600:0", "delete": "crwdns3602:0crwdne3602:0", "total": "crwdns3604:0crwdne3604:0", "question": "crwdns3606:0crwdne3606:0", "questions": "crwdns3608:0crwdne3608:0", "entries_per_page": "crwdns3610:0crwdne3610:0", "answer": "crwdns3612:0crwdne3612:0", "confirm_delete_title": "crwdns3614:0crwdne3614:0", "confirm_delete_content": "crwdns3616:0crwdne3616:0", "confirm_delete_action": "crwdns3618:0crwdne3618:0"}, "admin_test_questions_notify": {"error_loading": "crwdns3620:0crwdne3620:0", "error_deleting": "crwdns3622:0crwdne3622:0"}, "legal_help": {"q0": "crwdns3624:0crwdne3624:0", "a0": "crwdns3626:0crwdne3626:0", "q1": "crwdns3628:0crwdne3628:0", "a1": "crwdns3630:0crwdne3630:0", "q2": "crwdns3632:0crwdne3632:0", "a2": "crwdns3634:0crwdne3634:0", "q3": "crwdns3636:0crwdne3636:0", "a3": "crwdns3638:0crwdne3638:0", "q4": "crwdns3640:0crwdne3640:0", "a4": "crwdns3642:0crwdne3642:0", "q5": "crwdns3644:0crwdne3644:0", "a5": "crwdns3646:0crwdne3646:0", "q6": "crwdns3648:0crwdne3648:0", "a6": "crwdns3650:0crwdne3650:0"}, "legal_privacy": {"title": "crwdns3652:0crwdne3652:0", "note": "crwdns3654:0crwdne3654:0", "general_title": "crwdns3656:0crwdne3656:0", "general_1": "crwdns3658:0crwdne3658:0", "general_2": "crwdns3660:0crwdne3660:0", "test_rules_title": "crwdns3662:0crwdne3662:0", "test_rules_1": "crwdns3664:0crwdne3664:0", "test_rules_2": "crwdns3666:0crwdne3666:0", "test_rules_3": "crwdns3668:0crwdne3668:0", "test_rules_4": "crwdns3670:0crwdne3670:0", "expiration_title": "crwdns3672:0crwdne3672:0", "expiration_1": "crwdns3674:0crwdne3674:0"}, "legal_terms": {"title": "crwdns3676:0crwdne3676:0", "note": "crwdns3678:0crwdne3678:0", "general_title": "crwdns3680:0crwdne3680:0", "general_1": "crwdns3682:0crwdne3682:0", "general_2": "crwdns3684:0crwdne3684:0", "test_rules_title": "crwdns3686:0crwdne3686:0", "test_rules_1": "crwdns3688:0crwdne3688:0", "test_rules_2": "crwdns3690:0crwdne3690:0", "test_rules_3": "crwdns3692:0crwdne3692:0", "test_rules_4": "crwdns3694:0crwdne3694:0", "expiration_title": "crwdns3696:0crwdne3696:0", "expiration_1": "crwdns3698:0crwdne3698:0"}}