{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "continue": "Continue", "save": "Save", "edit": "Edit", "delete": "Delete", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "submit": "Submit", "reset": "Reset", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "download": "Download", "upload": "Upload", "view": "View", "add": "Add", "remove": "Remove", "update": "Update", "create": "Create", "select": "Select", "choose": "<PERSON><PERSON>", "browse": "Browse", "clear": "Clear", "apply": "Apply", "done": "Done", "finish": "Finish", "start": "Start", "stop": "Stop", "pause": "Pause", "resume": "Resume", "retry": "Retry", "skip": "<PERSON><PERSON>", "help": "Help", "settings": "Settings", "profile": "Profile", "logout": "Log out", "logging_out": "Logging out...", "login": "<PERSON><PERSON>", "go": "Go", "view_less": "VIEW LESS", "view_all": "VIEW All", "default": "<PERSON><PERSON><PERSON>", "congratulations": "Congratulations!", "signup": "Sign up", "email": "Email", "password": "Password", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "phone": "Phone", "address": "Address", "city": "City", "state": "State", "country": "Country", "zip_code": "Zip Code", "date": "Date", "time": "Time", "status": "Status", "price": "Price", "total": "Total", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "description": "Description", "details": "Details", "information": "Information", "notes": "Notes", "comments": "Comments", "message": "Message", "notification": "Notification", "alert": "<PERSON><PERSON>", "warning": "Warning", "info": "Information", "processing": "Processing...", "please_wait": "Please wait...", "no_data": "No data available", "no_results": "No results found", "not_found": "Not found", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "server_error": "Server error", "network_error": "Network error", "connection_error": "Connection error", "timeout_error": "Request timeout", "validation_error": "Validation error", "required_field": "This field is required", "invalid_format": "Invalid format", "invalid_email": "Invalid email address", "password_too_short": "Password must be at least 8 characters", "passwords_dont_match": "Passwords don't match", "file_too_large": "File is too large", "unsupported_file_type": "Unsupported file type", "max_file_size": "Maximum file size is {{size}}MB", "international": "International", "attention": "Attention", "type": "Type", "go_back": "Go Back", "select_date": "Select Date", "questions_answered": "Questions Answered", "user_type": "User Type", "account_status": "Account Status", "active": "Active", "archived": "Archived", "applicant": "Applicant", "student": "Student", "tutor": "Tutor", "updating": "Updating...", "statistics": "Statistics", "acceptance_rate": "Acceptance Rate", "students_helped": "Students Helped", "top_subject": "Top Subject", "daily_streak": "Daily Challenges Streak", "daily_challenges": "Daily Challenges", "login_to_your_account": "Login to your account", "answer_50_questions": "Answer 50 questions", "keep_answer": "Keep answering questions to level up!", "ranks": "Ranks", "rewards": "Rewards", "less_than_500_answers": "Less than 500 answers", "bronze_frame": "Bronze frame", "500_answers": "500+ answers", "50_Bonus": "$50 Bonus", "silver_frame": "Silver frame", "1500_answers": "1500+ answers", "gold_frame": "Gold frame", "100_Bonus": "$100 Bonus", "3000_answers": "3000+ answers", "diamond_frame": "Diamond frame", "300_Bonus": "$300 Bonus", "achievements": "Achievements", "completed_registration": "Completed Registration", "champion_novice": "Champion <PERSON><PERSON>", "answered_100_questions": "Answered 100 Questions", "answered_1000_questions": "Answered 1,000 questions", "answered_5000_questions": "Answered 5,000 questions", "answered_10000_questions": "Answered 10,000 questions", "champion_adept": "Champion Adept", "champion_expert": "Champion Expert", "champion_master": "Champion Master", "streak_starter": "<PERSON><PERSON> Starter", "achieved_day_streak": "Achieved a 7-day streak", "achieved_30_day_streak": "Achieved a 30-day streak", "streak_pro": "Streak Pro", "top_Subject_Math": "Top Subject: Math", "become_top_in_Math": "Become top in Math", "top_Subject_Science": "Top Subject: Science", "Become_top_in_Science": "Become top in Science", "helping_hand": "Helping hand", "helped_50_students": "Helped 50 students", "helped_200_students": "Helped 200 students", "super_helper": "Super Helper", "perfect_rate": "Perfect Rate", "acceptance_rate_month": "100% Acceptance Rate for a month", "monthly_rank": "Monthly Rank", "monthly_answers": "Monthly Answers", "maths": "Maths", "reading": "Reading", "writing": "Writing", "science": "Science", "answer_apprentice": "Answer Apprentice", "homework_hero": "Homework Hero", "knowledge_knight": "Knowledge Knight", "wisdom_wizard": "Wisdom Wizard", "edit_profile": "Edit Profile", "edit_user_details": "Edit User Details", "archive_account": "Archive Account", "unarchive_account": "Unarchive Account", "delete_account": "Delete Account", "approve": "Approve", "deny": "<PERSON><PERSON>", "all_questions": "All Questions", "answers": "Answers", "back_to_questions": "Back to Questions", "history": "History", "student_questions": "Student Questions", "withdrawal_approved": "Withdrawal approved successfully", "withdrawal_approval_failed": "Withdrawal approval failed", "applicant_approved": "Applicant Approved Successfully", "applicant_denied": "Applicant Denied Successfully", "user_deleted": "User Deleted Successfully", "user_archived": "User Archived Successfully", "user_restored": "User Restored Successfully", "create_student_account": "Create Student Account", "create_tutor_account": "Create Tutor Account", "login_here": "Login here", "set_your_own_hours": "Set Your Own Hours", "easy_qualifications": "Easy Qualifications", "login_blocked_contact_support": "You are no longer able to login. Please contact support should you have any queries.", "this_user_has_been_deleted": "This user has been deleted.", "unable_to_verify_account": "Unable to verify user account. Please try again.", "authentication_failed": "Authentication failed", "account_disabled_contact_support": "This account has been disabled. Please contact support.", "too_many_failed_attempts": "Too many failed attempts. Please try again later.", "login_failed_try_again": "<PERSON><PERSON> failed. Please try again.", "please_select_valid_date": "Please select a valid date.", "please_select_valid_date_range": "Please select a valid date range.", "default_user": "Default User", "preview": "Preview", "add_icon": "Add Icon", "edit_icon": "Edit Icon", "change_image": "Change Image", "choose_files": "<PERSON><PERSON>", "uploaded": "Uploaded", "max_file_size_100mb": "Max file size: 100 MB", "menu_icon": "menu icon", "toggle_menu": "Toggle menu", "logout_icon": "Logout icon", "user_profile": "User profile", "search_questions": "Search questions.", "apply_filter": "Apply Filter", "select_language": "select Question Language"}, "auth": {"signup_at_least_13": "Start earning today", "signup_13_years_old": "I am at least 13 years old", "signup_terms_and_conditions": "I agree to the terms and conditions of Odevly", "earn_per_answer": "Earn $3 Per Answer", "set_own_hours": "Set Your Own Hours", "easy_qualifications": "Easy Qualifications", "how_it_works": "How it Works", "earn_per_answer_text": "Answer a question and get paid $3 immediately - it's that simple! No hidden fees. Track your earnings on your profile and withdraw funds anytime.", "set_own_hours_text": "Answer questions whenever you want. Our platform is available 24/7, so you can earn anytime from anywhere in the world.", "easy_qualifications_text": "You don't need a teaching degree or previous tutoring experience. If you're at least 13, with solid knowledge of grade 1-8 subjects, you can be a tutor.", "how_it_works_text": "Questions are automatically sent to all available tutors, and are viewable in your profile feed. You can answer or skip as many as you want.", "sign_in": "Sign In", "login_title": "Welcome back", "login_subtitle": "Sign in to your account", "signup_title": "Create your account", "signup_subtitle": "Join us today", "forgot_password": "Forgot Password?", "reset_password": "Reset Password", "create_new_password": "Create new password", "confirm_password": "Confirm Password", "old_password": "Old Password", "new_password": "New Password", "change_password": "Change Password", "enter_email": "Enter your email", "enter_password": "Enter your password", "enter_name": "Enter your name", "enter_first_name": "Enter your first name", "enter_last_name": "Enter your last name", "enter_old_password": "Enter your old password", "enter_new_password": "Enter your new password", "enter_confirm_password": "Enter your confirm password", "continue_with_google": "Continue with Google", "signup_with_google": "Sign up with Google", "dont_have_account": "Don't have an account?", "already_have_account": "Already have an account?", "create_one": "Create one", "create_account": "Create an account", "signup_for_free": "Sign up for free", "terms_conditions": "Terms and conditions", "privacy_policy": "Privacy policy", "agree_to_terms": "By clicking \"Get started,\" you are confirming your agreement to our", "and_our": "as well as our", "password_reset_sent": "Password reset link has been sent to your email.", "check_email": "Check your email", "verification_link_sent": "We sent a verification link to", "click_verification_link": "Please click the verification link to access the password reset form.", "didnt_receive_email": "Didn't receive the email?", "resend_link": "Resend link", "back_to_login": "Back to login", "no_worries_reset": "No worries, we'll send you reset instructions.", "password_reset_success": "Your password has been successfully reset.", "click_to_login": "Click below to log in.", "account_doesnt_exist": "Account doesn't exist", "wrong_email_password": "Wrong email or password", "email_exists": "An account already exists with this email address - please log in.", "google_auth_error": "<PERSON><PERSON> failed - this email is associated with a Google Authentication account", "profile_updated": "Profile updated successfully", "error_sending_reset": "Error sending password reset email", "old_password_required": "Old password is required", "old_password_incorrect": "Invalid old password", "password_required": "Password is required", "email_required": "Email is required", "password_different": "Your new password must be different to previously used passwords", "first_name_required": "First Name is required", "last_name_required": "Last Name is required", "create_user_failed": "Failed to create user account.", "register_as_tutor": "Register as a Tutor", "register_as_student": "Register as a Student", "must_be_13_tutor": "You must be at least 13 years old to be a tutor", "must_be_13_student": "You must be at least 13 years old", "password_reset": "Password Reset", "reset_pass_text1": "Enter your new password below.", "reset_pass_text2": "Make sure it's secure and easy to remember.", "password_updated_successfully": "Password updated successfully!", "failed_to_update_password": "Failed to update password.", "must_be_signed_in": "You must be signed in to change the password.", "forgot_pass_text1": "Enter your email address and we'll send you a link to reset your password.", "forgot_pass_text2": "Check your email", "forgot_pass_text3": "We sent a password reset link to", "forgot_pass_text5": "Didn't receive the email?", "forgot_pass_text6": "Resend link", "error_occurred": "An error occurred. Please try again.", "enter_your_email": "Enter your email"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "my_account": "My Account", "my_tests": "My Tests", "my_questions": "My Questions", "packs": "Packs", "settings": "Settings", "transactions": "Transactions", "history": "History", "earnings": "Earnings", "profile": "Profile", "payout_method": "Payout Method", "tutor_settings": "<PERSON><PERSON>", "registration": "Registration", "admin_area": "Admin Area", "users": "Users", "applicant_questions": "Applicant Questions", "test_questions": "Test Questions", "user_questions": "User Questions", "edit_user": "Edit User", "pricing": "Pricing"}, "homepage": {"title": "Become a certified Master Teacher with our international exam", "subtitle": "Our tutors are ready 24/7 to answer your homework questions. Just type your question and get an answer in minutes!", "start_test": "Start test", "become_tutor": "Become a Tutor", "become_a_homework_hero": "Become a Homework Hero!", "become_student": "Become a student", "get_started": "Get Started", "learn_more": "Learn More", "features": "Features", "testimonials": "Testimonials", "faq": "Frequently Asked Questions", "contact": "Contact Us", "about": "About Us", "privacy": "Privacy Policy", "terms": "Terms of Service", "questions": "Questions?", "get_help_feedback": "Get help or share feedback with our amazing Support team!", "contact_us": "CONTACT US", "copyright": "COPYRIGHT © 2025 ODEVLY, ALL RIGHTS RESERVED", "subjects": "Subjects", "pricing": "Pricing", "how_it_works": "How It Works", "homework_help_for_grades_1-8": "Homework Help for Grades 1-8", "subjects_description": "From fractions to fossils, we've got all your homework bases covered! Tough subjects don't stand a chance against <PERSON><PERSON><PERSON><PERSON>'s expert tutors.", "how_it_works_description": "Stuck on a homework question? <PERSON><PERSON><PERSON><PERSON> gets you unstuck. No stress, no waiting—just instant answers from real tutors who've got your back.", "become_tutor_description": "Earn $3 for every question you answer. Sign up in minutes and start earning today!", "step_1_title": "Ask Away!", "step_1_description": "Just type out your question or snap a pic! Whether it's Math, Science, Social Studies, or Language Arts, we're here to help.", "step_2_title": "Get Answers Fast!", "step_2_description": "Sit tight—our expert tutors will solve your question in minutes. You'll get clear, reliable answers you can actually understand!", "step_3_title": "Keep Crushing It!", "step_3_description": "Save all your questions and answers in your personal study space. Perfect for test prep!", "recent_questions_title": "recent questions", "recent_questions_description": "Wondering what other students are asking? Here's a sneak peek! Each question below was answered by an Odevly tutor in minutes!", "sample_questions": {"question_1": "How many sides does a hexagon have?", "answer_1": "6 sides", "subject_1": "Math", "question_2": "How many bones are the human body?", "answer_2": "206", "subject_2": "Science", "question_3": "Who wrote <PERSON> and Juliet?", "answer_3": "<PERSON>", "subject_3": "Language Arts", "question_4": "What is the capitol of the USA?", "answer_4": "Washington D.C.", "subject_4": "Social Studies", "question_5": "What is 25% of 200?", "answer_5": "50", "subject_5": "Math", "question_6": "Which planet is closest to the Sun?", "answer_6": "Mercury", "subject_6": "Science"}, "meet_tutors_title": "homework heroes!", "meet_tutors_highlight": "heroes!", "meet_tutors_description": "Meet some of the experts behind <PERSON><PERSON><PERSON><PERSON>'s lightning-fast answers. These superhero tutors are here to save the day, one question at a time.", "tutor_info": {"name": "<PERSON>", "degree": "Masters Degree", "title": "High School history teacher", "location": "United States", "certification": "ACPT certified tutor", "experience": "5+ years exp as a tutor", "answers_this_month": "answers this month"}}, "certificate": {"verify_certificate": "Verify Certificate", "certificate_heading": "Enter a certificate number to continue", "enter_certificate_number": "Enter Certificate Number", "official_certificate": "Official Certificate", "certificate_number": "Certificate Number", "certificate_info": "Certificate Information", "invalid_certificate": "Invalid Certificate", "certificate_verified": "Certificate verified successfully", "certificate_not_found": "Certificate not found", "certificate_expired": "Certificate has expired", "certificate_invalid": "Invalid certificate format"}, "exam": {"welcome_title": "Welcome to Lorem Ipsum Test", "exam_details": "50 multiple choice questions", "time_limit": "1 hour time limit", "certificate_available": "Official certificate available", "things_to_remember": "Things to Remember", "complete_one_session": "You must complete this exam in one session", "close_window_warning": "If you close the window, all progress will be lost", "retake_available": "You will be able to retake the exam", "quit_test": "Quit Test", "quit_test_content": "All progress will be lost. Are you sure you want to quit?", "quit_test_confirm": "Yes, Quit Test", "invalid_exam": "We had an issue retrieving your exam information - please reach out to support for assistance.", "take_test": "Take a Test", "test_language": "Test Language", "score": "Score", "action": "Action", "date": "Date"}, "profile": {"personal_info": "Personal Info", "general_info": "General Info", "user_details": "User Details", "email_address": "Email Address", "phone_number": "Phone Number", "date_of_birth": "Date of Birth", "education_level": "Education Level", "tutor_experience": "Tutor Experience", "work_title": "Work Title", "billing_address": "Billing Address", "certificate_number": "Certificate Number", "save_changes": "Save Changes", "profile_photo": "Profile Photo", "upload_photo": "Upload your Photo", "upload_image": "Upload Image", "take_with_webcam": "Take with Webcam", "webcam": "Webcam", "webcam_content": "Capture & upload picture directly from your webcam.", "capture_photo": "Capture Photo"}, "payment": {"payment_info": "Payment Information", "payment_method": "Payment Method", "country_region": "Country/Region", "credit_debit_card": "Credit or Debit Card", "name_on_card": "Name on card", "card_number": "Card number", "expiration_date": "Expiry", "cvc": "Security Code", "buy_now": "Buy Now", "total": "Total", "processing": "Processing...", "financial_account_helper": "This is where you open your financial account", "name_on_card_placeholder": "e.g <PERSON>", "credits_never_expire": "Credits do not expire", "satisfaction_guaranteed": "100% guarantee", "tutor_access_24_7": "24/7 tutor access"}, "admin": {"dashboard": "Admin Dashboard", "users": "Users", "questions": "Questions", "transactions": "Transactions", "settings": "<PERSON><PERSON>s", "add_question": "Add Question", "edit_question": "Edit Question", "delete_question": "Delete Question", "view_user": "View User", "edit_user": "Edit User", "delete_user": "Delete User", "approve_user": "Approve User", "reject_user": "Reject User", "user_type": "User Type", "user": "User", "tutor": "Tutor", "admin": "Admin", "applicant": "Applicant", "status": "Status", "active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "date_joined": "Date Joined", "last_login": "Last Login", "total_users": "Total Users", "total_tutors": "Total Tutors", "total_questions": "Total Questions", "total_earnings": "Total Earnings", "total_applicants": "Total Applicants", "student_questions": "Student Questions", "question": "Question", "question_id": "Question ID", "answered": "Answered", "link": "Link", "id": "ID", "correct": "Correct", "first_name": "First Name", "last_name": "Last Name", "enter_your_first_name": "Enter your first name", "enter_your_last_name": "Enter your last name", "email_required": "Email is required", "invalid_email": "Invalid email address"}, "tutor": {"dashboard": "Tutor Dashboard", "earnings": "Earnings", "earnings_description": "Track your earnings and manage your payouts. View your transaction history and withdraw your funds when ready.", "history": "History", "payout_method": "Payout Method", "settings": "<PERSON><PERSON>", "registration": "Registration", "profile": "Profile", "balance": "Balance", "withdraw": "Withdraw", "payout_history": "Payout History", "question_history": "Question History", "answer_question": "Answer Question", "view_question": "View Question", "submit_answer": "Submit Answer", "answer_text": "Answer Text", "upload_image": "Upload Image", "earnings_per_question": "Earnings per question", "total_questions_answered": "Total questions answered", "total_earnings": "Total earnings", "available_balance": "Available Balance", "withdraw_funds": "Withdraw Funds", "pending_withdrawals": "Pending Withdrawals", "transaction": "transaction", "transactions": "transactions", "active_withdraw_method": "Active Withdraw Method", "withdraw_methods": "Withdraw Methods", "setup_payout_method": "Setup at least one payout method so we know where to send your money.", "uk_bank_account": "UK Bank Account", "us_bank_account": "US Bank Account", "change_method": "Change Method", "add_withdraw_method": "Add Withdraw Method", "no_earnings_data": "No earnings data found.", "confirm_withdrawal": "Confirm <PERSON>", "confirm_withdrawal_amount": "Are you sure you want to withdraw {{amount}}?", "yes_withdraw": "Yes, Withdraw", "payment_sent": "Payment has been sent!", "payment_available_shortly": "Your money should be available in your account shortly", "no_payout_method_added": "You have not added a payout method yet. Tell us how you want to receive your funds.", "add_payout_method": "Add Payout Method", "no_balance_to_withdraw": "No balance to withdraw.", "withdrawal_pending_approval": "This withdrawal is pending admin approval.", "withdrawal_submit_failed": "Failed to submit pending withdrawal. Please try again.", "withdrawal_error": "An error occurred while submitting pending withdrawal.", "withdrawal_failed_wise": "<PERSON><PERSON><PERSON> failed via <PERSON>.", "name_mismatch_error": "Your first and last name must match your bank details. Please update your profile.", "withdrawal_failed_support": "<PERSON><PERSON><PERSON> failed. Please reach out to support for assistance.", "pending_withdrawal_amount": "Withdrawal amount", "bank_account": "Bank Account", "paypal": "PayPal", "wise": "<PERSON>", "account_number": "Account Number", "routing_number": "Routing Number", "iban": "IBAN", "sort_code": "Sort Code"}, "student": {"dashboard": "Student Dashboard", "my_questions": "My Questions", "register_as_a_tutor": "REGISTER AS A TUTOR", "ask_question": "Ask a Tutor", "ask_question_description": "Need help with homework or a tricky question? Our friendly tutors are here to guide you. Get clear answers and learn something new every time. Just ask, and we'll help you figure it out!", "question_history": "Question History", "packs": "Packs", "pack": "Pack", "question_pack": "Question Packs", "buy_pack": "Buy Pack", "pack_details": "Pack Details", "questions_in_pack": "Questions in pack", "price_per_question": "Price per question", "total_price": "Total price", "purchase_pack": "Purchase Pack", "purchase_pack_message": "Please purchase a pack to ask questions.", "question_text": "Question Text", "upload_image": "Upload File", "what_need_help_with": "What do you need help with?", "question_placeholder": "I need help with question 12....", "see_sample_question_see_an": "See an", "see_sample_question": "example of a question", "submit_question": "Get Answer", "sample_question": "Sample Question", "sample_question_description": "This is how a question will appear", "subject": "Subject", "subject_math": "Mathematics", "subject_science": "Science", "subject_language_arts": "Language Arts", "subject_social_studies": "Social Studies", "subject_other": "Other", "priority": "Priority", "priority_low": "Low", "priority_medium": "Medium", "priority_high": "High", "priority_urgent": "<PERSON><PERSON>", "question_status": "Question Status", "status_pending": "Pending", "status_answered": "Answered", "status_cancelled": "Cancelled", "asked_by": "Asked by", "answered_by": "Answered by", "asked_date": "Asked Date", "answered_date": "Answered Date", "ask": "Ask", "history": "History", "buy": "Buy", "settings": "Settings", "transactions": "Transactions"}, "notifications": {"question_answered": "Your question has been answered", "new_question": "New question available", "payment_successful": "Payment successful", "payment_failed": "Payment failed", "withdrawal_successful": "<PERSON><PERSON><PERSON> successful", "withdrawal_failed": "<PERSON><PERSON><PERSON> failed", "profile_updated": "Profile updated successfully", "password_changed": "Password changed successfully", "email_sent": "<PERSON>ail sent successfully", "error_occurred": "An error occurred", "success": "Success", "warning": "Warning", "info": "Information"}, "errors": {"network_error": "Network connection issue. Please check your internet connection and try again.", "permission_error": "You don't have permission to perform this action. Please contact support if this is an error.", "timeout_error": "The operation timed out. Please try again.", "quota_error": "Service limit reached. Please try again later or contact support.", "file_size_error": "File size exceeds maximum allowed size", "file_type_error": "File type is not supported. Please use JPEG, PNG, or WebP.", "upload_failed": "Upload failed", "download_failed": "Download failed", "save_failed": "Save failed", "delete_failed": "Delete failed", "operation_failed": "Operation failed", "unknown_error": "An unknown error occurred. Try again later"}, "subjects": {"science": "Science", "math": "Math", "language_arts": "Language Arts", "social_studies": "Social Studies", "grade_range": "Grade 1-8"}, "meet_tutors": {"title_main": "homework", "heroes": "heroes!", "intro_1": "Meet some of the experts behind <PERSON><PERSON><PERSON><PERSON>'s lightning-fast answers.", "intro_2": "These superhero tutors are here to save the day, one question at a time.", "answers_this_month": "answers this month", "degree": "{{degree}}", "title": "{{title}}", "location": "{{location}}", "certification": "{{certification}}", "experience": "{{experience}}", "prev_tutor": "Previous tutor", "next_tutor": "Next tutor", "prev": "Previous", "next": "Next"}, "faq_section": {"q1": "What is <PERSON><PERSON><PERSON><PERSON>?", "a1": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀", "q2": "Who are <PERSON><PERSON><PERSON><PERSON>’s lessons for?", "a2": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀", "q3": "Which subjects does Odevly cover?", "a3": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀", "q4": "Why choose online tutoring?", "a4": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀", "q5": "How can I book a lesson?", "a5": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀", "q6": "How much does a single question cost?", "a6": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀"}, "pricing_section": {"title": "pricing", "subtitle": "No subscriptions or commitments – just reliable answers from expert tutors whenever homework gets tricky!", "questions": "Questions", "popular": "Popular", "math": "Math", "language_arts": "Language Arts", "science": "Science", "social_studies": "Social Studies", "buy_now": "BUY NOW", "feature_1": "Step-by-step solutions", "feature_2": "Chat with tutors", "feature_3": "Fast response time"}, "top_grade_section": {"study_smarter_not_harder": "study smarter,   not harder", "ask_experts_not_robots": "Ask Experts, Not Robots", "ai_bots_unreliable_description": "A.I. bots are unreliable. Get answers you can trust from <PERSON><PERSON><PERSON><PERSON>'s expert tutors.", "blast_roadblocks": "Blast Roadblocks!", "waste_hours_description": "Don't waste hours stuck on one tricky question. Zoom past homework roadblocks with <PERSON><PERSON><PERSON><PERSON>.", "peak_time_power": "Peak-Time Power!", "brain_ready_description": "Get help when your brain is ready to learn, day or night. Odevly tutors are available 24/7.", "get_started_button": "GET STARTED"}, "WhyLoveSection": {"sectionTitle": "Why you’ll love", "sectionTitleHighlight": "<PERSON><PERSON><PERSON><PERSON>", "items": [{"title": "12,000+ verified Experts"}, {"title": "Every question saves money"}, {"title": "Service that’s tailored to you"}, {"title": "Save valuable time at home"}]}, "admin_add_question": {"question_label": "Question", "answer_1_label": "Answer 1", "answer_2_label": "Answer 2", "answer_3_label": "Answer 3", "answer_4_label": "Answer 4", "error_no_answer": "Please select a correct answer to save the question", "cancel_button": "Cancel", "add_question_button": "Add Question", "save_question_button": "Save Question", "confirm_modal_title": "Question Added", "confirm_modal_content": "There are now {{count}} questions in the bank.", "close_button": "Close", "add_another_button": "Add another question"}, "admin_add_question_notify": {"question_counter_invalid": "Error: Question counter data invalid.", "question_required": "Question is required", "answer1_required": "Answer 1 is required", "answer2_required": "Answer 2 is required", "answer3_required": "Answer 3 is required", "answer4_required": "Answer 4 is required", "failed_update_counter": "Error: Failed to update question counter."}, "admin_question": {"student_questions_title": "Student Questions", "history_title": "History", "help_description": "Need help with homework or a tricky question? Our friendly tutors are here to guide you. Get clear answers and learn something new every time. Just ask, and we'll help you figure it out!", "search_placeholder_admin": "Search by word, question ID", "search_placeholder_user": "Search questions", "answered_button": "Answered", "not_answered_button": "Not Answered", "asked_by_label": "Asked By", "date_asked_label": "Date Asked", "asked_label": "Asked", "answered_label": "Answered", "feedback_button": "<PERSON><PERSON><PERSON>", "answer_label": "Answer", "loading_text": "Loading...", "delete_question_button": "Delete Question", "no_question_found": "No question found.", "no_questions_submitted": "You haven't submitted any questions.", "help_email_title": "If you need help please email us.", "help_email_content": "<a href=\"mailto:<EMAIL>\"><EMAIL></a>", "delete_confirm_title": "Delete Question", "delete_confirm_content": "Are you sure you want to delete this question?", "cancel_button": "Cancel", "yes_delete_button": "Yes, Delete"}, "admin_edit_user_profile": {"date_column": "Date", "id_column": "ID", "correct_column": "Correct", "amount_column": "Amount", "link_column": "Link", "acceptance_rate_label": "Acceptance Rate", "students_helped_label": "Students Helped", "top_subject_label": "Top Subject", "daily_streak_label": "Daily Challenges Streak", "monthly_rank_label": "Monthly Rank", "monthly_answers_label": "Monthly Answers", "acceptance_rate_placeholder": "e.g. 85", "students_helped_placeholder": "e.g. 120", "top_subject_placeholder": "e.g. Math", "daily_streak_placeholder": "e.g. 12", "monthly_rank_placeholder": "e.g. 5", "monthly_answers_placeholder": "e.g. 80", "maths_option": "Maths", "reading_option": "Reading", "writing_option": "Writing", "science_option": "Science", "answer_apprentice_option": "Answer Apprentice", "homework_hero_option": "Homework Hero", "knowledge_knight_option": "Knowledge Knight", "wisdom_wizard_option": "Wisdom Wizard", "updating_button": "Updating...", "update_button": "Update", "locked_option": "Locked", "unlocked_option": "Unlocked", "approve_button": "Approve", "deny_button": "<PERSON><PERSON>", "update_user_details_title": "Update User Details", "first_name_placeholder": "Enter first name", "last_name_placeholder": "Enter last name", "email_address_label": "Email Address", "email_placeholder": "Enter email address", "tutor_certification_label": "Tutor Certification Number", "certification_placeholder": "Enter certification number", "credit_amount_label": "Credit Amount", "deduct_amount_label": "Deduct Amount", "update_profile_button": "Update Profile", "approve_applicant_title": "Approve Applicant?", "deny_applicant_title": "Deny Applicant?", "irreversible_action_content": "Please note that this action is irreversible. We recommend double-checking by clicking the 'Visit Profile' button.", "delete_user_title": "Confirm Delete User", "delete_user_content": "Please note that this action is irreversible. Are you sure you want to permanently delete this user?", "delete_user_button": "Delete User", "unarchive_user_title": "Confirm Unarchive User", "archive_user_title": "Confirm Archive User", "unarchive_user_content": "Are you sure you want to restore this user?", "archive_user_content": "Are you sure you want to archive this user?", "unarchive_button": "Unarchive", "archive_button": "Archive"}, "admin_edit_user_profile_notify": {"user_not_found": "User not found or could not be fetched.", "error_fetching_user": "Error fetching user information.", "error_fetching_applicant_answers": "Error fetching applicant answers.", "no_changes": "No changes to update.", "profile_updated": "User profile updated successfully!", "failed_update": "Failed to update user profile.", "error_updating": "An error occurred while updating.", "applicant_approved": "Applicant Approved Successfully", "failed_approve": "Failed to approve applicant.", "error_approving": "An error occurred while approving applicant.", "applicant_denied": "Applicant Denied Successfully", "failed_deny": "Failed to fully deny applicant. Some data might remain.", "error_denying": "An error occurred while denying applicant.", "archived": "User Archived Successfully", "failed_archive": "Failed to archive user.", "error_archiving": "An error occurred while archiving user.", "restored": "User Restored Successfully", "failed_restore": "Failed to restore user.", "error_restoring": "An error occurred while restoring user.", "deleted": "User Deleted Successfully", "failed_delete": "Failed to delete user.", "error_deleting": "An error occurred while deleting user.", "error_uploading_profile_image": "Error uploading profile image.", "failed_to_upload_profile_image": "Failed to upload profile image.", "statistics_updated": "Statistics updated successfully", "failed_update_statistics": "Failed to update statistics", "achievements_updated": "Achievements updated successfully", "failed_update_achievements": "Failed to update achievements"}, "admin_user_profile": {"admin_label": "Admin", "user_name": "User Name", "approve": "Approve", "deny": "<PERSON><PERSON>", "edit_profile": "Edit Profile", "edit_user_details": "Edit User Details", "unarchive_account": "Unarchive Account", "archive_account": "Archive Account", "delete_account": "Delete Account", "email_address": "Email Address", "total_earnings": "Total Earnings", "joined_date": "Joined Date", "total_answers": "Total Answers", "question_credits": "Question Credits", "total_questions": "Total Questions", "payout_request": "Payout Request", "date_of_request": "Date of Request", "amount_requested": "Amount Requested", "all_questions": "All Questions", "test_question_answers": "Test Question Answers", "search_by_question_id": "Search by question ID", "view": "View", "update_user_details": "Update User Details", "first_name": "First Name", "last_name": "Last Name", "phone_number": "Phone Number", "tutor_certification_number": "Tutor Certification Number", "birthday": "Birthday", "current_balance": "Current Balance", "credit_amount": "Credit Amount", "deduct_amount": "Deduct Amount", "unarchive": "Unarchive", "archive": "Archive", "delete": "Delete", "update": "Update", "confirm_delete": "Confirm Delete", "confirm_archive": "Confirm Archive", "confirm_unarchive": "Confirm Unarchive", "approve_payout_request": "Approve the payout request?", "deny_payout_request": "Deny the payout request?", "approve_applicant": "Approve Applicant?", "deny_applicant": "Deny Applicant?", "irreversible_action": "Please note that this action is irreversible. We recommend double-checking by clicking the 'Visit Profile' button.", "restore_user": "Are you sure you want to restore this user?", "archive_user": "Are you sure you want to archive this user?", "delete_user": "Are you sure you want to delete this user?", "the_user_has_not_yet_answered_a_question": "The user has not yet answered a question.", "the_user_has_not_yet_asked_a_question": "The user has not yet asked a question."}, "admin_user_profile_notify": {"withdrawal_approved": "Withdrawal approved successfully", "withdrawal_approval_failed": "Withdrawal approval failed", "user_deleted": "User Deleted Successfully", "user_archived": "User Archived Successfully", "user_restored": "User Restored Successfully", "applicant_approved": "Applicant Approved Successfully", "applicant_denied": "Applicant Denied Successfully"}, "setting": {"first_name": "First Name", "last_name": "Last Name", "email_address": "Email Address", "phone_number": "Phone Number", "date_of_birth": "Date of Birth", "education_level": "Education Level", "tutor_experience": "Tutor Experience", "current_work_title": "Current Work Title (optional)", "country_of_residence": "Country of Residence", "tutor_certification_number": "Tutor Certification Number", "current_password": "Current Password", "new_password": "New Password", "update": "Update", "first_name_placeholder": "Enter your first name", "last_name_placeholder": "Enter your last name", "email_address_placeholder": "Enter your email address", "phone_number_placeholder": "Enter your phone number", "work_title_placeholder": "Enter your current work title", "country_placeholder": "Select your country of residence", "education_level_placeholder": "Select your education level", "tutor_experience_placeholder": "Select your level of tutor experience", "certificate_number_placeholder": "Enter your certificate number", "current_password_placeholder": "Enter your current password", "new_password_placeholder": "Enter your new password", "month_placeholder": "Month", "day_placeholder": "Day", "year_placeholder": "Year", "lets_get_started": "Let's get started!", "keep_going": "Keep going!", "making_progress": "Making progress!", "almost_done": "Almost done!", "all_set": "All set!", "current_student": "Current Student", "high_school": "High School", "bachelors_degree": "Bachelor's Degree", "masters_degree": "Master's Degree", "phd": "PhD", "0_years": "0 years", "1_year": "1 year", "2_years": "2 years", "3_years": "3 years", "4_years": "4 years", "5_plus_years": "5+ years", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "tutor_certificate": "Tutor Certificate", "tutor_certificate_notification_message": "Tutors are required to obtain a certificate before they can earn money from answering questions. We currently accept the ACPT certificate, which can be obtained from "}, "setting_notify": {"invalid_certificate": "Invalid certificate number, please try again.", "failed_validate_certificate": "Failed to validate certificate. Please try again.", "failed_update_profile": "Failed to update profile. Please try again.", "profile_updated": "Profile updated successfully!", "failed_update": "Failed to update profile.", "error_updating": "An error occurred while updating.", "no_changes": "No changes to update.", "required_field": "This field is required.", "invalid_email": "Invalid email address.", "error_occurred": "An error occurred. Please try again.", "new_password_must_be_at_least_8_characters": "New password must be at least 8 characters.", "current_password_is_required": "Current password is required.", "please_enter_a_new_password_or_clear_the_current_password_field": "Please enter a new password or clear the current password field.", "the_current_password_you_entered_is_incorrect": "The current password you entered is incorrect."}, "password_field": {"must_8_characters": "Must be at least 8 characters"}, "modal_sample_question_modal": {"sample_text": "I need help with number 3. I don’t know how to add fractions.", "close": "Close"}, "modal_set_price_modal": {"title": "Set New Price", "current_price": "Current Price: ${{price}}", "new_price_label": "New Price", "cancel": "Cancel", "save": "Save", "success": "Successfully Updated.", "error_no_price": "Please enter a price."}, "modal_view_question_modal": {"title": "View Question", "question": "Question", "applicants_answer": "Applicant's Answer", "close": "Close"}, "error": {"signout_failed": "Error signing out. Please try again.", "unknown": "An unknown error occurred."}, "navbar_legal": {"login": "<PERSON><PERSON>", "terms": "Terms", "privacy": "Privacy", "help": "Help", "registerAsTutor": "Register as a Tutor", "back": "Back"}, "navigation_tabs_admin": {"dashboard": "Dashboard", "users": "Users", "test_questions": "Test Questions", "student_questions": "Student Questions", "settings": "Settings"}, "navigation_tabs_legal": {"terms": "Terms", "privacy": "Privacy", "help": "Help"}, "payment_form": {"country_region": "Country/Region", "select_region": "Select a region", "helper_text": "This is where you open your financial account", "credit_or_debit_card": "Credit or Debit Card", "payment_method": "Payment Method", "name_on_card": "Name on Card", "card_number": "Card Number", "expiration_date": "Expiration Date", "cvc": "CVC", "processing": "Processing...", "buy_now": "Buy Now", "error_adding_transaction": "Error adding transaction:", "user_object_missing": "User object is missing or incomplete:", "user_error_notify": "We have experienced a problem - please try again or refresh the page.", "payment_failed": "Payment Failed", "payment_processing_error": "Payment processing error:", "payment_error_notify": "An error occurred during payment"}, "student_payment_billing": {"title": "Transactions", "subtitle": "View your historic purchases and pack usage.", "tab_purchases": "Purchases", "tab_questions": "Questions", "no_packs_purchased": "No packs have been purchased.", "no_questions_or_credits": "No questions or credits have been recorded.", "no_data_found": "No data found."}, "tutor_history": {"title": "History", "subtitle": "Need help with homework or a tricky question? Our friendly tutors are here to guide you. Get clear answers and learn something new every time. Just ask, and we'll help you figure it out!", "no_questions": "No Questions Found", "question_id": "Question ID", "asked": "Asked", "answered": "Answered", "feedback": "<PERSON><PERSON><PERSON>", "answer": "Answer", "loading": "Loading...", "notify_title": "If you need help please email us.", "notify_content": "<a href=\"mailto:<EMAIL>\"><EMAIL></a>"}, "tutor_payout": {"title": "Payout Method", "subtitle": "Available payout methods based on your location", "billing_country": "Billing Country/Region", "billing_country_helper": "This is where you open your financial account", "wire_transfer": "Wire Transfer", "wire_transfer_fee": "$30 USD per wire to any bank", "wire_transfer_time": "Up to 7 business days to receive funds", "disconnect": "Disconnect", "connect_bank": "Connect Your Bank", "connect_bank_sub": "Add the bank account where you want to receive payouts", "checking": "Checking", "saving": "Saving", "iban": "IBAN", "iban_helper": "Up to 17 digits. Can be found on the bottom of cheques. Example: ************.", "account_number": "Account Number", "account_number_helper_uk": "9 digits. Can be found on the bottom of cheques. Example: *********.", "account_number_helper_us": "8-10 digits. Can be found on the bottom of cheques.", "sort_code": "Sortcode", "routing_number": "Routing Number", "account_type": "Account Type", "city": "City", "country_code": "Country Code", "state": "State", "postal_code": "Postal Code", "postcode": "Postcode", "zipcode": "Zipcode", "first_line": "First Line of Address", "agree_msg": "By continuing you agree to let Odevly send 2 small deposit amounts (less than $1.00 USD) and retrieve them in 1 withdraw.", "cancel": "Cancel", "update": "Update", "agree_and_link": "Agree and Link", "confirm_disconnect_title": "Confirm Disconnect", "confirm_disconnect_content": "Are you sure you want to disconnect this method?", "confirm_disconnect_action": "Yes, Disconnect", "notify_iban_required": "Iban is required", "notify_account_number_required": "Account Number is required", "notify_sort_code_required": "Sort Code is required", "notify_routing_number_required": "Routing Number is required", "notify_account_type_required": "Account Type is required", "notify_city_required": "City is required", "notify_country_code_required": "Country Code is required", "notify_state_required": "State is required", "notify_postcode_required": "Postcode is required", "notify_first_line_required": "First Line is required", "notify_withdrawal_updated": "Withdrawal method updated successfully", "notify_withdrawal_added": "Withdrawal method added successfully", "notify_withdrawal_disconnected": "Withdrawal method disconnected"}, "TutorQuestions": {"answer_questions_title": "Answer Questions", "answer_questions_desc": "Need help with homework or a tricky question? Our friendly tutors are here to guide you. Get clear answers and learn something new every time. Just ask, and we'll help you figure it out!", "class_dismissed_title": "Class Dismissed (For Now)", "class_dismissed_desc": "Everyone's homework is solved! Your question feed is quiet for the moment, so take a well-deserved break. More students will need your help soon!", "my_profile": "My profile", "applicant_thank_you": "Thank you for submitting. Your application is under review.", "no_applicant_questions": "No Applicant Questions Available", "no_applicant_questions_desc": "Check back later or contact support.", "complete_registration": "Complete Registration", "complete_registration_desc": "This information can be updated on your Settings page. Once you have submitted the missing information, you will have full access to the platform and will be able to earn money by answering questions.", "first_name": "First Name", "last_name": "Last Name", "phone_number": "Phone Number", "date_of_birth": "Date of Birth", "certificate_number": "ACPT Tutor Certificate Number", "complete_now": "Complete Now", "complete_registration_modal_title": "Complete Your Registration", "complete_registration_modal_content": "Once you complete your registration, you will be able to earn $3 for every question you answer.", "question_id": "Question ID", "feedback": "<PERSON><PERSON><PERSON>", "answer": "Answer", "write_answer_placeholder": "Write your answer here...", "upload_answer_photo": "Upload a photo of your answer", "skip_question": "Skip Question", "submitting": "Submitting...", "skipping": "Skipping...", "answer_for": "Answer for ${{amount}}", "answer_cannot_be_empty": "Answer cannot be empty.", "invalid_question_data": "Error: Invalid question data.", "please_select_answer": "Please select an answer.", "counter": "{{count}}/2000", "question_cost": "${{amount}}", "question_id_number": "Question ID #{{id}}", "if_you_need_help": "If you need help please email us.", "email_support": "<EMAIL>", "skip_question_modal_title": "If you skip this question, you will be assigned the next available question.", "skip_question_action": "Skip Question", "cancel": "Cancel"}, "admin_applicant_questions": {"title_add": "Add Question", "title_edit": "Edit Question", "title_main": "Test Questions", "total_questions": "Total Questions", "total_question": "Total Question", "set_price": "Set Price", "add_a_question": "Add A Question", "expand": "Expand", "collapse": "Collapse", "question_id": "Question ID", "answer": "Answer", "correct_answer": "Correct Answer", "delete_question": "Delete Question", "edit_question": "Edit Question", "delete_confirm_title": "Delete Question", "delete_confirm_content": "Are you sure you want to delete this question?", "delete_confirm_action": "Yes, Delete", "delete_cancel": "Cancel", "set_price_title": "Set Price", "set_price_action": "Save", "set_price_default": "De<PERSON><PERSON>", "notify_delete_error": "Error deleting question.", "notify_no_question": "No question selected for deletion."}, "admin_edit_user": {"title": "Edit User", "loading": "Loading...", "no_user_selected": "No User Selected", "no_user_selected_desc": "Please go back to 'Users' and select an individual.", "go_to_users": "Go to Users"}, "admin_edit_user_notify": {"user_not_found": "User not found.", "error_loading": "Error loading user data.", "update_success": "User updated successfully.", "update_error": "Failed to update user."}, "admin_history": {"title": "History", "expand": "Expand", "collapse": "Collapse", "question_id": "Question ID", "asked_by": "Asked by", "answered_by": "Answered by", "answer": "Answer:", "delete_question": "Delete Question", "no_questions": "You haven't submitted any questions.", "loading": "Loading...", "feedback": "feedback", "back_to_questions": "Back to Questions", "help_title": "If you need help please email us.", "help_content": "<a href=\"mailto:<EMAIL>\"><EMAIL></a>", "delete_confirm_title": "Delete Question", "delete_confirm_content": "Are you sure you want to delete this question?", "delete_cancel": "Cancel", "delete_confirm_action": "Yes, Delete"}, "admin_history_notify": {"error_loading": "Error loading questions.", "error_deleting": "Error deleting question."}, "admin_test_questions": {"title_add": "Add Question", "title_main": "Test Questions", "add_new_question": "Add a new question", "edit": "Edit", "delete": "Delete", "total": "Total:", "question": "question", "questions": "questions", "entries_per_page": "Entries per page", "answer": "Answer:", "confirm_delete_title": "Confirm Delete", "confirm_delete_content": "Are you sure you want to delete this question?", "confirm_delete_action": "Yes, Delete"}, "admin_test_questions_notify": {"error_loading": "Error loading questions.", "error_deleting": "Error deleting question."}, "legal_help": {"q0": "Using Odevly", "a0": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀", "q1": "What is <PERSON><PERSON><PERSON><PERSON>?", "a1": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀", "q2": "Who are <PERSON><PERSON><PERSON><PERSON>’s lessons for?", "a2": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀", "q3": "Which subjects does Odevly cover?", "a3": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀", "q4": "Why choose online tutoring?", "a4": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀", "q5": "How can I book a lesson?", "a5": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀", "q6": "How much does a single question cost?", "a6": "Our tutoring process is simple and efficient! Just submit your question or upload a photo of your homework, and one of our expert tutors will review it. Within minutes, you’ll receive a step-by-step solution tailored to your needs. Plus, you can chat with tutors for further clarification. Learning has never been this easy! 🚀"}, "legal_privacy": {"title": "Privacy Policy", "note": "Note: These Terms of Service were last updated on March 31, 2023", "general_title": "General", "general_1": "The Duolingo English Test (“DET”) is operated by Duolingo, Inc. (“Duolingo”). Your use of the DET is governed by the Duolingo Terms and Conditions of Service (“Duolingo Terms”) as well as these Supplemental Terms and Conditions for the Duolingo English Test (“DET Terms”). The DET Terms take precedence over any Duolingo Terms to the extent they conflict. Duolingo may amend the DET Terms at any time, and any such amendments will apply to all users who register for or take the DET after they are published. By registering for or taking the DET, you represent that you have read and understood, and agree to be bound by, the Duolingo Terms and DET Terms.", "general_2": "PLEASE NOTE THAT THESE TERMS AND CONDITIONS CONTAIN A MANDATORY ARBITRATION OF DISPUTES PROVISION THAT REQUIRES THE USE OF ARBITRATION ON AN INDIVIDUAL BASIS TO RESOLVE DISPUTES IN CERTAIN CIRCUMSTANCES, RATHER THAN JURY TRIALS OR CLASS ACTION LAWSUITS. VIEW THESE TERMS HERE.", "test_rules_title": "Test Rules", "test_rules_1": "Duolingo may set and modify rules for taking the DET (“Test Rules”), which are incorporated into the DET Terms, in its sole discretion. Test Rules will be posted here or otherwise communicated to you before you begin the DET.", "test_rules_2": "You must register for the DET using your real name and provide a form of identification from this list when you take the DET. Using a false name or false identification is a violation of Test Rules.", "test_rules_3": "If Duolingo determines, in its sole discretion, that a user has violated any Test Rules, Duolingo may invalidate the user’s test results, even if those results have been previously certified, and notify all third parties that have received the user’s results of the invalidation. In cases of severe violations, Duolingo may ban a user from taking the DET again.", "test_rules_4": "Any appeal of an invalid or invalidated result must be submitted to Duolingo within 72 hours.", "expiration_title": "Test Results Expiration", "expiration_1": "After you take the DET, you will receive a certificate containing your DET results and scores (“DET Certificate”). Your DET Certificate is valid for two years from the date you take the DET. After two years, your DET Certificate will be marked expired and you will no longer be able to share your DET Certificate with third parties."}, "legal_terms": {"title": "Terms and Conditions", "note": "Note: These Terms of Service were last updated on March 31, 2023", "general_title": "General", "general_1": "The Duolingo English Test (“DET”) is operated by Duolingo, Inc. (“Duolingo”). Your use of the DET is governed by the Duolingo Terms and Conditions of Service (“Duolingo Terms”) as well as these Supplemental Terms and Conditions for the Duolingo English Test (“DET Terms”). The DET Terms take precedence over any Duolingo Terms to the extent they conflict. Duolingo may amend the DET Terms at any time, and any such amendments will apply to all users who register for or take the DET after they are published. By registering for or taking the DET, you represent that you have read and understood, and agree to be bound by, the Duolingo Terms and DET Terms.", "general_2": "PLEASE NOTE THAT THESE TERMS AND CONDITIONS CONTAIN A MANDATORY ARBITRATION OF DISPUTES PROVISION THAT REQUIRES THE USE OF ARBITRATION ON AN INDIVIDUAL BASIS TO RESOLVE DISPUTES IN CERTAIN CIRCUMSTANCES, RATHER THAN JURY TRIALS OR CLASS ACTION LAWSUITS. VIEW THESE TERMS HERE.", "test_rules_title": "Test Rules", "test_rules_1": "Duolingo may set and modify rules for taking the DET (“Test Rules”), which are incorporated into the DET Terms, in its sole discretion. Test Rules will be posted here or otherwise communicated to you before you begin the DET.", "test_rules_2": "You must register for the DET using your real name and provide a form of identification from this list when you take the DET. Using a false name or false identification is a violation of Test Rules.", "test_rules_3": "If Duolingo determines, in its sole discretion, that a user has violated any Test Rules, Duolingo may invalidate the user’s test results, even if those results have been previously certified, and notify all third parties that have received the user’s results of the invalidation. In cases of severe violations, Duolingo may ban a user from taking the DET again.", "test_rules_4": "Any appeal of an invalid or invalidated result must be submitted to Duolingo within 72 hours.", "expiration_title": "Test Results Expiration", "expiration_1": "After you take the DET, you will receive a certificate containing your DET results and scores (“DET Certificate”). Your DET Certificate is valid for two years from the date you take the DET. After two years, your DET Certificate will be marked expired and you will no longer be able to share your DET Certificate with third parties."}}