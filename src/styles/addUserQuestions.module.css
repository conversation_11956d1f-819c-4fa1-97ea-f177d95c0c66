.addQuestionBox {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.addQuestionModal {
  flex-direction: column;
  gap: 20px;
  width: 100%;
  max-width: 820px;
  padding: 20px 20px 0;
}

.addQuestionBtn {
  width: 80vw;
  display: flex;
  justify-content: flex-end;
}

.searchLoader {
  margin: 10px 0 0 15px;
  font-size: 14px;
}

.maxQuestions {
  width: 70%;
}

.counter {
  display: flex;
  justify-content: flex-end;
  position: absolute;
  font-size: 10px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  color: #99a0ae;
  right: 10px;
  bottom: 10px;
}

.borderRoundBox {
  padding: 20px;
  border-radius: 16px;
  background: #fff;
  width: 100%;
  max-width: 858px;
  margin: auto;
  border: 1px solid rgba(229, 229, 229, 1);
}

.studentRoundBox{
  max-width: 950px;
  width:100%;
}

.toggleButtonGroup {
  display: grid;
  grid-template-columns: 1fr 1fr;
  background: rgba(247, 247, 247, 1);
  height: 55px;
  border-radius: 16px;
  width: 386px;
  border: 1px solid rgba(229, 229, 229, 1);
  padding: 4px;
}

.toggleButton {
  color: rgba(75, 75, 75, 1);
  font-size: 16px;
  font-weight: 500;
  border: 0;
  text-transform: capitalize;
  height: 45px;
  border-radius: 12px !important;
}

.toggleButtonSelected {
  box-shadow: 0px -3px 0px 0px rgba(92, 179, 4, 1) inset;
  background: rgba(107, 204, 9, 1) !important;
  color: #fff !important;
}

.tabs {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  width:100%;
  max-width: 950px;
}

.studentFilters,.adminStudentFilters{
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 50px;
}

.adminStudentFilters{
  flex-direction: column;
  gap: 24px;
}

.questions {
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: column;
  min-width: 80%;
  flex-wrap: wrap;
  gap: 16px;
}

.accordionContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.accExpText {
  position: absolute;
  font-size: 14px;
  right: 0;
  top: 50%;
  color: #335cff;
  margin-right: 3px;
  margin-top: -11px;
}

.accExpandIcon{

}

.accExpanded{
  transform: rotate(180deg);
}

.accQuesDataBox {
  display: flex;
  gap: 20px;
  width: 100%;
}

.accQuesDataBox .accQuesImage,
.accAnswerImage img {
  min-width: 104px;
  width: 104px;
  height: 104px;
  object-fit: cover;
  border-radius:14px !important;
  display: block;
  background: #f7f7f7;
}

.accAnswerImage img{
  cursor: pointer;
}

.accQuesHeader {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.accQuesTop{
  display: flex;
  justify-content: space-between;
}

.accQuesId{
  display: flex;
  align-items: center;
}

.accQuesId, .accQuesId *{
  color: rgba(196, 196, 196, 1);
  font-size: 12px;
  font-weight: 500;
}

.accChoiceQues{
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.accQuesChoices{
  color: rgba(119, 119, 119, 1);
  font-size: 16px;
  font-weight: 500;
  line-height: 160%;
  padding-left: 32px;
  display: flex;
  gap: 15px;
}

.accQuesTitle {
  display: flex;
  align-items: self-start;
  color: rgba(119, 119, 119, 1);
  font-size: 16px;
  font-weight: 500;
  gap: 8px;
  padding-right: 20px;
  width: 100%;
}

.accQuesAnsImg{

}

.accQuesText,.accQuesAns{
  display: flex;
  flex-direction: column;
  gap: 10px;
  line-height: 150%;
}

.accQuesTitle img{
  width: 24px;
  display: block;
  position: relative;
  top: 1px;
}

.accQuesTitle p {
  margin: 0;
}

.questionInfo {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #99a0ae;
  position: relative;
  top: -2px;
}

.questionInfo span {
  line-height: 1;
}

.accQuesMetaRow{
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 30px;
  padding-left: 30px;
  margin-top: -5px;
}

.questionMeta{
  display: flex;
  gap: 4px;
  padding-right: 30px;
  padding-left: 30px;
}

.questionMeta *{
  font-size: 14px;
  color: rgba(196, 196, 196, 1);
  font-weight: 500;
}

.questionFeedback{
  color: rgba(69, 176, 246, 1);
  margin-left: 5px;
  cursor: pointer;
}

.imgQuestionContainer {
  display: flex;
  gap: 8px;
  position: relative;
}

.accQuesAnswerBox {
  width: 100%;
  padding: 4px 4px 4px 5px;
  border-radius: 12px;
  border: 1px solid rgba(229, 229, 229, 1);
  background: rgba(247, 247, 247, 1);
}

.accQuesAnswerBox span{
  font-weight: 500;
  color: rgba(119, 119, 119, 1);
}

.accQuesAnsRow{
  display: flex;
  width: 100%;
  align-items: center;
  gap: 8px;
}

.accQuesAnsRow img:first-child{
  border-radius: 0 !important;
  position: relative;
  top: -1px;
}

.accQuesAnsRow img:last-child{
  margin-left: auto;
}

.accAnswerLabel {
  color: #0E121B;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.accAnswerContent{
  padding: 10px 26px 15px;
}

.accAnswerData {
  display: flex;
  gap: 15px;
}

.accAnswerImage {
}

.accAnswerText {
  color: rgba(161, 161, 161, 1);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  width: 100%;
  word-break: break-all;
}

.accAnswerText p{
  margin-top: 0;
  margin-bottom: 8px;
}

.accQuesDelete{
  display: flex;
  justify-content: end;
  margin: 10px;
}

.accQuesDelete button{
  width: 140px;
}

.centerImgContainer img,
.imgQuestionContainer img {
  border-radius: 8px;
  display: block;
}

.centerImgContainer {
  display: flex;
  justify-content: center;
}

.addQuestionTitle {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0px auto 40px;
  gap: 16px;
  max-width: 790px;
  text-align: center;
}

.addQuestionTitle h1 {
  margin: 0;
  font-size: 36px;
  font-style: normal;
  font-weight: 700;
  line-height: 100%;
  color: rgba(75, 75, 75, 1);
}

.addQuestionTitle div {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  color: rgba(161, 161, 161, 1);
  line-height: 160%;
}

.exampleQuestion {
  font-size: 16px;
  font-weight: 500;
  color: rgba(119, 119, 119, 1);
  margin-bottom: 20px;
}

.exampleQuestion a {
  color: rgba(107, 204, 9, 1);
  cursor: pointer;
}

.getAnswerBox{
  width: 248px;
  margin-left: auto;
}

.quesRemainBox{
  padding-top: 24px;
  display: none;
  flex-direction: column;
  gap: 12px;
}

.quesRemainTotal{
  font-size: 14px;
  font-weight: 700;
  color: rgba(119, 119, 119, 1);
  text-align: center;
}

.quesRemainTotal span{
  color: rgba(69, 176, 246, 1);
}

.quesRemainBox button{
  min-width: 100%;
}

.contentImgDisplayed {
  display: flex;
  align-items: center;
  gap: 20px;
}

.exampleQuestionBox {
  display: flex;
  align-items: center;
  gap: 20px;
}

.filters {
  min-width: 400px;
  gap: 16px;
  display: flex;
  align-items: center;
}

.adminQuesFilters{
  width: 100%;
  align-items: center;
  justify-content: center;
}

.adminQuesFilters > div{
  width: auto;
}

.adminQuesFilters :global(.toggleButtonGroup){
  width: 300px;
}

.studentRangeFilter,
.studentSearch{

}

.linkName {
  text-decoration: none;
  cursor: pointer;
  color: #6bcc09 !important;
}

.emptyQuesMsgBox{
  border: 1px solid rgba(229, 229, 229, 1);
  padding: 50px;
  border-radius: 16px;
  text-align: center;
  flex-direction: column;
  display: flex;
  gap: 20px;
}

.emptyQuesMsgBox img{
  max-width: 100%;
}

.eqmInfoBox{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  text-align: center;
  max-width: 710px;
  margin: 0 auto;
  padding-bottom: 20px;
}

.eqmInfoBox h2{
  color: rgba(75, 75, 75, 1);
  font-weight: 700;
  font-size: 24px;
  line-height: 140%;
  letter-spacing: 0;
}

.eqmInfoBox div{
  color: rgba(161, 161, 161, 1);
  font-weight: 500;
  font-size: 16px;
  line-height: 160%;
}

.emptyQuesMsgBox button{
  max-width: 246px;
  padding: 6px 20px 8px;
  margin: 0 auto;
  width: 100%;
}

@media screen and (max-width: 1100px) {
  .addQuestionModal {
    width: 100%;
  }
}

@media screen and (max-width: 959px) {
  .accQuesChoices{
    flex-direction: column;
    display: flex;
    gap: 4px;
  }
}

@media screen and (max-width: 900px) {
  .adminStudentFilters, .studentFilters {
    flex-direction: column;
    gap: 12px;
    padding-bottom: 10px;
  }
}

@media screen and (max-width: 630px)
{
  .studentFilters .filters,
  .adminStudentFilters .filters{
    flex-direction: column;
    gap: 12px;
  }

  .toggleButtonGroup,
  .studentSearch,.studentRangeFilter{
    width:100%!important;
    max-width: 100% !important;
  }

  .studentRangeFilter div{
    width:100% !important;
  }
}

@media screen and (max-width: 659px) {
  .addQuestionModal textarea{
    font-size: 12px;
  }
  .exampleQuestion, .exampleQuestion *{
    font-size: 12px;
  }
  .getAnswerBox{
    margin-left: 0;
    margin-right: 0;
    width: 100%;
  }

  .getAnswerBox button{
    width: 100%;
  }

  .quesRemainBox{
    display: flex;
  }
}

@media screen and (max-width: 600px)
{
    .adminStudentFilters,.studentFilters{
      margin-bottom: 20px;
    }

  .accQuesDataBox .accQuesImage,
  .accAnswerImage img {
    min-width: 69px;
    width: 69px;
    height: 69px;
  }

  .accQuesDataBox{
    flex-direction: column;
  }

  .accQuesMetaRow{
    padding: 0;
  }

  .accQuesAnsRow span,.accAnswerText{
    font-size: 12px;
  }

  .accAnswerContent {
    padding: 10px 16px 15px;
  }

  .toggleButtonGroup{
    height: auto;
  }

  .toggleButtonGroup *{
    font-size:  12px !important;
  }

  .toggleButton{
    height: 35px;
  }

    .addQuestionTitle h1 {
      font-size: 32px;
    }

    .addQuestionModal {
      padding: 0px;
    }

    .accExpText,
    .accQuesTitle {
      font-size: 12px;
    }

    .accExpText {
      margin-top: -9px;
      font-size: 11px;
      right: -2px;
    }

    .questionInfo {
      flex-direction: column;
      font-size: 9px;
      top: 0px;
      align-items: stretch;
      gap: 3px;
    }

    .questionInfo span {
      padding: 0;
      border: 0;
    }

    .addQuestionTitle {
      margin-bottom: 30px;
    }

    .addQuestionTitle div {
      font-size: 15px;
    }
}

@media screen and (max-width: 549px)
{
  .accQuesDataBox{
    gap: 10px;
  }

  .accQuesMetaRow{
    flex-direction: column;
    justify-content: end !important;
    gap: 7px;
    align-items: end;
  }

  .emptyQuesMsgBox{
    padding: 30px;
  }

  .emptyQuesMsgBox img{
    max-width: 70%;
    display: block;
    margin: 0 auto;
  }
}

@media screen and (max-width: 399px)
{
  .emptyQuesMsgBox{
    padding: 15px;
  }

  .eqmInfoBox{
    padding-bottom: 0;
  }

  .eqmInfoBox h2{
    font-size: 18px;
  }

  .eqmInfoBox div{
    font-size: 12px;
    line-height: 1.3;
  }

  .addQuestionTitle h1 {
    font-size: 20px;
  }

  .addQuestionTitle div {
    font-size: 14px;
  }

  .accAnswerText, .accAnswerText p,
  .accQuesAns, .accQuesText p,.accQuesText{
    font-size: 13px !important;
  }

  .accQuesTitle{
    padding-right: 0;
  }

  .accQuesAnsImg{
    width: 18px !important;
  }

  .accQuesMetaRow *{
    font-size: 12px;
  }
}
