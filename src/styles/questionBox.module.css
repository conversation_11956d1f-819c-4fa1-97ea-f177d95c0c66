.questionBox {
  width: 60%;
  max-width: 900px;
  min-width: 700px;
  height: 500px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  box-shadow: 0px 0px 30px 4px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  padding: 40px;
  gap: 40px;
}

.questionNumber {
  color: #8e8e8e;
  font-weight: bold;
  font-size: 14px;
}

.questionText {
  font-size: 18px;
  font-weight: bolder;
}

.questionOptionsBox {
  display: flex;
  justify-content: space-between;
}

.questionOptions {
  display: flex;
  width: 90%;
  flex-direction: column;
  gap: 30px;
}

.option {
  display: flex;
  width: 95%;
  align-items: center;
}

.optionBox {
  border: 2px solid #e0e0e0;
  background-color: #f5f5f5;
  border-radius: 5px;
  word-wrap: none;
  font-size: 12px;
  font-weight: 600;
  width: 100%;
  padding: 10px 10px;
}

.btnContainer {
  display: flex;
  gap: 20px;
  margin-right: 2%;
  margin-top: 10px;
  justify-content: flex-end;
}

.iphoneDivider {
  background-color: aqua;
}

@media screen and (max-width: 960px) {
  .questionBox{
    width: 100%;
    max-width: calc(100% - 30px);
    margin: 0 15px;
    min-width: inherit;
    padding: 30px;
  }
}

@media screen and (max-width: 600px) {
  .questionBox{
    height: auto;
    gap: 20px;
  }

  .questionOptionsBox {
    flex-direction: column;
    gap: 20px;
  }

  .questionOptions{
    gap: 20px;
    width: 100%;
  }

  .btnContainer{
    margin-top: 15px;
  }
}


@supports (-webkit-touch-callout: none) and (not (translate: none)) {
  .questionBox,.questionOptions {
    display: grid;
  }

  .questionOptions:not(:last-child) {
    margin-bottom: 20px;
  }

  .btnContainer {
    display: grid;
     grid-auto-flow: column;
  }
}