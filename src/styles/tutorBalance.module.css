.container {
  max-width: 888px;
  padding: 0 15px;
  margin: 0 auto;
}

.pendingWithdrawalInfo{

}

.heading {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.earningTwoCols {
  max-width: 100%;
  margin: 0 auto;
  display: flex;
  gap: 20px;
}

.earningLeftBox {
  min-width: 280px;
  max-width: 280px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.earningRightBox {
  width: 100%;
}

.balanceCard,
.withdrawCard {
  padding: 20px;
  background-color: #fff;
  border-radius: 16px;
  border: 1px solid rgba(229, 229, 229, 1);
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.withdrawCard {
  text-align: center;
  gap: 9px;
}

.withdrawTitle {
  color: rgba(75, 75, 75, 1);
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px;
}

.withdrawText {
  color: rgba(161, 161, 161, 1);
  font-size: 16px;
  font-weight: 500;
  line-height: 160%;
}

.greeting {
  font-size: 18px;
  color: #555;
  margin-bottom: 10px;
}

.balanceLabel {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  color: rgba(161, 161, 161, 1);
}

.balanceAmount {
  font-size: 58px;
  font-weight: 700;
  line-height: 1;
  color: rgba(75, 75, 75, 1);
  margin-top: -10px;
  margin-bottom: -3px;
}

.totalEarnBox,
.linkedMethodBoxContainer {
  margin-top: 18px;
  padding: 8px 10px;
  border-radius: 8px;
  background: rgba(247, 247, 247, 1);
  color: #525866;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.totalEarnBox{
  margin-top: 0;
}

.linkedMethodBoxContainer {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0 10px;
  padding: 0;
  background: none;
  gap: 15px;
}

.linkedMethodBox {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
}

.linkedMethodBox .totalEarnLabel{
  color: rgba(119, 119, 119, 1);
  font-size: 14px;
}

.totalEarnLabel,
.linkedMethodLabel {
  text-align: left;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  color: rgba(161, 161, 161, 1);
}

.linkedMethodLabel {
  font-weight: 700;
  color: rgba(75, 75, 75, 1);
}

.totalEarnAmount {
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: 24px;
  color: rgba(119, 119, 119, 1);
}

.withdrawButton {
  background-color: #0070ba;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.withdrawButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.withdrawButton:hover:not(:disabled) {
  background-color: #0056b3;
}

.amountWithdrawInput {
  height: 50px;
  padding: 0px 10px;
  width: 100%;
  border: 1px solid #cccccc;
  border-radius: 3px;
}

.amountWithdrawInput:focus {
  outline: none;
  border-color: black;
}

.fieldAgreeMsg{
  color: rgba(119, 119, 119, 1);
  font-weight: 700;
  font-size: 16px;
  line-height: 120%;
  letter-spacing: 0;
}

.fieldDescription {
  font-weight: 500;
  font-size: 14px;
  line-height: 140%;
  color: rgba(196, 196, 196, 1);
  margin-top: 5px;
}

.fieldDescription div {
  margin-top: 13px;
  font-size: 14px;
}

.withdrawLabel {
  color: rgba(119, 119, 119, 1);
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 10px;
  display: block;
}

.withdrawLabel,
.amountWithdrawBox {
  width: 50%;
}

.inputDescription {
  color: #525866;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  padding-left: 2px;
}

.amountWithdrawBox {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.fieldsRoundBox {
  margin-top: 21px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: #ffffff;
  border-radius: 16px;
}

.connectBorderBox {
  border-radius: 16px;
  border: 1px solid rgba(229, 229, 229, 1);
  padding: 20px;
  width: 100%;
}

.connectBoxHead {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.connectFieldsBox {
  padding: 20px;
  flex-direction: column;
  display: flex;
  gap: 32px;
  border-radius: 12px;
  background: rgba(250, 250, 250, 1);
}

.connectBoxTitle {
  font-size: 24px;
  font-weight: 700;
  color: rgba(75, 75, 75, 1);
  margin-bottom: 2px;
}

.connectBoxText {
  color: rgba(161, 161, 161, 1);
  font-weight: 500;
  font-size: 16px;
  line-height: 160%;
  letter-spacing: 0;
}

.payoutMethodBox {

}

.btnContainer {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  gap: 16px;
  width: 100%;
}

.btnContainer button{
  min-width: calc(50% - 8px);
}

.wireTransferBox {
  display: flex;
  align-items: center;
  gap: 20px;
  width: 100%;
  margin-bottom: 32px;
}

.bankDataBox {
  display: flex;
  align-items: center;
  gap: 16px;
}

.bankDetails {
}

.bankLabel {
  font-size: 16px;
  font-weight: 700;
  line-height: 20px;
  color: rgba(75, 75, 75, 1);
  margin-bottom: 6px;
}

.listTexts {
  margin: 0;
  padding: 0;
  font-weight: 500;
  line-height: 140%;
  color: rgba(119, 119, 119, 1);
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.listTexts li{
  position: relative;
  font-size: 14px;
  list-style: none;
  padding-left: 14px;
}

.listTexts li:before{
  content:'';
  position: absolute;
  background: rgba(107, 204, 9, 1);
  width: 6px;
  height: 6px;
  border-radius: 100%;
  left:2px;
  top: 50%;
  margin-top: -3px;
}

.connectLink {
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  line-height: 20px;
  letter-spacing: -0.0008em;
  color: #335cff;
  margin-left: auto;
}

@media screen and (max-width: 899px) {
  .earningTwoCols {
    flex-direction: column;
    gap: 20px;
  }

  .earningLeftBox {
    min-width: auto;
    max-width: inherit;
    flex-direction: row;
  }

  .balanceCard,
  .withdrawCard {
    flex: 1;
  }

  .linkedMethodBoxContainer{
    justify-content: start;
    gap: 20px;
  }

  .earningRightBox :global(.tableWrapper.multiColsTable){
    width: 100%;
  }
}

@media screen and (max-width: 695px) {
  .earningLeftBox {
    flex-direction: column;
  }

  .balanceAmount{
    font-size: 50px;
  }
}

@media screen and (max-width: 599px) {
  .earningRightBox :global(.tableWrapper.multiColsTable){
    table-layout: fixed;
  }

  .earningRightBox :global(.tableWrapper.multiColsTable tr th:last-child){
    width: 85px;
  }

  .balanceAmount{
    font-size: 40px;
  }
}