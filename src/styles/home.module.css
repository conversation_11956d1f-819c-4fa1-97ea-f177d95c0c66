.page,
.paymentPage {
  width: 100%;
  padding-bottom: 0px;
  background: #F6F6F8;
}

.pageTopSpace{
  padding-top: 60px;
  min-height: calc(100vh - 435px);
}

.pageTransitioning{

}

.layoutTransitionOverlay{

}

.paymentPage{
  min-height: 100vh;
  background: #fff;
}

.paymentPage h2{
  font-size: 36px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 0px;
  color: rgba(75, 75, 75, 1);
  text-align: center;
}

.page{
  background: #fff;
}

.pageContent{
  padding: 100px 0 55px;
}

.bannerContent{
  padding-top: 120px;
  background: url('/images/hero-background.svg') bottom center;
  background-size: cover;
  padding-bottom: 180px;
  position: relative;
  z-index: 1;
}

.bannerContent:before{
  content:'';
  position: absolute;
  left:0;
  right:0;
  top:-80px;
  height:130px;
  background: #6bcc09;
  z-index: 0;
}

.bannerWrapper{
  display: flex;
  gap: 66px;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.bannerLeftBox{
  max-width: 612px;
}

.bannerRightBox{

}

.bannerRightBox img{
  margin-right: -35px;
  margin-top: 86px;
}

.bannerTitle{
  font-weight: 700;
  font-size: 58px;
  line-height: 120%;
  letter-spacing: 0;
  color: #fff;
  margin: 0;
}

.bannerTitle span{
  font-size: inherit;
  line-height: inherit;
  font-weight: inherit;
}

.bannerText{
  font-weight: 500;
  font-size: 20px;
  line-height: 140%;
  letter-spacing: 0;
  color: #fff;
  max-width: 549px;
  margin-top: 10px;
}

.heroFormImage{
  max-width: 100%;
}

.askQuesForm{
  margin-top: 40px;
  border-bottom: 0;
  padding: 18px 20px 20px;
  background: #fff;
  border-radius: 16px;
  box-sizing: border-box;
}

.askQuesForm button{
  width: 100%;
}

.askFormFields{
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.askTopRow{
  display: flex;
  gap: 16px;
}

.askTopRow button{
  max-width: 110px;
  margin-left: auto;
}

.askTopRow button img{
  margin-right: 10px !important;
}

.askFormField{
  display: flex;
  align-items: center;
  gap: 5px;
}

.askFieldInput select,
.askFieldInput textarea{
  height: 138px;
  border-radius: 16px;
  gap: 10px;
  padding: 16px 24px;
  border: 1px solid #E5E5E5;
  font-weight: 500;
  font-size: 16px;
  line-height: 160%;
  letter-spacing: 0;
  outline: none;
  resize: none;
  width:100%;
  background: #fff;
  color: #777;
}

.askFieldInput textarea{
  border:0;
  padding: 0;
  height: 142px;
  color: #333 ;
  border-radius: 0px;
  font-size: 16px;
}

.askFieldInput select{
  border-radius: 30px;
  height: 49px;
  font-weight: 500;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0;
  padding: 16px 16px 16px 16px;
  /* Remove default arrow */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  color: #aaa;
  cursor: pointer;
  max-width: 112px;

  /* Custom styling */
  background: url('/icons/ask-form/select-down-arrow.svg') no-repeat right 15px center;
}

.askFormPreviewWrapper{
  position: relative;
  width: 48px;
  height: 48px;
}

.askFormPreviewImage{
  width: 48px;
  height: 48px;
  border: 1px solid #C4C4C4;
  display: block;
  border-radius: 8px;
  object-fit: cover;
}

.askFormPreviewRemove{
  position: absolute;
  top: -4px;
  right:-4px;
  cursor: pointer;
}

.askFieldLabel{
  font-weight: 500;
  font-size: 16px;
  line-height: 160%;
  letter-spacing: 0;
  color: #777777;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.askFieldInput{

}

.askBottomRow{

}

.askFormFileInput input{
  pointer-events: none !important;
}

.askFormFileInput label{
  display: flex;
  border: 1px solid rgba(229, 229, 229, 1);
  border-radius: 16px;
  padding: 7px 25px 7px 7px;
  align-items: center;
  font-size: 16px;
  color: #ddd;
  font-weight: 500;
  gap: 10px;
}

.askFileInputDisabled{
  pointer-events: none;
  opacity: 0.8;
}

.homeFormFileInput label{
  border: 0;
  padding: 0;
  border-radius: 0;
}

.askFormFileInput label img{
  cursor: pointer;
  display: block;
  border-radius: 100%;
}

.askFormFileInput label img:hover{
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.12);
}

.askFormFileInput span{
  width: 142px;
  border-radius: 8px;
  padding: 9px 9px;
  font-weight: 500;
  font-size: 16px;
  line-height: 160%;
  text-align: center;
  color: #777777;
  background: #D6F2CE;
  height: 44px;
  display: inline-block;
  cursor: pointer;
}

.askFormFileInput input{
  opacity: 0;
  position: absolute;
  cursor: pointer;
  max-width: 100px;
}

.container {
  height: 85vh;
  display: flex;
}

.englishPage {
  /*min-height: 100vh;*/
}

.historyPage{
  padding: 0 15px;
}

.paymentContainer {
  display: flex;
  justify-content: end;
  gap: 20px;
  position: relative;
  margin: 0 auto;
  max-width: 860px !important;
  padding-top: 40px;
}

.paymentContainer::-webkit-scrollbar {
  /* WebKit */
  width: 0px;
}

.back {
  height: fit-content;
  text-decoration: underline;
  cursor: pointer;
  display: flex;
  font-weight: 500;
  left: 25px;
  position: absolute;
  margin-top: 22px;
}

.back img {
  width: 13px;
  margin-right: 10px;
}

.payment {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  max-width: 580px;
}

.rightCards {
  width: 100%;
  max-width: 280px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card1,
.card2 {
  width: 100%;
  padding: 20px;
  border-radius: 16px;
  border: 1px solid rgba(229, 229, 229, 1);
  background: #FFF;
}

.divider {
  width: 100%;
  border-top: 1px solid rgba(229, 229, 229, 1);
  margin: 20px 0;
}

.card2 h3 {
  font-weight: 700;
  text-align: center;
  margin-top: 0;
  color: rgba(75, 75, 75, 1);
  font-size: 18px;
  margin-bottom: 15px;
}

.card2 ul {
  margin-bottom: 5px;
  padding-left:20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  color: rgba(119, 119, 119, 1);
  font-size: 14px;
  font-weight: 500;
}

.card2 ul li{
  font-size: 14px;
  font-weight: 500;
}

.row {
  display: flex;
  justify-content: space-between;
}

.title, .price  {
  color: rgba(75, 75, 75, 1);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.price {
  color: rgba(75, 75, 75, 1);
  font-weight: 700;
}

.btnContainer {
  width: 100%;
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.validExam {
  min-height: 100vh;
  height: auto;
  width: 100%;
}

.invalidExam {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
}

.demandGridTitle {
  text-align: center;
  margin-bottom: 45px;
  margin-top: 20px;
}

.demandGridTitle h2 {
  font-size: 26px;
  margin: 0;
}

.demandGridBoxes {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 30px;
}

.demandBox {
  text-align: center;
}

.demandBoxImg img {
  max-width: 100%;
  height: 210px;
  object-fit: cover;
  width: 100%;
  border-radius: 10px;
}

.demandBoxTitle {
  margin: 20px 0 15px;
  font-weight: 700;
}

.demandBoxText {
  font-size: 15px;
}

.sendResultBox,
.getStartedBox {
  align-items: center;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 15px;
  padding: 90px 20px;
  text-align: center;
  background: #ebebeb url("/images/world-map.svg") no-repeat;
}

.sendResultTitle {
  font-size: 26px;
  font-weight: 700;
  margin: 0;
}

.sendResultText {
  font-size: 15px;
  margin: 24px 0 32px;
  line-height: 1.7;
}

.sendResultButton {
  display: flex;
  gap: 10px;
}

.accurateResultBox {
  display: flex;
  gap: 30px;
  justify-content: space-between;
  align-items: center;
}

.accurateImage img {
  max-width: 100%;
}

.accurateData {
}
.accurateDataList {
  margin-top: 25px;
  display: grid;
  gap: 40px;
}

.accurateItem {
  display: flex;
  gap: 18px;
}

.accurateItemIcon img {
  width: 40px;
}

.accurateItemData {
}

.accurateItemTitle {
  font-weight: 700;
  font-size: 15px;
  margin-top: -3px;
  margin-bottom: 0;
}

.accurateItemTitle span {
  font-weight: 700;
  white-space: nowrap;
  height: 20px;
  font-size: 13px;
  line-height: 100%;
  text-align: center;
  letter-spacing: 0.035em;
  color: #222;
  padding: 4.25px 8px 2.75px;
  background: #eee;
  border-radius: 6px;
}

.accurateItemText {
  font-size: 14px;
  color: #555;
  margin-top: 3px;
}

.getStartedBox {
  padding: 50px 20px;
  background: #333;
  color: #fff;
}

/* Subjects Section */

.sectionHead{
  text-align: center;
  max-width: 580px;
  margin: 0 auto;
}

.sectionTitle{
  font-weight: 700;
  font-size: 58px;
  line-height: 120%;
  letter-spacing: 0;
  color: #4B4B4B;
}

.sectionText{
  font-weight: 500;
  font-size: 16px;
  line-height: 160%;
  letter-spacing: 0;
  color: #777777;
  padding-top: 10px;
}

.subjectsBox{
  padding-top: 54px;
  margin-top: 70px;
}

.subjectsList{
  margin-top: 60px;
  display: flex;
  gap: 20px;
}

.subjectItem{
  border-radius: 16px;
  padding: 24px 20px;
  border-width: 2px;
  border-style: solid;
  text-align: center;
  width: 100%;
  min-height: 294px;
  justify-content: end;
  display: flex;
  flex-direction: column;
}

.subjectPurple{
  border-color: #CE82FF;
}

.subjectPurple .subjectItemTitle{
  color: #CE82FF;
}

.subjectBlue{
  border-color: #45B0F6;
}

.subjectBlue .subjectItemTitle{
  color: #45B0F6;
}

.subjectOrange{
  border-color: #FF9600;
}

.subjectOrange .subjectItemTitle{
  color: #FF9600;
}

.subjectGreen{
  border-color:#6BCC09;
}

.subjectGreen .subjectItemTitle{
  color:#6BCC09;
}

.subjectItemIcon{

}

.subjectItemTitle{
  font-weight: 700;
  font-size: 24px;
  line-height: 140%;
  margin-top: 29px;
}

.subjectItemGrade{
  font-weight: 500;
  font-size: 16px;
  line-height: 160%;
  color: #777777;
  margin-top: 10px;
}

/* How It Works Section */

.howItWorksBox{
  padding-top: 60px;
  margin-top: 120px;
}

.hiwWrapper{
  display: flex;
  justify-content: space-between;
}

.hiwLeftBox{
  width: 451px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-bottom: 20px;
}

.hiwLeftBox .sectionText{
  margin-bottom: 40px;
}

.hiwLeftBox button{
  width: 100%;
  max-width: 246px;
}

.hiwRightBox{
  width: 100%;
  max-width: 589px;
  display: flex;
  flex-direction: column;
  gap: 40px;
  padding-top: 0px;
}

.hiwRightBox button{
  display: none;
}

.hiwStepBox{
  display: flex;
  gap: 20px;
}

.hiwStepNum{
  min-width: 70px;
  height: 70px;
  color: #fff;
  background: #6BCC09;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px;
  font-weight: 700;
}

.hiwStepInfo{
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 0px;
  max-width: 420px;
}

.hiwStepTitle{
  font-size: 20px;
  font-weight: 700;
  color: #6BCC09;
  line-height: 140%;
}

.hiwStepText{
  line-height: 160%;
  font-size: 16px;
  font-weight: 500;
  color: #777777;
}

.curveBgWrapper img{
  width: 100%;
  max-width: 100%;
  display: block;
}

.recentQuesBox {
  margin-top: 170px;
}

.recentQuesWrapper{
  background-color: #66cc00;
  padding: 35px 0 38px;
}

.recentQuesBox .sectionHead * {
  text-align: center;
  color: white;
}

.recentQuesGrid {
  margin-top: 60px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
}

.recentQuesCard {
  background-color: white;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.recentQuesCardInfo{
  padding: 10px 20px 24px;
}

.recentQuesQSection,
.recentQuesASection {
  display: flex;
  align-items: center;
  gap: 16px;
  font-weight: bold;
}

.recentQuesASection p{
  color: #777777;
  font-weight: 500;
  font-size: 18px;
}

.recentQuesQTitle{
  font-weight: 500;
  font-size: 18px;
  line-height: 140%;
  color: #777777;
  margin-top: 6px;
}

.recentQuesASection{
  margin-top: 18px;
}

.recentQuesQLabel,
.recentQuesALabel {
  background-color: #eee;
  border-radius: 8px;
  min-width: 40px;
  height: 40px;
  font-size: 20px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #777777;
}

.recentQuesALabel {
  background-color: #D6F2CE;
  color: #6BCC09;
  font-weight: 700;
  font-size: 20px;
}

.recentQuesSubject {
  border-radius: 0 0 8px 8px;
  font-size: 16px;
  font-weight: 500;
  padding: 7px 20px 5px;
  position: relative;
  top: 0px;
}

.recentQuesMath {
  background-color: #D5EEFF;
  color: #45B0F6;
}

.recentQuesScience {
  background-color: #FAEEFF;
  color: #CE82FF;
}

.recentQuesLanguageArts {
  background-color: #FFF4E9;
  color: #FF9600;
}

.recentQuesSocialStudies {
  background-color: #D6F2CE;
  color: #6BCC09;
}

/* Meet Tutor Section */

.meetTutorsBox {
  margin-top: 180px;
  text-align: center;
}

.tutorCardHighlight {
  color: #32b1ff;
  font-size: inherit;
}

.tutorCardSlider {
  margin-top: 60px;
}

.tutorCardCard {
  background: #fff;
  border: 2px solid #E5E5E5;
  border-radius: 16px;
  padding: 24px 20px;
  margin: 0 auto;
  text-align: left;
}

.tutorCardHead{
  display: flex;
  gap: 22px;
}

.tutorCardAvatar {
  width: 97px;
  height: 97px;
  background: #E5E5E5;
  border-radius: 16px;
}

.tutorCardNameRow {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tutorCardName {
  font-weight: 500;
  font-size: 20px;
  color: #777;
  margin-top: -2px;
}

.tutorCardStars {
  display: flex;
  align-items: center;
  gap: 9px;
  position: relative;
}

.tutorCardStar {
  color: #ffb400;
}

.tutorCardRating {
  color: #4B4B4B;
  font-weight: 500;
  margin-left: 7px;
}

.tutorCardAnswers {
  display: flex;
  gap: 8px;
  padding: 2px 6px;
  background: #E7F9E1;
  border-radius: 8px;
  margin-top: 2px;
}

.tutorCardAnswerCount {
  color: #6BCC09;
  font-weight: 700;
  font-size: 14px;

}

.tutorCardAnswerOn{
  color: #6BCC09;
  font-weight: 500;
  font-size: 14px;
}

.tutorCardBrand {
  color: #62ce00;
  font-weight: bold;
}

.tutorCardDetails {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tutorCardDetails > div img{
  position: relative;
  top: -1px;
}

.tutorCardDetails > div {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #777;
  gap: 14px;
}

.tutorCardNavBottom {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 24px;
}

.tutorCardNavBottom button{
  background: none;
  border:0;
}

.tutorCardArrow {
  cursor: pointer;
}

/* Become Tutor Section */

.becomeTutorBox{
  margin-top: 150px;
}

.becomeTutorWrapper{
  padding: 55px 0 20px;
  background: #6BCC09;
  position: relative;
}

.becomeTutorWrapper img{
  position: absolute;
  right: 0;
  max-width: 440px;
  bottom: -57px;
}

.becomeInfoBox{
  color: #fff;
  max-width: 890px;
}

.becomeInfoBox .sectionTitle,
.becomeInfoBox .sectionText{
  color: #fff;
}

.becomeInfoBox .sectionText{
  margin-bottom: 41px;
}

.becomeInfoBox button{
  width:100%;
  max-width: 274px;
}

/* Pricing Section */

.pricingSectionBox {
  padding-top: 60px;
  margin-top: 120px;
  text-align: center;
}

.planListing{
  margin-top: 50px;
}

.planContainer {
  text-align: center;
  padding: 2rem;
}

.planTitle {
  font-size: 2rem;
  margin-bottom: 1.5rem;
}

.planCards {
  margin-top: 50px;
}

.planOnlyCards {
  margin-top: 50px;
  display: flex;
  gap: 24px;
}

.planCard {
  border-radius: 16px;
  padding: 20px 20px 24px 20px;
  flex: 1 1 300px;
  border: 2px solid #e0e0e0;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.planHeader {
  display: flex;
  align-items: center;
  position: relative;
  width: calc(100% + 40px);
  margin-top: -20px;
  border-radius: 10px 10px 0 0;
  height: 68px;
  justify-content: center;
  box-sizing: content-box;
  margin-left: -20px;
  margin-right: auto;
}

.planHeader h3{
  color: #fff;
  font-size: 32px;
  font-weight: 700;
}

.planQuestions {
  font-size: 1.25rem;
  font-weight: 600;
}

.planPopular {
  right:-10px;
  top: 33px;
  background: rgba(255, 150, 0, 1);
  color: #fff;
  padding: 2px 18px;
  font-size: 16px;
  font-weight: 700;
  border-radius: 0 20px 20px 0;
  position: absolute;
  transform: rotate(90deg);
  width: 99px;
  display: flex;
  justify-content: center;
}

.planFeatures,
.planSubjects {
  display: flex;
  flex-direction: column;
  gap: 22px;
  width: 100%;
  list-style: none;
  margin: 24px 0 0;
}

.planFeatures svg,
.planSubjects svg{
  position: relative;
  top: -2px;
  width: 25px;
}

.planSubjects{
  margin-top: 20px;
}

.planDataDivider{
  border-top: 1px solid #eee;
  margin-top: 18px;
  width:100%;
  opacity: 0.3;
}

.planFeature, .planSubject {
  color: #777777;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  gap: 13px;
  align-items: center;
}

.planSubject {
  font-weight: 500;
}

.planGrade {
  color: #eee;
  margin-left: -8px;
  font-weight: 500;
}

.planPrice {
  font-size: 36px;
  font-weight: 700;
  color: #777777;
  margin-top: 16px;
  margin-bottom: 16px;
}

.planCard button{
  width:100%;
}

/* Why Love Section */
.whyLoveBox {
  text-align: center;
  margin-top: 180px;
}

.whyLoveTitle {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: #333;
}

.whyLoveHighlight {
  color: #6BCC09;
  font-size: inherit;
}

.whyLoveGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-top: 26px;
}

.whyLoveItem {
  padding: 24px;
  border-radius: 16px;
  border: 2px solid;
  background-color: white;
  transition: transform 0.2s;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
}

.whyLoveIcon img{
  display: block;
}

.whyLoveText {
  font-size: 20px;
  font-weight: 700;
  color: #777777;
  line-height: 135%;
}

.whyLoveBoxBlue {
  border-color: #45B0F6;
}

.whyLoveBoxGreen {
  border-color: #6BCC09;
}

.whyLoveBoxPurple {
  border-color: #CE82FF;
}

.whyLoveBoxOrange {
  border-color: #FF9600;
}

/* Top Grade Section */
.topGradeBox {
  margin-top: 190px;
}

.topGradeBox button{
  width:100%;
  max-width: 216px;
}

.topGradeWrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.topGradeImage{
  position: relative;
  width: 50%;
}

.topGradeImage img{
  left: -80px;
  position: absolute;
  top: 18px;
  max-width: calc(100% + 50px);
}

.topGradeContent {
  flex: 1;
  max-width: 530px;
}

.topGradeContent .sectionHead{
  text-align: left;
  margin: 0;
  max-width: inherit;
}

.topGradeContent .sectionHead .sectionTitle span{
  font-size: inherit;
  display: block;
}

.topGradeContent .topGradeImage{
  display: none;
}

.topGradeContent .topGradeImage img{
  position: relative;
  max-width: 100%;
  right: 0;
  top: 0;
}

.topGradeSteps {
  list-style: none;
  padding: 0;
  margin-top: 40px;
  display: flex;
  flex-direction: column;
  gap: 55px;
  margin-bottom: 45px;
}

.topGradeStep {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.topGradeNumber {
  min-width: 70px;
  height: 70px;
  font-weight: 700;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #fff;
  position: relative;
}

.topGradeSeptInfo{
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
  top: 2px;
}

.topGradeNumber1 .topGradeNumber{
  background-color: #45B0F6;
  box-shadow: 0px -4px 0px 0px #088DCC inset;
}

.topGradeNumber1 .topGradeNumber:after,
.topGradeNumber2 .topGradeNumber:after{
  width:8px;
  height: 32px;
  border-radius: 16px;
  background: #45B0F6;
  content:'';
  position: absolute;
  box-shadow: 0px -4px 0px 0px #088DCC inset;
  left:0;
  right:0;
  bottom: -42px;
  margin: 0 auto;
}

.topGradeNumber2 .topGradeNumber:after{
  background: #CE82FF;
  box-shadow: 0px -4px 0px 0px #B460EB inset;
}

.topGradeNumber1 .topGradeStepTitle{
  color: #45B0F6;
}

.topGradeNumber2 .topGradeNumber{
  background-color: #CE82FF;
  box-shadow: 0px -4px 0px 0px #B460EB inset;
}

.topGradeNumber2 .topGradeStepTitle{
  color: #CE82FF;
}

.topGradeNumber3{
  margin-top: -10px;
}

.topGradeNumber3 .topGradeNumber{
  background-color: #FF9600;
  box-shadow: 0px -4px 0px 0px #D8830A inset;
}

.topGradeNumber3 .topGradeStepTitle{
  color: #FF9600;
}

.topGradeStepTitle {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
}

.topGradeDescription {
  font-size: 16px;
  color: #777777;
  font-weight: 500;
  line-height: 160%;
  padding-left: 2px;
}

.topGradePoints{
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 27px;
  max-width: 488px;
  margin-bottom: 40px;
}

.topGradeItem{
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.topGradeItemTitle{
  font-weight: 700;
  font-size: 20px;
  line-height: 140%;
  letter-spacing: 0;
  color: #45B0F6;
}

.topGradeItemText{
  font-weight: 500;
  font-size: 16px;
  line-height: 160%;
  color: #777777;
}

/* FAQ Section */
.faqQuestionsBox {
  margin-top: 180px;
}

.faqQuestionsBox .sectionTitle{
  text-align: center;
  max-width: 670px;
  margin: 0 auto;
}

.faqListing{
  max-width: 912px;
  margin: 47px auto 0;
}

.faqItem {
  border: 2px solid #E5E5E5;
  border-top: none;
  background: #fff;
}

.faqItem:first-child {
  border-top: 2px solid #E5E5E5;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

.faqItem:last-child {
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}

.faqQuestion {
  font-size: 16px;
  color: #777777;
  padding: 35px 24px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faqQuestionActive{
  color: #4B4B4B;
}

.faqAnswer {
  font-size: 16px;
  color: #777777;
  font-weight: 500;
  padding: 17px 24px 40px;
  padding-top: 0px;
  line-height: 160%;
  animation: fadeIn 0.1s ease;
  margin-top: -7px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.faqChevron {
  transition: transform 0.1s ease;
  transform: rotate(180deg);
}

.faqChevronOpen {
  transform: rotate(0deg);
}

/* Margins */

.subjectsBox{
  margin-top: 135px;
}

.howItWorksBox{
  margin-top: 140px;
}

.recentQuesBox,.becomeTutorBox{
  margin-top: 160px;
}

.meetTutorsBox{
  margin-top: 200px;
}

.pricingSectionBox{
  margin-top: 130px;
}

.faqQuestionsBox{
  margin-top: 190px;
}

@media screen and (max-width: 1499px)
{
  .becomeTutorWrapper img{
    max-width: 410px;
    bottom: -45px;
  }
}

@media screen and (max-width: 1349px)
{
  .becomeInfoBox {
    max-width: 750px;
  }

  .becomeInfoBox .sectionTitle{
    line-height: 1.1;
  }

}

@media screen and (max-width: 1229px)
{
  .pageContent {
    padding-top: 71px;
  }

  .bannerTitle span{
    display: block;
  }

  .bannerWrapper {
    gap: 0px;
  }

  .bannerLeftBox {
    min-width: 570px;
  }

  .bannerText{
    max-width: 530px;
  }

  .bannerRightBox {
    padding-top: 0;
    flex: 1 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .bannerRightBox img{
    min-width: 100% !important;
    margin-right: 0;
    margin-top: 0px;
  }

  .bannerContent{
    padding-bottom: 130px;
  }

  .askQuesForm {
    max-width: 450px;
  }

  .askFieldInput textarea {
    height: 107px;
  }

  .sectionTitle {
    font-size: 48px;
    line-height: 126%;
  }

  .subjectsBox {
    margin-top: 60px;
    padding-top: 10px;
  }

  .subjectsBox .sectionText{
    max-width: 500px;
  }

  .subjectsList {
    margin-top: 40px;
  }

  .subjectItem {
    min-height: 280px;
  }

  .subjectItemTitle {
    font-size: 18px;
    margin-top: 20px;
  }

  .subjectItemGrade {
    font-size: 14px;
    margin-top: 5px;
  }

  .howItWorksBox {
    margin-top: 80px;
    padding-top: 5px;
  }

  .hiwLeftBox {
    min-width: 50%;
  }

  .hiwLeftBox .sectionText {
    max-width: 392px;
    margin-bottom: 30px;
  }

  .hiwStepNum {
    min-width: 60px;
    height: 60px;
  }

  .hiwRightBox {
    gap: 25px;
  }

  .hiwStepTitle {
    font-size: 24px;
  }

  .hiwStepText {
    font-size: 16px;
    line-height: 160%;
  }

  .recentQuesBox {
    margin-top: 25px;
    padding: 50px 0 0;
  }

  .recentQuesGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .recentQuesCardInfo {
    padding: 16px 16px 20px;
  }

  .recentQuesQTitle, .recentQuesASection p {
    font-size: 18px;
  }

  .recentQuesQLabel, .recentQuesALabel {
    min-width: 35px;
    height: 35px;
    font-size: 18px;
  }

  .meetTutorsBox {
    margin-top: 70px;
  }

  .recentQuesGrid{
    margin-top: 50px;
  }

  .tutorCardSlider {
    margin-top: 25px;
  }

  .tutorCardCard {
    padding: 13px 15px 15px;
  }

  .tutorCardAvatar {
    width: 65px;
    height: 65px;
  }

  .tutorCardHead {
    gap: 10px;
  }

  .tutorCardNameRow {
    gap: 6px;
  }

  .tutorCardName {
    font-size: 18px;
  }

  .tutorCardStars {
    gap: 7px;
  }

  .tutorCardStar img {
    width: 16px;
  }

  .tutorCardAnswers {
    margin-top: -2px;
  }

  .tutorCardAnswers span {
    font-size: 12px;
  }

  .tutorCardDetails{
    gap: 14px;
  }

  .tutorCardDetails img {
    width: 20px;
  }

  .tutorCardDetails div {
    font-size: 14px;
  }

  .tutorCardNavBottom{
    margin-top: 20px;
  }

  .tutorCardNavBottom img{
    width: 49px;
  }

  .becomeTutorBox{
    margin-top: 10px;
    padding: 50px 0 40px;
  }

  .becomeInfoBox{
    max-width: 50%;
  }

  .becomeTutorWrapper{
    padding: 20px 0;
  }

  .becomeTutorWrapper img {
    bottom: -35px;
    max-width: 420px;
  }

  .becomeInfoBox .sectionText {
    font-size: 18px;
    margin-bottom: 40px;
  }

  .pricingSectionBox {
    margin-top: 30px;
    padding-top: 0;
    text-align: center;
  }

  .planCards {
    margin-top: 35px;
  }

  .planCard{
    padding: 16px 16px 20px 16px;
  }

  .planHeader{
    height: 50px;
    width: calc(100% + 36px);
    top: 2px;
    margin-left: -18px;
  }

  .planHeader h3 {
    font-size: 24px;
  }

  .planPopular{
    padding: 2px 18px 3px;
    font-size: 12px;
    width: 74px;
    top: 23px;
  }

  .planFeatures svg, .planSubjects svg {
    width: 20px;
    top: -1px;
  }

  .planFeature, .planSubject{
    font-size: 14px;
  }

  .planFeatures, .planSubjects{
    gap: 12px;
  }

  .planPrice{
    font-size: 32px;
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .planGrade {
    font-size: 14px;
  }

  .whyLoveBox {
    margin-top: 85px;
  }

  .whyLoveItem{
    padding: 20px;
  }

  .whyLoveText{
    font-size: 16px;
    font-weight: 500;
  }

  .topGradeBox {
    margin-top: 85px;
  }

  .topGradeSteps {
    gap: 60px;
    margin-bottom: 40px;
  }

  .topGradeNumber {
    min-width: 60px;
    height: 60px;
  }

  .topGradeSeptInfo {
    display: flex;
    gap: 7px;
    top:4px;
  }

  .topGradeStepTitle{
    font-size: 24px;
  }

  .topGradeDescription{
    font-size: 14px;
  }

  .topGradeImage img {
    top: 60px;
    max-width: 85%;
    left: 0;
  }

  .faqQuestionsBox{
    margin-top: 85px;
  }

  .faqQuestionsBox .sectionTitle{
    max-width: 490px;
  }

  .faqListing{
    max-width: 680px;
    margin: 40px auto 0;
  }

  /* Margins */

  .subjectsBox{
    margin-top: 135px;
  }

  .howItWorksBox{
    margin-top: 140px;
  }

  .recentQuesBox{
    margin-top: 100px;
  }

  .becomeTutorBox{
    margin-top: 90px;
  }

  .meetTutorsBox{
    margin-top: 150px;
  }

  .pricingSectionBox{
    margin-top: 100px;
  }

  .faqQuestionsBox{
    margin-top: 150px;
    padding-bottom: 30px;
  }

  .topGradeBox{
    margin-top: 150px;
  }
}

@media screen and (max-width: 1159px) {
  .paymentContainer {
    flex-direction: column;
    display: block;
    margin: 0px;
    padding-top: 20px;
  }

  .paymentPage {
    margin: 0 auto;
  }

  .payment,
  .rightCards {
    max-width: 100% !important;
  }

  .back {
    position: relative;
    left: 0;
    width: 100px;
  }

  .rightCards {
    margin-top: 40px;
    gap: 30px;
  }

  .accurateResultBox {
    display: flex;
    flex-direction: column-reverse;
  }
}

@media screen and (max-width: 959px)
{
  .pageTopSpace{
    min-height: calc(100vh - 348px);
  }

  .tutorCardAnswers span {
    font-size: 14px;
  }

  .pageContent{
    padding-top: 10px;
    padding-bottom: 50px;
  }

  .bannerLeftBox{
    min-width: inherit;
  }

  .bannerContent{
    padding-bottom: 100px;
  }

  .bannerWrapper{
    display: flex;
    flex-direction: column-reverse;
  }

  .bannerText{
    max-width: 100%;
  }

  .bannerLeftBox{
    max-width: 100%;
    text-align: center;
  }

  .bannerRightBox{
    text-align: center;
  }

  .bannerRightBox img {
    max-width: 75% !important;
    margin: 0 auto;
    min-width: inherit !important;
  }

  .askQuesForm{
    max-width: 600px;
    margin: 40px auto 0;
  }

  .subjectsList{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }

  .subjectItemTitle{
    margin-top: 29px;
  }

  .hiwWrapper{
    display: flex;
    flex-direction: column;
  }

  .hiwLeftBox{
    padding-bottom: 40px;
    text-align: center !important;
    margin: auto;
    width: 100%;
    max-width: 580px;
  }

  .hiwLeftBox button{
    margin: auto;
  }

  .hiwLeftBox .sectionText{
    max-width: 100%;
    margin-bottom: 20px;
  }

  .hiwRightBox{
    padding-top: 0px;
    margin: auto;
    max-width: 500px;
  }

  .hiwRightBox button{
    display: block;
    margin-top: 20px;
  }

  .becomeTutorBox{
    padding-top: 40px;
  }

  .becomeInfoBox{
    max-width: 100%;
    text-align: center;
  }

  .becomeTutorWrapper img{
    display: none;
  }

  .whyLoveGrid{
    grid-template-columns: repeat(2, 1fr);
  }

  .topGradeWrapper .topGradeImage{
    display: none;
  }

  .topGradeWrapper .topGradeContent *{
    text-align: center;
  }

  .topGradePoints{
    margin-left: auto;
    margin-right: auto;
  }

  .topGradeWrapper .topGradeContent .topGradeImage{
    display: block;
    width:100%;
  }

  .topGradeWrapper .topGradeContent .topGradeImage img{
    margin-top: -20px;
  }

  .topGradeWrapper .topGradeContent button{
    margin: auto;
  }

  .topGradeContent{
    max-width: 100%;
    text-align: center;
  }

  .topGradeSteps{
    margin-top: 30px;
  }
}

@media screen and (max-width: 767px) {
  .demandGridBoxes {
    grid-template-columns: 1fr;
  }

  .sendResultBox,
  .getStartedBox {
    padding: 40px 25px 50px;
    margin-top: 0;
  }

  .demandGridTitle h2,
  .sendResultTitle {
    font-size: 23px;
  }

  .planOnlyCards{
    flex-direction: column;
    gap: 15px;
  }

  .topGradeWrapper .topGradeContent .sectionHead{
    text-align: center;
  }

  .topGradePoints{
    max-width: 100%;
    text-align: center;
  }

  .topGradeWrapper .topGradeContent button{
    margin: auto;
    display: flex;
  }
}

@media screen and (max-width: 659px)
{
  .pageTopSpace{
    min-height: calc(100vh - 240px);
  }

  .card1,
  .card2{
    padding: 15px;
  }

  .askFormFileInput label span,
  .askFormFileInput label div{
    font-size: 11px !important;
    align-items: center;
    display: flex;
    justify-content: center;
    height: 30px;
    width: inherit;
  }

  .askFormFileInput label img{

  }

  .pageContent {
    padding-top: 10px;
    padding-bottom: 40px;
  }

  .bannerRightBox img {
    max-width: 220px !important;
  }

  .bannerTitle{
    font-size: 32px;
    line-height: 126%;
  }

  .bannerText{
    font-size: 14px;
    line-height: 140%;
    max-width: 100%;
  }

  .askQuesForm {
    margin-top: 15px;
    padding: 13px 15px 15px;
  }

  .askQuesForm .askFormFields button{
    font-size: 0 !important;
    min-width: 55px;
    max-width: 55px;
    padding: 6px 12px 8px;
  }

  .askQuesForm .askFormFields button img{
    margin: 0 !important;
  }

  .askFieldInput textarea {
    height: 90px;
    font-size: 15px;
  }

  .subjectsBox{
    padding-top: 30px;
    margin-top: 30px;
  }

  .sectionTitle{
    font-size: 32px;
    line-height: 120%;
  }

  .sectionText{
    font-size: 14px;
    line-height: 140%;
    padding-top: 10px;
  }

  .subjectsList {
    margin-top: 30px;
  }

  .subjectItemIcon img{
    max-width: 70px;
  }

  .subjectItem {
    min-height: 200px;
    padding: 14px;
    min-width: 50%;
  }

  .subjectItemTitle {
    margin-top: 15px;
    font-size: 16px;
    line-height: 1.2;
  }

  .subjectItemGrade {
    font-size: 12px;
    margin-top: 2px;
  }

  .hiwLeftBox button{
    margin: auto;
  }

  .howItWorksBox {
    margin-top: 30px;
    padding-top: 40px;
  }

  .hiwStepNum{
    min-width: 50px;
    height: 50px;
    font-size: 25px;
    font-weight: bold;
  }

  .hiwStepInfo{
    gap: 6px;
  }

  .hiwStepTitle{
    font-size: 20px;
  }

  .hiwLeftBox{
    text-align: center;
  }

  .hiwStepText {
    font-size: 14px;
    line-height: 140%;
  }

  .hiwRightBox button {
    margin-top: 3px;
  }

  .recentQuesBox {
    margin-top: 60px;
    padding: 0;
  }

  .recentQuesBox .sectionText br{
    display: none;
  }

  .recentQuesGrid{
    grid-template-columns: repeat(1, 1fr);
    margin-top: 25px;
  }

  .recentQuesQTitle, .recentQuesASection p {
    font-size: 16px;
  }

  .recentQuesASection{
    margin-top: 6px;
  }

  .recentQuesSubject{
    font-size: 13px;
  }

  .meetTutorsBox{
    margin-top: 60px;
  }

  .meetTutorsBox .sectionText br{
    display: none;
  }

  .tutorCardNavBottom{

  }

  .becomeTutorBox{
    margin-top: 60px;
    padding-top: 0;
    padding-bottom: 0;
  }

  .becomeTutorWrapper {
    padding: 35px 0 40px;
  }

  .becomeInfoBox .sectionText {
    font-size: 14px;
    margin-bottom: 20px;
    padding-top: 20px;
  }

  .pricingSectionBox{
    padding-top: 30px;
    margin-top: 30px;
  }

  .planCard{
    padding: 16px 16px 12px 16px;
    min-height: 490px;
  }

  .whyLoveGrid {
    gap: 20px;
  }

  .whyLoveBox{
    margin-top: 60px;
  }

  .whyLoveItem{
    padding: 12px;
    gap: 15px;
  }

  .whyLoveText {
    font-size: 14px;
  }

  .topGradeBox{
    margin-top: 60px;
  }

  .topGradeBox button{
    max-width: 100%;
  }

  .topGradeBox .sectionText {
    padding-top: 0px;
  }

  .topGradeSteps {
    margin-top: 30px;
    margin-bottom: 20px;
  }

  .topGradePoints {
    gap: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
  }

  .topGradePoints .topGradeItemTitle{
    font-size: 18px;
  }

  .topGradePoints .topGradeItemText{
    font-size: 14px;
  }

  .topGradeBox button {
    width: 100%;
    max-width: 100%;
  }

  .faqQuestionsBox{
    margin-top: 60px;
  }

  .faqItem {
    border: 1px solid #E5E5E5;
    border-top: 0;
  }

  .faqItem:first-child {
    border-top: 1px solid #E5E5E5;
  }

  .faqListing{
    margin: 20px auto 0;
  }

  .faqQuestion{
    padding: 14px 16px;
    font-size: 14px;
  }

  .faqAnswer {
    padding: 7px 16px 15px;
    font-size: 12px;
  }

  .faqChevron{
    max-width: 18px;
  }

  .rightCards {
    margin-top: 20px;
    gap: 20px;
  }
}

@media screen and (max-width: 659px)
{
  .askFieldInput label{
    padding: 5px !important;
  }

  .subjectsBox{
    margin-top: 70px;
  }

  .howItWorksBox{
    margin-top:60px;
  }

  .pricingSectionBox{
    margin-top: 70px;
  }

  .recentQuesBox,.topGradeBox,.faqQuestionsBox{
    margin-top: 100px;
  }

  .faqQuestionsBox{
    padding-bottom: 40px;
  }

  .meetTutorsBox{
    margin-top: 100px;
  }

  .becomeTutorBox{
    margin-top: 90px;
  }
}

@media screen and (max-width: 499px) {
  .sendResultButton {
    flex-direction: column;
    gap: 15px;
  }
  .sendResultButton button {
    width: 200px;
  }
  .bannerTitle{
    font-size: 29px;
  }

  .paymentPage h2{
    font-size: 20px;
  }

  .pageTopSpace{
    min-height: calc(100vh - 230px);
  }
}

@media screen and (max-width: 399px)
{
  .pageTopSpace{
    padding-top: 30px;
  }

  .askTopRow{
    gap: 8px;
  }

  .askFormFileInput label img{
    width: 32px;
    height: 32px;
  }

  .askFormPreviewWrapper{
    width: 24px;
    height: 24px;
  }

  .askFormFileInput .askFormPreviewImage{
    width: 24px;
    height: 24px;
  }

  .askQuesForm .askFormFields button{
    min-width: 32px;
    height: 32px;
    padding: 6px 5px 8px;
    width: 37px;
    border-radius: 8px;
  }

  .askQuesForm .askFormFields button img{
    width: 16px;
  }
}


@supports (-webkit-touch-callout: none) and (not (translate: none)) {
  .box,
  .list,
  .rightCards {
    display: grid;
  }

  .rightCards {
    margin-top: 20px;
  }

  .item {
    display: grid;
    grid-auto-flow: column;
  }
}
