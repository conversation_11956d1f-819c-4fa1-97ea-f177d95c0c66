

.pageSections {
    display: flex;
    flex-direction: column;
    gap: 70px;
}

.profileAvatarBox {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.profileInfo {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.profileInfo .name {
    color: rgba(75, 75, 75, 1);
    font-weight: 700;
    font-size: 24px;
    line-height: 140%;
    text-align: center;
}

.profileInfo .joined {
    color: rgba(161, 161, 161, 1);
    font-weight: 700;
    font-size: 18px;
    line-height: 140%;
    letter-spacing: 0;
    text-align: center;
}

.sectionTitle {
    color: rgba(75, 75, 75, 1);
    font-weight: 700;
    font-size: 24px;
    line-height: 140%;
    letter-spacing: 0;
    margin: 0;
}

.statsAndChallenges {
    display: flex;
    justify-content: space-between;
    gap: 55px;
}

.statisticsBox {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    position: relative;
}

.statisticsBox:after {
    content: '';
    background: rgba(229, 229, 229, 1);
    width: 2px;
    bottom: 0;
    position: absolute;
    right: -55px;
    top: 54px;
}

.statistics {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.statBox {
    width: 100%;
    padding: 22px 20px;
    border-radius: 16px;
    border: 2px solid rgba(229, 229, 229, 1);
    display: flex;
    gap: 20px;
    align-items: start;
}

.statBox img {
    max-width: 24px;
    margin-top: 2px;
}

.statBoxInfo {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.statDivider {
    width: 2px;
    height: 100%;
}

.statBox span {
    color: rgba(75, 75, 75, 1);
    font-weight: 700;
    font-size: 20px;
    line-height: 140%;
    letter-spacing: 0;
}

.statBox small {
    color: rgba(119, 119, 119, 1);
    font-weight: 700;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0;
}

.challengesBox {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    max-width: 370px;
}

.dailyChallenges {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.challengeBox {
    width: 100%;
    padding: 24px 20px;
    border-radius: 16px;
    border: 2px solid rgba(229, 229, 229, 1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 100px;
}

.challengeBox span {
    font-weight: 700;
    font-size: 16px;
    line-height: 100%;
    color: rgba(75, 75, 75, 1);
}

.challengeBox .checkmark img {
    width: 35px;
}

.challengeBox button {
    min-width: 90px;
    padding: 6px 20px 8px;
}



.rankHeader {
    display: flex;
    gap: 20px;
}

.rankBody {
    background: rgba(250, 250, 250, 1);
    border-radius: 20px;
    padding: 20px;
    margin-top: 20px;
    display: flex;
    gap: 20px;
    min-height: 330px;
}

.rankBodyImg {
    width: 100%;
    max-width: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
}



.rankBodyContent {
    padding: 14px 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.rankResets {
    background: rgba(231, 249, 225, 1);
    border-radius: 8px;
    padding: 4px 8px;
    font-size: 16px;
    font-weight: 500;
    color: rgba(107, 204, 9, 1);
}

.rankBodyInfo {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.rankTitle {
    color: rgba(107, 204, 9, 1);
    font-weight: 700;
    font-size: 36px;
    line-height: 100%;
}

.rankSubtitle, .rankNote {
    color: rgba(161, 161, 161, 1);
    font-weight: 500;
    font-size: 16px;
    line-height: 160%;
}

.rankBodyProgress {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: -12px;
}

.rankBodyProgress .progressBar {
    background: #fff;
    height: 25px;
}

.rankBodyProgress .progressBarFill {
    background: rgba(107, 204, 9, 1);
}

.rankBodyFoot {
    display: flex;
    flex-direction: column;
    justify-content: end;
    gap: 18px;
    align-items: end;
    margin-top: auto;
}

.rankBodyFoot button {
    max-width: 230px;
    padding: 6px 22px 8px;
}

.progressText {
    font-weight: 700;
    font-size: 16px;
    line-height: 100%;
    color: rgba(119, 119, 119, 1);
}

.progressText span {
    color: rgba(69, 176, 246, 1);
}

.answerButton {
    background-color: #00b894;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 1rem;
    font-size: 1rem;
    cursor: pointer;
}

.ranksSection h2,
.achievementsSection h2 {
    margin-bottom: 1rem;
}

.ranks :global(.rankSliderNav) {
    display: none;
}

.rankCard {
    flex: 1 1 180px;
    padding: 26px 22px;
    border-radius: 16px;
    text-align: center;
    position: relative;
    border: 2px solid rgba(229, 229, 229, 1);
    min-height: 400px;
}

.rankCard.active {
    border-color: rgba(69, 176, 246, 1);
    background: #fff;
}

.rankCard img {
    width: 100%;
    max-width: 218px;
    margin: auto;
}

.rankCard .rankInfo {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 14px;
}

.rankCard .rankName {
    color: rgba(107, 204, 9, 1);
    font-weight: 700;
    font-size: 24px;
    line-height: 140%;
}

.rankCard .rankDescription {
    color: rgba(119, 119, 119, 1);
    font-weight: 700;
    font-size: 16px;
    line-height: 100%;
}

.rankRewards {
    margin-top: 45px;
    display: flex;
    flex-direction: column;
    gap: 21px;
    text-align: center;
    min-height: 104px;
}

.rewardTitle {
    color: rgba(75, 75, 75, 1);
    font-weight: 700;
    font-size: 18px;
    line-height: 140%;
    position: relative;
    width: fit-content;
    margin: 0 auto;
}

.rewardTitle:before,
.rewardTitle:after {
    content: '';
    width: 20px;
    height: 1px;
    background: rgba(75, 75, 75, 1);
    position: absolute;
    top: 50%;
}

.rewardTitle:before {
    left: -36px;
}

.rewardTitle:after {
    right: -36px;
}

.rewardName, .rewardBonus {
    font-weight: 700;
    font-size: 16px;
    line-height: 100%;
    color: rgba(119, 119, 119, 1);
}

.rewardBonus {
    color: rgba(69, 176, 246, 1);
    padding-top: 5px;
}

.locked {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 14px;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.locked img {
    max-width: 30px;
    position: absolute;
    right: 15px;
    top: 15px;
}

.achievements {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}



.achievement img {
    width: auto;
    max-width: inherit;
}

.achievement .statBoxInfo {
    width: 100%;
    gap: 9px;
}

.viewAll {
    margin-top: 20px;
    text-align: right;
    font-weight: 700;
    font-size: 16px;
    line-height: 122%;
    text-transform: uppercase;
    cursor: pointer;
    color: rgba(69, 176, 246, 1);
}

.progressWrap {
    margin-top: 12px;
}

.progressComplete {
    color: rgba(196, 196, 196, 1);
    font-weight: 700;
    font-size: 16px;
    line-height: 100%;
}

.progressBar {
    width: 100%;
    height: 15px;
    background-color: rgba(247, 247, 247, 1);
    border-radius: 32px;
    position: relative;
}

.progressBarFill {
    height: 100%;
    background-color: rgba(255, 194, 0, 1);
    border-radius: 32px;
    transition: width 0.3s ease;
}

.progressTooltip {
    position: absolute;
    top: -33px;
    left: auto;
    right: 60px;
    background: rgba(107, 204, 9, 1);
    color: rgba(255, 255, 255, 1);
    padding: 5px 14px 7px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 700;
    white-space: nowrap;
}

.progressTooltipArrow {
    position: absolute;
    bottom: -10px;
    left: 50%;
    margin-left: -10px;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid rgba(107, 204, 9, 1);
}

/* Complete Reg Box */

.completeRegBox {
    border-radius: 16px;
    background: rgba(229, 245, 255, 1);
    padding: 24px;
    color: rgba(69, 176, 246, 1);
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
}

.crBoxWaveImg {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.crBoxHead {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 516px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.crBoxTitle {
    font-weight: 700;
    font-size: 24px;
    line-height: 140%;
    text-align: center;
}

.crBoxText {
    font-weight: 500;
    font-size: 16px;
    line-height: 160%;
    text-align: center;
}

.crBoxProgress {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 52px;
    position: relative;
    z-index: 1;
}

.crBoxProgress .progressWrap {
    width: 100%;
}

.crBoxProgress .progressBar {
    height: 19px;
    background-color: #fff;
}

@media screen and (max-width: 1229px) {
    .rankName {
        min-height: 67px;
    }

    .rankDescription {
        min-height: 33px;
    }
}

@media screen and (max-width: 1099px) {
    .statBox {
        gap: 12px;
    }

    .statBox, .challengeBox {
        padding: 20px 18px;
    }

    .challengeBox {
        min-height: 96px;
    }

    .statsAndChallenges {
        gap: 20px;
    }

    .statisticsBox::after {
        right: -21px;
    }
}

@media screen and (max-width: 999px) {
    .pageSections {
        gap: 50px;
    }

    .statDivider {
        display: none;
    }

    .statsAndChallenges {
        flex-direction: column;
        gap: 50px;
    }

    .statisticsBox::after {
        display: none;
    }

    .challengesBox {
        max-width: 100%;
    }

    .dailyChallenges {
        flex-direction: row;
        gap: 20px;
    }
}

@media screen and (max-width: 899px) {
    .rankBody, .rankBodyFoot {
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .rankBodyImg {
        max-width: inherit;
    }

    .rankBodyImg img {
        max-width: 100%;
    }

    .rankBodyContent {
        text-align: center;
    }

    .rankBodyProgress {
        margin-top: -10px;
    }

    .rankBodyFoot {
        margin-top: 10px;
    }
}

@media screen and (max-width: 849px) {
    .achievements {
        grid-template-columns: repeat(1, 1fr);
    }
}

@media screen and (max-width: 699px) {
    .statistics {
        grid-template-columns: repeat(1, 1fr);
    }

    .dailyChallenges {
        flex-direction: column;
    }
}

@media screen and (max-width: 595px) {
    .completeRegBox {
        padding: 18px;
    }

    .sectionTitle, .crBoxTitle {
        font-size: 20px;
    }

    .crBoxText {
        font-size: 14px;
    }

    .rankResets {
        font-size: 12px;
    }

    .rankTitle {
        font-size: 32px;
    }

    .rankSubtitle, .progressText, .rankNote {
        font-size: 14px;
    }

    .rankBodyImg img {
        max-width: 80%;
    }

    .achievement img {
        max-width: 74px;
    }

    .statBox span {
        font-size: 18px;
    }

    .statBox small {
        font-size: 14px;
        line-height: 1.3;
    }

    .viewAll {
        font-size: 13px;
    }

    .crBoxProgress {
        margin-top: 10px;
        flex-direction: column-reverse;
        gap: 0;
    }

    .crBoxProgress .progressTooltip {
        position: relative;
        right: 0;
        white-space: inherit;
        top: 0;
        margin-top: 10px;
        text-align: center;
        font-size: 14px;
    }

    .progressTooltipArrow {
        display: none;
    }

    .pageSections, .statsAndChallenges {
        gap: 100px;
    }
}

@media screen and (max-width: 399px) {
    .profileInfo .name {
        font-size: 20px;
    }

    .profileInfo .joined {
        font-size: 14px;
        line-height: 120%;
    }

    .rankTitle {
        font-size: 26px;
    }

    .statBox, .challengeBox {
        padding: 14px 12px;
    }

    .achievement img {
        max-width: 60px;
    }
}