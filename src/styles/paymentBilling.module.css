.container {
  max-width: 1340px;
  margin: 0 auto;
  padding: 0 15px;
}

.innerContainer {
  display: flex;
  gap: 20px;
  justify-content: space-between;
}

.leftSide {
  width: 35%;
}

.divider {
  border: 1px solid #e0e0e0;
}

.rightSide {
  width: 100%;
}

.tabs {
  display: flex;
  margin-bottom: 2rem;
  width: 100%;
  border-bottom: 1px solid #e0e0e0;
}

.tab {
  margin-right: 1.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  cursor: pointer;
  color: #7d7d7d;
}

.activeTab {
  color: #000;
  border-bottom: 2px solid #000;
}

.title {
  font-size: 45px;
  font-weight: 700;
  margin-bottom: 8px;
}

.subtitle {
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 32px;
}

.planContainer {
  background-color: #f5f5f5;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 32px;
}

.planTitle {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}

.planName {
  font-size: 45px;
  font-weight: 700;
  margin-bottom: 8px;
}

.questionsRemaining {
  color: #666;
  margin-bottom: 16px;
}

.upgradeButton {
  background-color: #0070ba;
  color: #fff;
  height: 50px;
  width: 100%;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.linkedCard {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  padding: 24px;
  gap: 10px;
  border-radius: 8px;
  margin-bottom: 32px;
}

.linkedCardTitle {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 16px;
}

.cardDetails {
  display: flex;
  padding: 12px;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
  background: #eaeaea;
}

.cardName {
  font-weight: bold;
}

.cardNumber {
  color: #666;
}

.addCardButton {
  background-color: #0070ba;
  margin-top: 20px;
  height: 50px;
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.questionsTable {
  width: 100%;
  border-collapse: collapse;
}

.questionsTableTitle {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 16px;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.tableHeader {
  background-color: #f5f5f5;
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #ddd;
  background-color: #0070ba;
}

.tableRow {
  border-bottom: 1px solid #ddd;
}

.tableCell {
  padding: 12px;
}

@media screen and (max-width: 600px) {
  .tableCell {
    font-size: 14px;
  }
}
