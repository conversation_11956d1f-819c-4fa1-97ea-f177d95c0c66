.pageHeader{
    display: flex;
    gap: 20px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    max-width: 790px;
    margin: auto;
    padding-top: 60px;
    text-align: center;
}

.headTitle{
    color: rgba(75, 75, 75, 1);
    font-weight: 700;
    font-size: 36px;
    line-height: 100%;
}

.headText{
    color: rgba(161, 161, 161, 1);
    font-weight: 500;
    font-size: 16px;
    line-height: 160%;
}

.pageContent{

}

@media screen and (max-width: 799px) {
    .pageHeader br{
        display: none;
    }
}

@media screen and (max-width: 499px)
{
    .pageHeader{
        padding-top: 40px;
        margin-bottom: -20px;
        gap: 14px;
    }

    .headTitle{
        font-size: 22px;
    }

    .headText{
        font-size: 13px;
    }
}