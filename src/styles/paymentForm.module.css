.paymentForm {
  width: 100%;
  font-family: Arial, sans-serif;
}

.paymentForm h2,
.paymentForm h3 {
  margin-top: 0;
}

.paymentForm h3{
  color: #3F3F3F;
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.paymentForm h2{
  font-size: 36px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 25px;
  color: rgba(75, 75, 75, 1);
  text-align: center;
}

.menuDivider {
  background: gray;
  padding: 0;
  opacity: 0.3 !important;
}

.formGroup {
  margin-bottom: 16px;
}

.formGroup fieldset {
  border-color: #e0e0e0;
}

.helperText{
  color: rgba(196, 196, 196, 1);
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
}

.firstHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0px 0 30px;
}

.firstHeader h3 {
  font-size: 16px;
  font-weight: 700;
  color: rgba(75, 75, 75, 1);
  display: flex;
  align-items: center;
  gap: 14px;
}

.innerContainer {
  padding: 20px;
  flex-direction: column;
  display: flex;
  gap: 30px;
  border-radius: 12px;
  background: rgba(250, 250, 250, 1);
}

.innerContainer h3{
  font-size: 24px;
  font-weight: 700;
  color: rgba(75, 75, 75, 1);
  margin-bottom: 2px;
}

.formGroup label {
  color: rgba(119, 119, 119, 1);
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 10px;
  display: block;
}

.formGroup select,
.formGroup input {
}

.ccInfo {
  padding: 20px;
  background: #ffffff;
  border: 1px solid rgba(229, 229, 229, 1);
  border-radius: 12px;
  margin-top: 5px;
}

.cardIcons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.expiryTabs {
  min-width: 100px;
}

.expiryCvc {
  display: flex;
  width: 100%;
  gap: 16px;
}

.expiry {
  display: flex;
  width: 50%;
  gap: 16px;
}

.cvc {
  width: 50%;
}

.cvc label{
  display: flex;
  gap: 10px;
  align-items: center;
  min-height: 25px;
}

.cvc label img{
  cursor: pointer;
}

.btn {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 4px;
  color: #fff;
  background-color: #007bff;
  font-size: 16px;
  cursor: pointer;
}

.btnContainer {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  margin-top: 30px;
}

.btn:hover {
  background-color: #0056b3;
}

.cardInputWrap{
  display: flex;
  position: relative;
}

.cardInputWrap img{
  position: absolute;
  right: 17px;
  top: 50%;
  margin-top: -8px;
}

.cardInput {
  border: 1px solid rgba(229, 229, 229, 1);
  width: 100%;
  border-radius: 12px;
  height: 58px;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-sizing: border-box;
}

.cardInput > div {
  width: 100%;
}

@media screen and (max-width: 1159px) {
  .paymentForm {
    margin-top: 30px;
  }

  .btnContainer {
    justify-content: center;
  }
}

@media screen and (max-width: 1099px) {
  .paymentForm h2,
  .paymentForm h3 {
    font-size: 16px;
  }
}

@media screen and (max-width: 659px) {
  .cardInput{
    height: 45px;
  }

  .innerContainer{
    gap: 20px;
  }

  .cardIcons img{
    width: 40px;
  }

  .paymentForm{
    margin-top: 15px;
  }

  .paymentForm h3{
    font-size: 14px;
    gap: 10px;
  }

  .ccInfo{
    padding: 15px;
  }

  .helperText{
    font-size: 12px;
  }

  .firstHeader{
    margin-bottom: 20px;
  }

  .btnContainer button{
    width: 100% !important;
  }
}

@media screen and (max-width: 500px) {
  .expiryCvc {
    flex-direction: column;
  }

  .cvc {
    width: 100%;
  }

  .ccInfo{
    padding: 10px 0 0;
    border:0;
  }
}

@supports (-webkit-touch-callout: none) and (not (translate: none)) {
  .innerContainer,
  .expiryCvc {
    display: grid;
  }

  .paymentForm {
    margin-top: 20px;
  }

  .cvc {
    width: 100%;
  }
}
