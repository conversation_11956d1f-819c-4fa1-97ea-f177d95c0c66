.homePageContent{
  padding-top: 79px;
}

.homeWidgets button{
  font-size: 16px;
}

.topNavbar{
  padding: 15px 0;
  background: #6BCC09;
  position: fixed;
  z-index: 99;
  top:0;
}

.topNavbarFixed .leftOptions,
.topNavbarFixed .actionOptions button:first-child{
  display: none !important;
}

.hiddenOptions{
  display: none !important;
}

.adminNavigation .hiddenOptions{
  display: flex !important;
}

.myAccountIcon{
  cursor: pointer;
}

.appHeaderBar{
  padding: 15px 0;
  background: #6BCC09;
  position: relative;
  z-index: 99;
  top: 0;
}

.pageTopMargin{
  padding-top: 60px;
  min-height: calc(100vh - 435px);
}

.navWrapper{
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.navPublicWrapper{

}

.logo {
  height: 28px;
  margin-right: 10px;
}

.logoWrapper{
  cursor: pointer;
}

.mainLogo{
  display: block;
}

.drawerList {
  width: 250px;
}

.spacer {
  flex-grow: 0.5;
}

.menuIcon {
  background-color: #eaecf0;
  height: 35px;
  width: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.signoutOption {
  display: flex;
  cursor: pointer;
  gap: 20px;
}

.navigation{
  display: flex;
  align-items: center;
  gap: 20px;
}

.adminNavigation .leftOptions{
  gap: 20px;
  margin-left: auto;
  margin-right: 20px;
}

.leftOptions, .rightOptions, .actionOptions {
  display: flex;
  align-items: center;
  height:100%;
  gap: 30px;
}

.rightOptions{
  gap: 16px;
}

.actionOptions{
  gap: 16px;
}

.navigation .actionOptions button{
  font-size: 16px;
  text-transform: uppercase;
}

.navigation .actionOptions button:last-child{
  min-width: 238px;
}

.navbarOption {
  font-weight: 500;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0;
  color: #FFFFFF;
  cursor: pointer;
  transition: all 0.2s linear 0s;
  text-transform: uppercase;
}

.disabled{
  pointer-events: none;
  opacity: 0.8;
}

.navbarOption img{
  filter: grayscale(100%)!important;
  opacity: 0.8 !important;
  width: 18px;
  margin-left: auto;
}

.navbarOption span{
  font-size: 16px;
  font-weight: 700;
}

.navbarMobOption{
  display: none;
}

.navLoginOption{
  color: #45B0F6 !important;
  background: #F6FCFF;
  border-radius: 12px 12px 0 0;
}

.navLoginOption span{
  color: #45B0F6 !important;
}

.navRegOption{
  color: #6BCC09 !important;
  background: #E7F9E1;
  border-radius: 0 0 12px 12px;
}

.navbarOption:hover{
  color: #fff;
}

.adminNavigation .activeTab {
  position: relative;
}

.adminNavigation .activeTab:after{
  border-bottom: 2px solid #fff;
  position: absolute;
  bottom: -4px;
  content: '';
  left: 0;
  right: 0;
}

.navbarOption:hover img,
.activeTab img{
  filter: grayscale(0%) !important;
}

.roundButton{
  height: 38px;
  padding: 7px 8px 8px;
  font-weight: 400;
  border-radius: 8px;
  border: 1px solid #E1E4EA;
  background: #FFF;
  cursor: pointer;
  transition: all 0.2s linear 0s;
  box-shadow: 0px 0px 0px 1px #EBEBEB, 0px 1px 3px 0px rgba(143, 143, 143, 0.20), 0px -2.4px 0px 0px rgba(62, 62, 62, 0.04) inset;
}

.roundButton:hover{
  color: #335CFF;
}

.navMobileHead{
  align-items: center;
  justify-content: space-between;
  display: none;
  position: absolute;
  right: 15px;
  top: 8px;
}

.navMobileHead .myAccountIcon{
  width: 32px;
  display: block;
  margin-top: -2px;
  position: relative;
  top: -2px;
  margin-right: 15px;
}

.navMobileLogo{

}

.navMobileClose img{
  width: 35px;
  cursor: pointer;
}

/* Static Pages Navbar */

.staticTopNavbar{
  padding: 15px 0;
  background: #45B0F6 !important;
}

.staticTopNavbar .actionOptions button{
  padding-left: 34px;
  padding-right: 34px;
  min-width: 114px!important;
  font-size: 16px;
}

.staticTopNavbar .leftOptions{
  display: none;
}

@media screen and (max-width: 959px)
{
  .pageTopMargin{
    min-height: calc(100vh - 348px);
  }

  .homePageContent{
    padding-top: 130px;
  }

  .adminNavigation .leftOptions{
    gap: 0px;
    margin-right: 0;
  }

  .navbarMobOption{
    display: block;
  }

  .adminNavigation .activeTab:after,
  .adminNavigation .hiddenOptions{
    display: none !important;
  }

  .navWrapper{
    flex-direction: column;
    justify-content: start;
    align-items: start;
    gap: 10px;
  }

  .navPublicWrapper{
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .navPublicWrapper .navigation{
    width: auto;
  }

  .navigation{
    width: 100%;
  }

  .logoutAction{
    display: none !important;
  }

  .leftOptions{
    min-width: 160px;
    background: #fff;
    flex-direction: column;
    align-items: start;
    gap:0;
    border-radius: 12px;
    position: absolute;
    height: auto;
    z-index: 9;
    right:10px;
    top: 40px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    display:none;
  }

  .topNavbarFixed .leftOptions.showForceMenu,
  .showForceMenu{
    display: flex !important;
  }

  .showForceMenu .navbarOption{
    font-size: 12px;
  }

  .showForceMenu .navbarOption.activeTab {
    color: #6bcc09;
  }

  .topNavbarFixed .navMobileHead{
    display: none !important;
  }

  .topNavbarFixed .actionOptions{
    position: absolute;
    right: 0;
    text-align: right;
    display: flex;
    justify-content: end;
    top: 0;
  }

  .leftOptions::before {
    content: "";
    position: absolute;
    top: -9px;
    right: 12px;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 10px solid white; /* match popup bg */
    z-index: 1;
  }

  .leftOptions .navbarOption{
    padding: 8px 12px;
    width:100%;
    border-top: 1px solid #E5E5E5;
    color: #4B4B4B;
    text-transform: capitalize;
  }

  .leftOptions .navbarOption:hover{
    color: #6bcc09;
  }

  .leftOptions .navbarOption:first-child{
    border:0;
  }

  .leftOptions span,.navbarOption{
    font-size: 12px;
    font-weight: 700;
    height: 44px;
    align-items: center;
    display: flex;
  }

  .actionOptions{
    justify-content: space-between;
    width: 100%;
  }

  .navMobileHead {
    display: flex;
  }

  .staticTopNavbar .navigation{
    display: none;
  }

  .topNavbarFixed .mainLogo{
    position: relative;
    top: 4px;
  }

  .homeWidgets button, .navigation .actionOptions button, .navbarOption, .navbarOption span {
    font-size: 15px;
  }
}

@media screen and (max-width: 659px)
{
  .pageTopMargin{
    min-height: calc(100vh - 240px);
  }

  .homePageContent{
    padding-top: 113px;
  }

  .mainLogo{
    width: 85px;
  }

  .navMobileHead {
    display: flex;
    top: 4px;
  }

  .navMobileHead img{
    width: 30px;
  }

  .leftOptions{
    top: 35px;
  }

  .navigation .actionOptions button:last-child {
    min-width: 100px;
  }

  .topNavbarFixed .actionOptions{
    right: 10px;
  }

  .mainLogo{
    margin-top: 2px;
  }

  .navigation .actionOptions button{
    font-weight: 700;
  }

}

@media screen and (max-width: 600px) {
  .logo {
    height: 20px;
    display: block;
  }

  .homeWidgets button, .navigation .actionOptions button, .navbarOption, .navbarOption span {
    font-size: 12px;
  }
}

@media screen and (max-width: 499px) {
  .navigation .actionOptions button{
    text-transform: uppercase;
  }

  .pageTopMargin{
    min-height: calc(100vh - 230px);
  }
}

@media screen and (max-width: 399px) {
  .pageTopMargin {
    padding-top: 30px;
  }
}
