/* Search Widget */
.searchWrapper {
    display: flex;
    position: relative;
    width: 100%;
    height: 40px;
    background: #fff;
    border-radius: 8px;
}

.searchIcon {
    position: absolute;
    top: 50%;
    left: 12px;
    margin-top: -9px;
    cursor: pointer;
    z-index: 1;
}

.searchClear {
    position: absolute;
    top: 50%;
    right: 10px;
    margin-top: -10px;
    cursor: pointer;
    z-index: 1;
    width: 10px;
    height: 10px;
    display: inline-block;
    line-height: 1;
}

.searchClear img {
    width: 9px;
    display: inline-block;
}

.filterButton, .filterDateButton {
    padding: 8px 14px;
    border: 1px solid #e1e4ea;
    background: #fff;
    border-radius: 8px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    cursor: pointer;
    font-size: 14px;
    color: #0e121b;
}

.filterDateButton {
    color: #99A0AE;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: -0.08px;
    padding: 8px 7px 8px 10px;
    width: 250px;
    justify-content: start;
}

.filterButtonActive {
}

.filterButton b {
}

.filterButton:hover {
    border: 1px solid #b0bec5;
}

.filterDivider {
    border-top: 1px solid #E1E4EA;
    margin-left: -15px;
    width: calc(100% + 30px);
    margin-top: 3px;
}

.actionFooter {
    margin-top: 2px;
    display: flex;
    justify-content: space-between;
    gap: 8px;
}

.actionFooter button {
    width: 100%;
    height: 36px;
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
}

.filterCancel {
    box-shadow: none;
    border-radius: 8px;
    border: 1px solid #E1E4EA !important;
    background: #FFF;
}

.filterCancel:hover {
    border: 1px solid #aaa !important;
}

/* Filters Widget */
.filtersWrapper {
    position: relative;
}

.filterFieldsBox {
    position: absolute;
    padding: 15px;
    width: 310px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 1px;
    z-index: 1;
    left: 0;

    border-radius: 16px;
    border: 1px solid #E1E4EA;
    background: #FFF;
    box-shadow: 0px 16px 32px -12px rgba(14, 18, 27, 0.10);
}

.filterField {
}

/* Dates Widget */

.datesWrapper {
    width: 200px;
}

.pickerFooter {
    margin-top: 9px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    width: 100%;
    padding: 16px;
    border-top: 1px solid #F2F5F8;
}

.pickerFooter button {
    width: 100%;
}

/* Search Box Filter */
.searchBoxFilter {
    border-radius: 5px;
    border: 0;
    background: #f0eee9;
}

.fileInputContainer,
.userImg {
    display: flex;
    gap: 12px;
    flex-direction: column;
}

.fileInputContainer{
    gap: 20px;
}

.userImg {
    flex-direction: row;
}

.fileInputHolder {
    display: flex;
    align-items: center;
    gap: 8px;
}

.fileInputHolder div {
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    color: #99a0ae;
}

.fileInput {
    height: 33px;
    background: #fff;
    border-radius: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    color: #0e121b;
    padding: 8px 16px;
    justify-content: space-between;
    cursor: pointer;
    border: 1px solid #e1e4ea;
    gap: 4px;
}

.fileInput:hover {
    background: #f9f9f9;
}

.fileInput input {
    display: none;
}

.fileInput img {
}

.filePreviewWrapper {
    position: relative;
    width: 37px;
    height: 37px;
}

.fileUploadedMsg{
    font-size: 14px;
}

.fileMaxUploadSizeMsg{

}

.filePreviewImage{
    width: 37px;
    height: 37px;
    border: 1px solid #C4C4C4;
    display: block;
    border-radius: 8px !important;
    object-fit: cover;
    box-sizing: border-box;
    max-width: 100%;
    max-height: 100%;
}

.userImgModal {
    display: flex;
    padding: 0px;
    height: 104px;
    width: 104px;
    justify-content: center;
    border: 1px solid rgba(229, 229, 229, 1);
    border-radius: 14px;
    align-items: center;
    font-size: 12px;
    background: #f7f7f7;
}

.fileName {
    color: #1c1c1c;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: -0.011px;
    min-width: 150px;
    word-wrap: break-word;
}

.filePreview img {
    width: 24px;
    height: 24px;
    object-fit: cover;
    display: block;
}

.userImgModal img {
    width: 100%;
    height: 100%;
    border-radius: 14px;
    object-fit: cover;
    display: block;
}

.fileClear {
    position: absolute;
    right: 15px;
    top: 50%;
    margin-top: -8px;
    cursor: pointer;
}

.fileClear img {
    width: 16px;
    height: auto;
}

.profilePreview{
    width: 104px;
    height: 104px;
    position: relative;
}

.profilePreview > img{
    width: 104px;
    height: 104px;
    border-radius: 100%;
    object-fit: cover;
    border: 1px solid rgba(229, 229, 229, 1)
}

.profilePreview label img{
    position: absolute;
    cursor: pointer;
    bottom: -2px;
    right: -2px;
}

.profilePreview label input{
    opacity: 0;
    position: absolute;
    cursor: pointer;
    max-width: 50px;
}

.toggleRadioGroup {
    border: 0;
    display: flex;
    gap: 20px;
}

.toggleRadioButton {
    background: #f0eee9;
    border-radius: 6px !important;
    font-size: 11px;
    color: #003b51;
    border: 0;
    height: 42px;
    width: 200px;
}

.toggleRadioSelected {
    background: #003b51 !important;
    color: #fff !important;
}

.toggleButtonGroup {
    border: 1px solid #f0eee9;
    border-radius: 8px;
    padding: 1px;
    width: fit-content;
}

.toggleButton {
    padding: 2px 11px;
    font-size: 11px;
    color: #878a8d;
    border: 0;
    border-radius: 6px !important;
    text-transform: capitalize;
}

.toggleButton:hover {
    background: #fff;
    color: #003b51 !important;
}

.toggleButtonSelected {
    color: #003b51 !important;
    background: #f0eee9 !important;
}

.disableBox {
    pointer-events: none !important;
    opacity: 0.5 !important;
    filter: grayscale(100%);
    cursor: not-allowed;
}

.customSelectWrapper{
    display: flex;
    flex-direction: column;
    gap: 10px;
    position: relative;
}

.customSelectIcon{
    position: absolute;
    top: 50%;
    margin-top: -13px;
    left: 16px;
}

.selectContainer {
    position: relative;
    width: 100%;
    height: 60px;
}

.selectDisable {
    pointer-events: none;
    opacity: 0.8;
}

.select {
    width: 100%;
    border: 1px solid rgba(229, 229, 229, 1);
    border-radius: 12px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    font-size: 16px;
    background-color: white;
    cursor: pointer;
    color: rgba(75, 75, 75, 1);
    height: 58px;
    padding: 15px 26px 15px 16px;
    transition: all 0.1s linear 0s;
    font-family: inherit;
    font-weight: 500;
}

.select:hover {
    border-color: #b0bec5;
}

.selectDark {
    border-radius: 4px;
    border-color: #dfdedb;
    color: #003b51;
}

.selectHasIcon{
    padding-left: 47px;
}

.selectHighlightField{
    background: rgba(255, 252, 242, 1);
}

.selectFilled {
    border-radius: 4px;
    border: 0;
    background: #f8f7f3;
    font-size: 10px;
    padding: 5px 20px 5px 12px;
    min-width: 68px;
}

.customSelectMsg{
    color: rgba(196, 196, 196, 1);
    font-weight: 500;
    font-size: 14px;
    line-height: 140%;
}

.customArrow {
    position: absolute;
    right: 10px;
    top: 1px;
    pointer-events: none;
    bottom: 0;
    display: flex;
    align-items: center;
}

.customArrow img {
    width: 9px;
    height: 6px;
}

.dashToggleGroup {
    border: 0;
    display: flex;
    gap: 20px;
}

.dashToggleButton {
    background: #fff;
    border: 1px solid #003b51 !important;
    border-radius: 6px !important;
    font-size: 12px;
    color: #003b51;
    height: 30px;
    width: 120px;
    display: flex;
    gap: 25px;
    padding: 5px;
    align-items: center;
}

.dashToggleButton img {
    width: 6px;
}

.dashToggleSelected {
    background: #003b51 !important;
    color: #fff !important;
}

.dashToggleSelected img {
    filter: brightness(0) invert(1);
}

.notifyBox{
    border-radius: 12px;
    padding: 14px 16px;
    display: flex;
    gap: 16px;
    text-align: left;
    align-items: center;
}

.notifyBox.info {
    border: 2px solid rgba(69, 176, 246, 1);
    background: rgba(246, 252, 255, 1);
}

.notifyBox.warning {
    border: 2px solid rgba(241, 209, 82, 1);
    background-color: rgba(255, 252, 242, 1);
}

.notifyBox.error {
    border: 2px solid #ef4444;
    background-color: #fef2f2;
}

.notifyBox.success {
    border: 2px solid #22c55e;
    background-color: #f0fdf4;
}

.notifyIcon img{
    display: block;
}

.notifyInfo{
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.notifyTitle{
    font-weight: 700;
    font-size: 16px;
    line-height: 140%;
    color: rgba(75, 75, 75, 1);
}

.notifyText{
    font-weight: 500;
    font-size: 14px;
    line-height: 140%;
    color: rgba(119, 119, 119, 1);
}

.toggleTextBox{
    position: relative;
    padding-right: 20px;
    min-height: 70px;
}

.truncateTwoLines {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.toggleTextIcon{
    border: 0;
    background: none;
    position: absolute;
    top:0;
    right:0;
    cursor: pointer;
}

.toggleTextIcon img{
    width: 32px;
}

.toggleTextIconOpen{
    transform: rotate(-180deg);
}

@media screen and (max-width: 1199px) {
    .filterFieldsBox {
        left: auto;
        right: 0;
    }
}

@media screen and (max-width: 1259px) {
    .searchWrapper {
        max-width: inherit;
    }
}

@media screen and (max-width: 659px) {
    .selectContainer{
        height: 47px;
    }
    .select {
        height: 45px;
        font-size: 14px;
        padding: 12px 26px 12px 13px;
    }
    .customSelectIcon{
        left: 10px;
    }
    .selectHasIcon{
        padding-left: 40px;
    }
    .customArrow{
        right: 4px;
    }

    .fileMaxUploadSizeMsg{
        display: none !important;
    }
}

@media screen and (max-width: 399px) {
    .notifyBox{
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        gap: 10px;
    }
}