.tutorPage {
  height: 100%;
  overflow-y: hidden;
}

.tutorAddQuesPage{

}

.tutorContainer{
  padding: 0 15px;
  max-width: 888px;
  margin: 0 auto;
}


.attentionBox {
  padding: 25px 32px;
  background: #ffffff;
  border: 1px solid #e1e4ea;
  border-radius: 16px;
  max-width: 530px;
  margin: 40px auto 0;
  display: flex;
  margin-bottom: 40px;
  flex-direction: column;
  gap: 32px;
}

.attentionBoxHeader {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attentionBoxTitle {
  font-weight: 600;
  font-size: 32px;
  line-height: 39px;
  text-align: center;
  color: #0e121b;
}

.attentionBoxText {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #0e121b;
}

.attentionBoxList {
  flex-direction: column;
  display: flex;
  gap: 10px;
}

.attentionListItem {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 18px;
  gap: 10px;
  background: #f1f5f9;
  border: 0.66px solid #e1e4ea;
  border-radius: 8px;
}

.attentionItemWrap {
  display: flex;
  gap: 7px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.attentionItemTitle {
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #000000;
  display: flex;
  gap: 7px;
}

.attentionItemTitle div {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.attentionItemTitle div a {
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #005bd3;
}

.addQuestionTitle {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0px auto 40px;
  gap: 16px;
  max-width: 790px;
  text-align: center;
}

.addQuestionTitle h1 {
  margin: 0;
  font-size: 36px;
  font-style: normal;
  font-weight: 700;
  line-height: 100%;
  color: rgba(75, 75, 75, 1);
}

.addQuestionTitle div {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  color: rgba(161, 161, 161, 1);
  line-height: 160%;
}

.outerBorder {
  position: relative;
  height: 100%;
}

.header1 {
  height: 10%;
  display: flex;
  align-items: center;
  padding: 10px;
  justify-content: space-between;
  position: relative;
  border-bottom: 1px solid rgb(231, 232, 234);
}

.questionCount {
  font-weight: 600;
}

.questionList {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding-bottom: 0px;
}

.questionBox,
.applicantQuestionBox {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 22px 20px;
  gap: 18px;
  background: #ffffff;
  border: 1px solid rgba(229, 229, 229, 1);
  border-radius: 16px;
  position: relative;
}

.applicantFooter {
  display: flex;
  align-items: center;
}

.questionBox {
  padding-bottom: 24px;
}


.questionDetails {
  display: flex;
  font-weight: bolder;
  color: rgb(84, 91, 94);
  gap: 10px;
}

.btnContainer,
.acceptAnsBtnContainer {
  display: flex;
  gap: 10px;
  justify-content: end;
  width: 100%;
}

.btnContainer {
  margin-top: 8px;
}



.modalBtnContainer {
  margin-top: 30px;
}

.question {
  font-size: 14px;
  font-weight: 500;
  line-height: 16.94px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  width: 100%;
  padding-top: 20px;
}

.questionCost {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  padding: 6px 11px;
  background: rgba(231, 249, 225, 1);
  border-radius: 10px;
  font-size: 16px;
  font-weight: 700;
  line-height: 19.36px;
  color: rgba(107, 204, 9, 1);
  position: absolute;
  right: 20px;
  top: 20px;
}

.questionImage img {
  display: block;
  border-radius: 14px;
  cursor: pointer;
  background: #f7f7f7;
}

.questionInfoBox {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 40px;
  font-size: 14px;
  width: 100%;
  max-width: 570px;
}

.questionInfoBox span {
  display: block;
}

.questionInfoBox span p{
  margin-top: 0px;
  margin-bottom: 8px;
}

.questionMetaBox {
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #99a0ae;
  position: relative;
  top: 0px;
}

.questionMetaBox span {
  border-left: 1px solid #bbb;
  padding: 0 5px;
  display: inline-block;
  line-height: 1;
}

.questionMetaBox span:first-child {
  border: 0;
  padding-left: 0;
}

.questionWrap {
  display: flex;
  gap: 20px;
}

.currentQues {
  color: rgb(84, 91, 94);
  font-weight: bolder;
}

.answer {
  display: flex;
  align-items: center;
}

.addToProfile {
  display: flex;
  flex-direction: column;
  height: 70vh;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.availQuestion {
  border: 1px solid #e1e4ea;
  display: flex;
  width: 100%;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  justify-content: space-between;
  font-size: 14px;
}

.questionsModal {
  display: flex;
  flex-direction: column;
  gap: 14px;
  max-height: 620px;
  overflow-y: auto;
  padding-right: 5px;
}

.questionText {
  width: 70%;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
}

.questionDate {
  color: #666;
}

.availQuestionsBtns {
  display: flex;
  position: relative;
  top: 20px;
  right: 20px;
  justify-content: flex-end;
}

.accQuesDataBox {
  padding: 30px 30px;
  display: flex;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
}

.ansQuesBox {
  border: 1px solid rgb(231, 232, 234);
  background: #fff;
}

.accQuesDataBox img,
.accAnswerImage img {
  min-width: 76px;
  width: 266px;
  height: 186px;
  object-fit: cover;
  border-radius: 8px;
  display: block;
}

.accQuesHeader {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 18px;
}

.quesPriceBox {
  border: 1px solid white;
  background-color: #523ffa;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px 12px;
  font-weight: bolder;
  border-radius: 10px;
  height: 40px;
}

.questionInfo {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #99a0ae;
  position: relative;
  top: -2px;
}

.accQuesMetaRow{
  display: flex;
  justify-content: end;
  width: 100%;
  margin-top: 10px;
}

.imgQuestionContainer {
  display: flex;
  gap: 8px;
  position: relative;
}

.accQuesAnswerBox {
  border-radius: 11px;
  border: 1px solid #e1e4ea;
  background: #f1f5f9;
  padding: 16px;
}

.accAnswerLabel {
  color: #0e121b;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.accAnswerData {
  display: flex;
  gap: 15px;
  margin-top: 12px;
}

.addQuestionModal {
  margin-top: 0px;
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 20px;
  padding: 20px 30px;
  border-radius: 16px;
}

.applicantAnswerBox {
  position: relative;
  margin-top: 40px;
}

.applicantAnswerBox h3 {
  font-size: 14px;
  margin-bottom: 7px;
  margin-top: 0;
}

.applicantAnswerOptionsBox {
  padding-top: 5px;
  display: flex;
  gap: 16px;
}

.applicantAnswerOption {
  padding: 13px;
  color: rgba(119, 119, 119, 1);
  cursor: pointer;
  border-radius: 14px;
  background-color:rgba(247, 247, 247, 1);
  font-size: 16px;
  width: 74px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  border: 1px solid rgba(196, 196, 196, 1);
}

.applicantAnswerOption:hover{
  border-color: rgba(107, 204, 9, 1);
}

.selectedAnswer{
  color: #fff;
  background: rgba(107, 204, 9, 1);
  border-color: rgba(107, 204, 9, 1);
  box-shadow: 0px -4px 0px 0px rgba(92, 179, 4, 1) inset;
}

@media screen and (max-width: 1100px) {
  .addQuestionModal {
    width: 100%;
  }
}

@media screen and (max-width: 767px) {
  .questionWrap{
    flex-direction: column;
  }
  .questionInfoBox{
    max-width: 100%;
  }
  .acceptAnsBtnContainer{
    flex-direction: column;
  }
  .question{
    padding-top: 0;
  }
}

@media screen and (max-width: 700px) {
  .applicantFooter {
    flex-direction: column;
    gap: 20px;
  }
}

@media screen and (max-width: 600px)
{
  .addQuestionTitle h1 {
    font-size: 32px;
  }

  .addQuestionTitle div{
    font-size: 14px;
  }

  .addQuestionModal {
    padding: 15px;
  }

  .questionWrap {
    flex-direction: column;
  }

  .questionMetaBox {
    margin-top: 10px;
  }

  .applicantAnswerBox {
    margin-top: 20px;
  }

  .addQuestionTitle {
    margin-bottom: 30px;
  }

  .questionInfoBox{
    padding-right: 0;
  }
}

@media screen and (max-width: 400px) {
  .btnContainer,
  .acceptAnsBtnContainer {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  .tutorAddQuesPage .acceptAnsBtnContainer{
    display: flex;
    flex-direction: column;
  }
}

@media screen and (max-width: 399px) {
  .addQuestionTitle h1{
    font-size: 20px;
  }
  .applicantQuestionBox{
    padding: 15px;
  }

  .questionCost{
    top: 15px;
    right: 15px;
    font-size: 12px;
    padding: 4px 11px;
  }

  .questionImage img{
    width: 69px !important;
    height: 69px !important;
    border-radius: 8px;
  }

  .questionInfoBox{
    gap: 20px;
  }
}
