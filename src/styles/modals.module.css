.modalHead {
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  position: relative;
}

.modalCloseIcon{
  position: absolute;
  cursor: pointer;
  top: 20px;
  right: 20px;
  z-index: 1;
}

.modalHeadIcon{
  padding-top: 15px;
  padding-bottom: 10px;
}

.modalHead h2{
  color: rgba(75, 75, 75, 1);
  text-align: center;
  font-size: 24px;
  font-style: normal;
  font-weight: 700 !important;
  line-height: 140%;
}

.modalContent{
  color: rgba(119, 119, 119, 1);
  font-weight: 500;
  font-size: 16px;
  line-height: 160%;
  text-align: center;
}

.modalTitle {
  color: rgba(75, 75, 75, 1);
  text-align: center;
  font-size: 24px;
  font-style: normal;
  font-weight: 700 !important;
  line-height: 140%;
  max-width: 100%;
}

.modalBlueTitle {
  color: #333;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 1.5;
  letter-spacing: -0.72px;
  margin-bottom: 18px;
}

.modalSmallTitle {
  font-size: 26px;
  margin: 0;
  font-weight: 700;
  color: #000;
}

.modalText {
  color: rgba(119, 119, 119, 1);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 160%;
  max-width: 330px;
}

.modalText a{
  color:#335CFF;
}

.modalBody {
  padding-top: 20px;
}

.sampleQuesBox {
  padding: 8px;
  background: rgba(247, 247, 247, 1);
  border-radius: 12px;
  display: flex;
  gap: 11px;
  border: 1px solid rgba(196, 196, 196, 1);
}

.sampleQuesBox img {
  border-radius: 8px;
  width: 69px;
  height: 69px;
  object-fit: cover;
}

.sampleQuesBox div {
  color: rgba(119, 119, 119, 1);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 160%;
  padding-top: 5px;
}

.modalFooter {
  padding-top: 20px;
  display: flex;
  justify-content: center;
}

.modalFooter button{
  min-width: 277px;
}

.modalFooterCols{
  display: grid;
  grid-template-columns: 0.45fr 1fr;
  gap: 16px;
}

.modalFooterCols button{
  min-width: calc(50% - 8px);
}

.modalCancelButton{
  border-radius: 8px;
  background: #FFF;
  box-shadow: 0px 0px 0px 0px #EBEBEB, 0px 1px 2px 0px rgba(143, 143, 143, 0.20), 0px -2.4px 0px 0px rgba(62, 62, 62, 0.04) inset;
  color: #444 !important;
}

.modalCancelButton span{
  border: 1px solid #eaecf0;
}

.modalCancelButton:hover{
  color: #335CFF !important;
  box-shadow: 0px 0px 0px 0px #EBEBEB, 0px 1px 2px 0px rgba(143, 143, 143, 0.20), 0px -2.4px 0px 0px rgba(62, 62, 62, 0.04) inset;
}

.modalForm {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modalFieldRow {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 7px;
}

.inputLabel {
  color: #344054;
  font-size: 14px;
}

.inputField {
  position: relative;
}

.inputField input {
  width: 100%;
}

.inputField img {
  cursor: pointer;
  width: 24px;
  position: absolute;
  top: 50%;
  margin-top: -12px;
  right: 8px;
  opacity: 0.6;
}

.modalInput {
  padding: 6px 12px 8px;
  font-size: 16px;
  border-radius: 6px;
  border: 1px solid #d0d5dd;
  letter-spacing: 0.4px;
  height: 48px;
  outline: none;
  padding-right: 42px;
}

.modalSubmit {
  background: #000;
  text-transform: capitalize;
  border-radius: 6px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.4px;
  box-shadow: none !important;
  color: #fff;
}

.footerActions {
  display: flex;
  gap: 20px;
  margin-top: 25px;
  justify-content: center;
  align-items: center;
  border-top: 1px solid #e1e4ea;
}

.webcamLoader {
  margin-top: 20px;
  color: #444;
}

.webcamBox {
  margin-top: 25px;
  border: 1px solid #000;
  border-radius: 8px;
}

.webcamBox video {
  max-width: 100%;
  display: block;
}

.btnContainer {
  display: flex;
  gap: 20px;
  margin-top: 30px;
}

.modalRow{
  display: flex;
  gap: 6px;
  flex-direction: column;
}

.modalRowTitle{
  font-size: 15px;
  font-weight: 600;
  color: #6bcc09 !important;
}

.modalRowText{
  font-size: 14px;
}

.reminderModal .modalHead{
  display: flex;
  gap: 20px;
  flex-direction: row;
  text-align: left;
  padding-top: 30px;
}

.remModalInfo{
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.remModalInfo .modalTitle{
  color: #fff;
}

.remModalInfo .modalText{
  color: #fff;
}

.reminderModal .modalFooter{
  padding-top: 24px;
}

.reminderModal .modalFooter button{
  min-width: 100%;
  background: rgba(255, 255, 255, 1) !important;
  color: rgba(69, 176, 246, 1);
  box-shadow: 0px -4px 0px 0px rgba(213, 238, 255, 1) inset;
}

/* Notification Modal */

.notificationModal .modalHead{
  padding-top: 24px;
}

.notificationModal .modalFooter{
  padding-bottom: 20px;
}

@media screen and (max-width: 700px) {
  .webcamBox video {
    max-width: 100%;
    width: 100%;
    height: auto;
    min-height: inherit;
  }
}

@media screen and (max-width: 549px) {
  .reminderModal .modalHead{
    flex-direction: column;
  }

  .remModalInfo{
    text-align: center;
  }

  .remModalInfo .modalTitle{
    max-width: inherit;
  }

  .remModalInfo .modalText{
    max-width: inherit;
  }
}

@media screen and (max-width:499px) {
  .modalTitle{
    font-size: 20px;
  }

  .modalText{
    font-size: 13px;
  }

  .modalHead{
    padding-top: 15px;
  }

  .modalHeadIcon{
    padding-bottom: 0;
    padding-top: 5px;
  }

  .modalHeadIcon img{
    width: 44px;
  }

  .sampleQuesBox div{
    font-size: 13px;
  }

  .modalFooter{
    padding-bottom: 10px;
  }

  .modalFooter button{
    min-width: 100%;
  }
}
/* 
@media screen and (max-width: 400px) {
    .webcamBox video{
        max-width: 10%;
        height: 300px;
        display: block;
    }
} */

@media only screen and (min-height: 736px) and (orientation: portrait) {
  .webcamBox {
    background-color: aqua;
  }
}

@supports (-webkit-touch-callout: none) and (not (translate: none)) {
  .modalForm,
  .modalFieldRow {
    display: grid;
  }

  .btnContainer {
    display: grid;
    grid-auto-flow: column;
  }
}
