.addQuestion {
  margin-top: 50px;
  border-radius: 16px;
  background: #FFF;
  box-shadow: 0px 1px 2px 0px rgba(10, 13, 20, 0.03);
  padding: 24px;
}

.languageSelectLabel,
.answerLabel,
.questionLabel {
  color: #0E121B;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px; 
  letter-spacing: -0.08px;
}

.answerLabel{
  padding-left: 2px;
}

.languagesBox {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  font-size: 14px;
}

.language {
  width: fit-content;
  padding: 5px 10px;
  display: flex;
  align-items: center;
  gap: 5px;
  border-radius: 3px;
  cursor: pointer;
  border: 1px solid #3f3f3f;
}

.active {
  background: #3f3f3f;
  color: white;
}

.questionBox {
  margin-top: 0px;
}

.questionInput,
.answerInput {
  width: 100%;
  height: 170px;
  resize: none;
  margin-top: 10px;
  border-radius: 8px !important;
  border: 1px solid #E1E4EA;
  background: #FFF;
  padding: 8px 10px;
  font-size: 14px!important;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  letter-spacing: -0.08px;
  font-family: inherit;
}

.questionInput:focus,
.answerInput:focus {
  outline: 1px solid #3f3f3f;
}

.answerInput {
  height: 70px;
}

.answerBox {
  margin-top: 10px;
}

.answer {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding-top: 8px;
  margin-left: -7px;
}

.answerBox .answer:first-child{
  padding-top: 0;
}

.answerVal {
  margin-top: 10px;
  width: 100%;
}

.errorNoAnswer {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
  color: red;
}

.btnContainer {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  justify-content: flex-end;
}

.btnContainerQuestionAdd {
  display: flex;
  gap: 20px;
  justify-content: space-between;
}
