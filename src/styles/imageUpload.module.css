.imageUploadBox{
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 20px;
}

.uploadFileInput{
    position: absolute;
    opacity: 0;
}

.uploadedImage{
    width: 160px;
    height: 160px;
    border-radius: 12px;
    object-fit: cover;
}

.uploadActions{
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.uploadButton, .uploadBorderButton{
    width: 175px;
    height: 42px;
    background-color: #3f3f3f;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    text-transform: none;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.uploadButton:hover{
    background-color: #878b93;
}

.uploadBorderButton{
    background-color: #fff;
    color: #3f3f3f;
    border: 1px solid #3F3F3F;
}

.uploadBorderButton:hover{
    background-color: #f7f7f7;
    color: #000;
    border: 1px solid #000;
}

@media screen and (max-width: 449px)
{
    .imageUploadWebBox{
        flex-direction: column;
        margin-top: 20px;
    }
}

@supports (-webkit-touch-callout: none) and (not (translate: none)) {
    .uploadActions,.imageUploadBox {
      display: grid;
     
    }
  }