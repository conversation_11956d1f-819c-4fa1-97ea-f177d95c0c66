.logoBox{
    padding: 0 0 14px 14px;
}

.tabsWrapper{
    position: relative;
    padding-right: 24px;
    margin-top: -65px;
}

.tabsWrapper:before{
    content:'';
    height:100%;
    width:1px;
    background: #E0E0E0;
    position: absolute;
    right:-1px;
    top:-100px;
}

.pageTabs{
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activeTab{
    background: #3F3F3F !important;
    color:#fff!important;
}

.pageTab a{
    color: #3F3F3F;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    padding: 14px;
    border-radius: 8px;
    display: block;
    transition: all 0.2s linear 0s;
    -webkit-transition: all 0.2s linear 0s;
    -moz-transition: all 0.2s linear 0s;
    -o-transition: all 0.2s linear 0s;
}

.pageTab a:hover{
    background: #eaecf0;
}

.paginationBox{

}

.pagiShowingBox{

}

@media screen and (max-width: 1199px) {
    .tabsWrapper{
        margin-bottom: 30px;
        padding-right: 0;
    }
    .tabsWrapper:before{
        display: none;
    }
    .pageTabs{
        gap: 5px;
    }
}

@media screen and (max-width: 999px) {
    .paginationBox{
        flex-direction: column;
        gap: 0px;
        padding-top: 25px;
    }

    .pagiShowingBox{
        display: none;
    }
}

@media screen and (max-width: 599px) {
    .pageTab a{
        padding: 11px;
        border-radius: 6px;
    }
}