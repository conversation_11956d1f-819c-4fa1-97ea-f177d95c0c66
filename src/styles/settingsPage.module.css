.settingsContainer {
  max-width: 888px;
  margin: 0 auto;
  padding: 0 15px;
}

.heading {
  font-size: 45px;
  font-weight: 700;
  margin-bottom: 1rem;
}

.tabs {
  display: flex;
  margin-bottom: 2rem;
  width: 45%;
  border-bottom: 1px solid #e0e0e0;
}

.tab {
  margin-right: 1.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  cursor: pointer;
  color: #7d7d7d;
}

.activeTab {
  color: #000;
  border-bottom: 2px solid #000;
}

.section {
  margin-top: 2rem;
  width: 45%;
}

.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.label {
  font-weight: 500;
}

.description {
  font-size: 0.85rem;
  color: #7d7d7d;
  margin-top: 0.25rem;
}

.toggle {
  width: 40px;
  height: 20px;
  border-radius: 12px;
  background: #e0e0e0;
  position: relative;
  cursor: pointer;
}

.toggle.enabled {
  background: #000;
}

.toggleCircle {
  width: 16px;
  height: 16px;
  background: #fff;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 2px;
  transition: transform 0.3s ease;
}

.toggle.enabled .toggleCircle {
  transform: translateX(20px);
}

.link,
.link2 {
  color: #0070ba;
  font-size: 0.95rem;
  text-decoration: underline;
}

.link2 {
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
}

.editIcon {
  font-size: 0.85rem;
  margin-right: 0.5rem;
}

.form {
  margin-top: 1rem;
}

.inputLabel {
  display: block;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  font-weight: 500;
}

.input {
  width: 100%;
  /* padding: 0.75rem; */
  font-size: 1rem;
  margin-top: 0.5rem;
  color: #000;
}

.certificateField {
  width: 40%;
}

.certificationValidationModal {
  margin-top: 18px;
  padding: 16px;
  border-radius: 8px;
  background: #f5f7fa;
  color: black;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.certificationValidationText {
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: -0.08px;
}

.certificationValidationLink {
  cursor: pointer;
  color: #375efb;
}
