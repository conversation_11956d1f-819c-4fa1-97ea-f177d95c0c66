.container {
  max-width: 1230px;
  margin: 0 auto;
  padding: 0px 15px 30px;
  color: #333;
}

.backButton {
  background: none;
  border: none;
  color: black;
  font-weight: 500;
  font-size: 18px;
  cursor: pointer;
  margin-bottom: 30px;
}

.backButton:hover {
  text-decoration: underline;
}

.title {
  font-size: 45px;
  font-weight: 700;
}

.subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 40px;
}

.plansContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 900px;
  margin: 0 auto;
}

.planCard {
  background-color: #ffffff;
  border-radius: 16px;
  padding: 20px;
  transition: transform 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.planMeta{
  display: flex;
  gap: 20px;
  align-items: center;
}

.planCard:hover {

}

.planTitle {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  color: #0E121B;
  margin: 0;
}

.planDetails {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.planPrice {
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  color: #0E121B;
  margin: 0;
}

.upgradeButton {
  background-color: #0070ba;
  color: #fff;
  border: none;
  width: 100%;
  border-radius: 8px;
  height: 50px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.upgradeButton:hover {
  background-color: #0056b3;
}

.disabledButton {
  background-color: #f0f0f0;
  color: #999;
  border: none;
  border-radius: 8px;
  height: 50px;
  width: 100%;
  padding: 10px 20px;
  font-size: 14px;
  cursor: not-allowed;
}

.payment {
  padding: 0px 20%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.packsList{
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 0;
  padding: 0;
  margin-top: 15px;
}

.packsList li{
  display: flex;
  align-items: center;
  gap: 8px;
}

@media screen and (max-width: 600px)
{
  .planCard{
    padding: 20px 15px;
  }

  .planPrice{
    font-size: 20px;
  }
}