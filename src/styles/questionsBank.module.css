.questionsWrapper,
.examsWrapper {
  padding: 0 0 60px 30px;
  min-height: 65vh;
  /* height: 100vh; */
}

.questionsWrapper{
  max-width: 888px;
  margin: 0 auto;
  padding: 0;
  font-family: inherit;
}

.dashboardWrapper,
.usersWrapper {
  max-width: 1030px;
  margin: 0 auto;
  padding: 0px 15px 50px;
  font-family: inherit;
}

.usersWrapper {
  max-width: 1030px;
  padding: 0px 15px 0px;
}

.examsWrapper {
  padding-bottom: 200px;
}

.questionTitle {
  margin: 0;
  font-size: 42px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  color: #0e121b;
  text-align: center;
}

.addNewQuesBox {
  margin-top: 32px;
  display: flex;
  justify-content: space-between;
}

.totalQues {
  color: #525866;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-top: 17px;
  text-align: center;
}

.totalQues span{
  color: #0E121B;
}

.quesSearchBox {
  margin-top: 35px;
  display: flex;
  gap: 9px;
}


.questionsList {
  margin-top: 60px;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.question {
  color: #3f3f3f;
  gap: 3px;
  margin-bottom: 10px;
  font-weight: 500;
  display: flex;
}

.quesNumber {
  display: inline-flex;
}

.quesOptions {
  display: flex;
  flex-direction: column;
  gap: 22px;
  padding-left: 8px;
  padding-top: 8px;
  padding-bottom: 20px;
}

.quesOption {
  color: #0E121B;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px; /* 142.857% */
  letter-spacing: -0.08px;
}

.quesAnswerBox {
  color: #3f3f3f;
  margin-top: 18px;
  display: flex;
  justify-content: space-between;
  gap: 20px;
}


.quesAnswerActions {
  display: flex;
  gap: 15px;
}

.quesAnswerActions a {
  color: #3f3f3f;
}

.quesPagination {
  margin-top: 40px;
}

.filtersBox {
  display: flex;
  gap: 15px;
}

.chartContainer {
  margin-top: 50px;
}

.tableTwoCols{
  display: flex;
  gap: 40px;
}

.tableHolder {
  margin-top: 38px;
}

.payoutRequests{
  width:100%;
  min-width: 570px;
}

.tableTitle {
  font-size: 24px;
  font-weight: 500;
  line-height: 29.05px;
  color: #3f3f00;
}

.tutorList{
  border-radius: 12px;
  background: #FFF;
  padding: 16px;
  margin-top: 18px;
  width:100%;
  max-height: 530px;
  overflow: auto;
}

.tutorItem{
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  padding: 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.tutorItem:first-child{
  padding-top: 0;
  border: 0;
}

.tutorImage img{
  width: 35px;
  height: 35px;
  border-radius: 100%;
  object-fit: cover;
  display: block;
}

.tutorInfo{
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.tutorName{
  color: #000;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: -0.08px;
}

.tutorStatus{
  color: rgba(0, 0, 0, 0.32);
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: -0.08px;
}

.tutorView{
  color: #335CFF;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  letter-spacing: -0.08px;
  margin-left: auto;
  cursor: pointer;
}

.selectedBox {
  display: flex;
  align-items: center;
  gap: 25px;
  margin-top: 30px;
  color: #3f3f3f;
}


.sendMailBox {
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #3f3f3f;
}

.searchLoader {
  margin: 10px 15px;
  font-size: 14px;
}

.filterTags {
  margin-top: 12px;
  display: flex;
  gap: 12px;
  align-items: center;
}

.filterTag {
  border-radius: 8px;
  height: 36px;
  border: 1px solid #ddd;
  font-size: 14px;
  color: #3f3f3f;
  font-weight: 500;
  padding: 8px 12px;
  display: flex;
  flex-direction: row-reverse;
}

.filterTag span {
  padding-left: 9px;
  padding-right: 0;
}

.filterTag div {
  height: inherit;
  margin: 0 !important;
}

.filterTag svg {
  cursor: pointer;
}

.questionId {
  color: #3f3f3f;
  font-weight: 500;
  font-size: 14px;
}

.InfoRow {
  margin-top: 36px;
  display: flex;
  align-items: center;
  gap: 17px;
}

.infoBox {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 18px;
  gap: 10px;

  width: 100%;
  height: 96px;

  background: #ffffff;
  border: 1px solid #eee;
  box-shadow: 0px 8px 18px rgba(123, 135, 156, 0.08);
  border-radius: 12px;
}

.infoBoxInner {
  display: flex;
  align-items: center;
  gap: 12px;
}

.infoImage {
  width: 60px;
  height: 60px;
  background: #e6eefc;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
}

.infoText {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.infoText span {
  font-size: 24px;
  font-weight: 700;
  line-height: 29.05px;
  margin: 0;
  display: block;
}

.infoText div {
  color: #000;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

@media screen and (max-width: 1259px) {
  .quesSearchBox {
    flex-direction: column;
  }

  .quesSearchBox{
    flex-direction: row;
  }

  .searchFilter {
    max-width: 100% !important;
  }
}

@media screen and (max-width: 959px) {
  .addNewQuesBox {
    margin-top: 25px;
  }
  .totalQues {
    margin-top: 25px;
  }
  .quesSearchBox {
    margin-top: 35px;
  }
  .questionsList {
    margin-top: 45px;
  }

  .payoutRequests{
    min-width: 450px;
  }
}

@media screen and (max-width: 859px) {
  .payoutRequests{
    min-width: 400px;
  }
}

@media screen and (max-width: 759px) {
  .InfoRow {
    flex-direction: column;
  }

  .infoBox {
    width: 100%;
  }

  .tableTwoCols{
    flex-direction: column;
    gap: 0px;
  }
}

@media screen and (max-width: 599px) {
  .quesSearchBox {
    flex-direction: column;
  }

  .userSearchBox{
    flex-direction: row;
  }

  .payoutRequests{
    min-width: inherit;
  }

  .payoutRequests th span, .payoutRequests div table thead tr th{
    font-size: 12px !important;
  }

}

@media screen and (max-width: 499px) {
  .questionTitle{
    font-size: 32px;
  }
}