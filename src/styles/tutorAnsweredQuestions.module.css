.addQuestionBox {
  height: 80vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.addQuestionModal {
  margin-top: 50px;
  display: flex;
  width: 50%;
  flex-direction: column;
  gap: 20px;
}

.addQuestionBtn {
  width: 80vw;
  display: flex;
  justify-content: flex-end;
}

.searchLoader {
  margin: 10px 0 0 15px;
  font-size: 14px;
}

.maxQuestions {
  width: 70%;
}

.counter {
  display: flex;
  justify-content: flex-end;
  position: relative;
  bottom: 10px;
  font-size: 14px;
}

@media screen and (max-width: 1100px) {
  .addQuestionModal{
    width: auto;
  }
}

