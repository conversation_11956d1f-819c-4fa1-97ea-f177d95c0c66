.loginPage,.signupPage {
  min-height: 100vh;
  background: #fff;
  padding-bottom: 20px;
}

.loginPage{
  padding-bottom: 0;
  background: url('/images/login-footer-bg.svg') no-repeat bottom center;
  background-size: 100%;
}

.loginTopImage,.loginTopMobile{
  position: absolute;
  margin: 0 auto;
  display: block;
  left: 0;
  right: 0;
  top: 100px;
  z-index: -1;
  max-width: 100%;
}

.loginTopMobile{
  display: none;
}

.signupSection {
  margin-top: 12px;
  border-radius: 16px;
  border: 1px solid #E5E5E5;
  background: #f7f7f7;
  display: flex;
  justify-content: space-between;
}

.loginFormSection.studentSignup{
  max-width: 600px;
  width:100%;
  margin: 0 15px;
}

.studentSignup .formSection{
  border:0;
  box-shadow: none;
}

.loginSection{
  height: calc(100vh - 120px);
  min-height: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.studentSignupBox{
  height: auto;
  min-height: calc(100vh - 120px);
  padding-bottom: 15px;
}

.loginFormSection{
  padding: 42px 20px;
  border-radius: 18px;
  border: 1px solid #E5E5E5;
  box-shadow: 0px -4px 0px 0px #E5E5E5 inset;
  width:100%;
  max-width: 426px;
  margin: 0 auto;
  background:#fff;
}

.topAuthBar{
  height: 77px;
  display: flex;
  padding: 10px 0;
  align-items: center;
  justify-content: space-between;
}

.topAuthBar button{
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  padding: 6px 24px 8px;
  height: 47px;
}

.topAuthBarLogo{
  display: block;
}

.logoSection img{
  display: block;
}

.menuToggle{
  cursor: pointer;
  display: none;
  align-items: center;
  position: absolute;
  margin-top: 4px;
}

.menuToggle img{
  display: block;
}

.logoWithLink{
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logoWithLink a{
  color: #fff;
}

.logoutLink {
  color: #FFF;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: -0.08px;
  cursor: pointer;
}

.formSection,.signupFormSection{
  background: #fff;
  width: 100%;
  max-width: 598px;
  /*min-height: calc(100vh - 60px);*/
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  border-left: 1px solid #E5E5E5;
}

.signupFormSection{
  padding: 40px;
}

.forgotSentHeader{
  background: #E7F9E1;
  border-radius: 12px;
  padding: 6px;
  margin-top: 20px;
  text-align: center;
  font-size: 18px;
  color: #4B4B4B;
  font-weight: 700;
  line-height: 135%;
}

.forgotSentHeader span{
  color: #6BCC09;
}

.formHeader{
  margin-bottom: 20px;
  text-align: center;
}

.formFieldsWrapper{
  width: 100%;
  max-width: 100%;
}

.loginSection .formFieldsWrapper{
  margin-top: 20px;
}

.formLogo{
  width:100%;
  margin-bottom: auto;
  padding-bottom: 20px;
}

.formLogo img{
  display: block;
}

.formTabs{
  display: grid;
  grid-template-columns: 1fr 1fr;
  width:100%;
  margin-bottom: 25px;
  background: #F5F7FA;
  height: 40px;
  border-radius: 10px;
  padding: 4px;
}

.formTabs a{
  color: #99A0AE;
  text-align: center;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: -0.08px;
  padding: 6px 10px;
  display: inline-block;
  text-decoration: none;
  cursor: pointer;
}

.formActiveTab{
  box-shadow: 0px 2px 4px 0px rgba(14, 18, 27, 0.03), 0px 6px 10px 0px rgba(14, 18, 27, 0.06);
  border-radius: 8px;
  background: #fff;
  color: #0E121B !important;
}

.signupRoundBox{
  padding: 25px;
  border-radius: 16px;
  background: #fff;
  width:100%;
  max-width: 600px;
  margin: auto;
}

.box {
  margin: 0 15px;
  text-align: left;
  width: 390px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 30px 32px 32px 32px;
  border: 1px solid #D0D5DD;
  border-radius: 10px;
  box-shadow: 0px 0px 2px 0 #D0D5DD;
}

.resetPassBox {
  margin: 0 15px;
  text-align: left;
  width: 480px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 30px 32px 32px 32px;
  border: 1px solid #D0D5DD;
  border-radius: 10px;
  box-shadow: 0px 0px 2px 0 #D0D5DD;
}

.headImage{
  text-align: center;
}

.headImage img{
  display: block;
  margin: auto;
}

.heading {
  font-weight: 700;
  font-size: 24px;
  line-height: 140%;
  letter-spacing: 0;
  text-align: center;
  color: #4B4B4B;
}

.subtitle,.didNtReceive {
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  color: #777777;
  margin-top: 10px;
  text-align: center;
}

.didNtReceive{
  color: #4B4B4B;
}

.subtitle b{
  font-weight: 600;
  display: block;
}

.notReceived{
  font-size: 14px;
  color: #475467;
  text-align: center;
  margin-top: 10px;
}

.notReceived a{
  color:#000;
  font-weight: 600;
  text-decoration: none;
}

.backToLogin{
  padding: 8px 16px;
  border-radius: 8px;
  background: #FFF;
  font-size: 18px;
  font-weight: 400;
  line-height: 24px;
  color: #0E121B;
  height: 40px;
  text-align: center;
  text-decoration: none;
  box-shadow: 0px 0px 0px 1px #EBEBEB, 0px 1px 3px 0px rgba(143, 143, 143, 0.20), 0px -2.4px 0px 0px rgba(62, 62, 62, 0.04) inset;
}

.backToLogin:hover{
  color: #375dfb;
}

.formTitle{
  font-weight: 700;
  font-size: 24px;
  line-height: 140%;
  letter-spacing: 0;
  text-align: center;
  color: #4B4B4B;
}

.twoFieldsRow{
  display: flex;
  gap: 20px;
}

.field {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;
  margin-bottom: 20px;
}

.inputText{
  height:44px;
  border-radius: 8px;
}

.helper {
  margin-top: 2px;
  color: gray;
  font-size: 12px;
  display: flex;
  gap: 5px;
  font-weight: 400;
  color: #999;
}

.helper img{
  width: 16px;
}

.label {
  font-size: 16px;
  color: rgba(119, 119, 119, 1);
  font-weight: 500;
  line-height: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
}

.inputLabel{
  color: #0E121B;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
  letter-spacing: -0.011px;
  margin-bottom: 12px;
}

.inputFieldWrap{
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.login {
  color: #475467;
  font-size: 16px;
  margin-top: 12px;
  text-align: center;
}

.loginLink {
  text-decoration: none;
  color: #101828;
  cursor: pointer;
  font-weight: 600;
}

.forgotPass {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  font-size: 14px;
  font-weight: 400;
  margin-top: 20px;
  cursor: pointer;
  color: #1C1C1C;
  text-decoration: underline;
}

.formSubmit{
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.formSignUpSubmit button{
  max-width: 328px;
  margin: auto;
  min-width: inherit;
  width: 100%;
}

.formForgotSubmit{
  gap: 10px;
}

/* How It Works */
.hiwSection{
  padding: 40px;
  width:100%;
}

.hiwToggleHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.hiwToggleIcon {
  display: none;
}

.hiwRotateIcon{
  transform: rotate(180deg);
}

.hiwSection h2{
  font-weight: 700;
  font-size: 24px;
  line-height: 140%;
  color: #4B4B4B;
  text-align: center;
  width: 100%;
}

.hiwSection .hiwSteps{
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin-top: 24px;
}

.hiwSteps .hiwStep{
  display: flex;
  gap: 16px;
}

.hiwStep .hiwStepNum{
  min-width: 34px;
  height: 34px;
  background: #6BCC09;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 60px;
  font-size: 16px;
  font-weight: 700;
}

.hiwStep .hiwStepInfo{
  display: flex;
  flex-direction: column;
  gap: 5px;
  max-width: 420px;
}

.hiwStep .hiwStepTitle{
  font-weight: 700;
  font-size: 20px;
  line-height: 140%;
  color: #777777;
}

.hiwStep .hiwStepText{
  font-size: 16px;
  font-weight: 500;
  color: #777777;
  line-height: 160%;
}

@media screen and (max-width: 1229px) {
  .twoFieldsRow{
    flex-direction: column;
    gap:0;
  }
}

@media screen and (max-width: 959px)
{
  .signupPage .signupSection{
    flex-direction: column;
  }

  .signupPage .formSection,
  .signupPage .signupFormSection{
    margin: 0 20px 20px;
    width: 100%;
    max-width: calc(100% - 40px);
    border: 1px solid #E5E5E5;
  }

  .hiwSection{
    padding-top: 25px;
  }

  .hiwStep .hiwStepInfo {
    max-width: 100%;
  }
}

@media screen and (max-width: 700px) {
  .formLogo{
    display: none;
  }
  .formSection,.signupFormSection{
    padding: 30px 20px;
    min-height: auto;
    margin: 16px;
    border-radius: 12px;
  }
}

@media screen and (max-width: 659px)
{
  .topAuthBar button {
    font-size: 12px;
  }

  .loginPage{
    background: url('/images/login-footer-tab-bg.svg') no-repeat bottom center;
    background-size: 100%;
  }

  .loginFormSection{
    padding: 40px 15px;
  }

  .topAuthBarLogo{
    max-width: 85px;
  }

  .topAuthBar{

  }

  .hiwSection{
    padding: 5px 0 15px;
  }

  .hiwToggleHeader{
    height:40px;
    padding: 11px;
    background: #F7F7F7;
    border-radius: 10px;
  }

  .hiwToggleIcon{
    display: flex;
  }

  .hiwToggleIcon img{
    position: relative;
    right:-4px;
  }

  .hiwSection h2{
    font-size: 14px;
  }

  .hiwSection .hiwSteps{
    display: none;
    background: #f7f7f7;
    padding: 20px 12px;
    border-radius: 10px;
    margin-top: 5px;
    gap: 14px;
  }

  .hiwStep .hiwStepNum{
    min-width: 25px;
    height: 25px;
    font-size: 12px;
    font-weight: 500;
  }

  .hiwStep .hiwStepTitle {
    font-size: 14px;
    line-height: 100%;
  }

  .hiwStep .hiwStepText {
    font-size: 12px;
    line-height: 1.3;
    font-weight: 500;
  }

  .hiwSteps .hiwStep {
    gap: 10px;
  }

  .hiwSection .showSteps {
    display: flex;
  }

  .hiwSection .hideSteps {
    display: none;
  }

  .hiwStep .hiwStepInfo {
    gap: 5px;
  }

  .signupPage .signupSection {
    padding: 0;
    border: 0;
    background: none;
  }

  .signupPage .formSection,
  .signupPage .signupFormSection{
    margin: 0;
    max-width: 100%;
  }

  .label{
    font-size: 12px;
  }

  .signupPage .formTitle{
    font-size: 20px;
  }

  .signupPage .formTitle + div{
    font-size: 14px;
    margin-top: 14px;
  }
}

@media screen and (max-width: 500px)
{
  .loginPage{
    background: url('/images/login-footer-mobile-bg.svg') no-repeat bottom center;
    background-size: 100%;
  }

  .loginTopImage{
    display: none;
  }

  .loginTopMobile{
    display: block;
    top: 75px;
    width:100%;
  }
}

@media screen and (max-width: 600px)
{
  .box,.resetPassBox {
    padding: 20px 22px 22px 22px;
    height:auto;
  }

  .login {
    font-size: 14px;
  }

  .logoTopBar{
    padding: 14px 16px;
  }

  .formSection,.signupFormSection{
    padding: 16px;
    min-height: auto;
  }

  .formTabs a{
    font-size: 13px;
  }

  .heading{
    font-size: 24px;
  }

  .subtitle{
    margin-top: 0;
  }

}

@media screen and (max-width: 399px)
{
  .formTitle{
    font-size: 20px;
  }

  .subtitle{
    font-size: 14px;
  }
}

@supports (-webkit-touch-callout: none) and (not (translate: none)) {
  .box,.list {
    display: grid;
  }

  .item {
    display: grid;
     grid-auto-flow: column;
  }
}