.tabsContainer {
  background-color: #fff;
  border-bottom: 2px solid #E5E5E5;
}

.tabsStudent{

}

.quesRemains{
    font-weight: 500;
    font-size: 16px;
    line-height: 160%;
    color: rgba(196, 196, 196, 1);
    display: flex;
    align-items: center;
    gap: 8px;
}

.quesRemains span img{
    position: absolute;
    top: -2px;
    right: -2px;
    cursor: pointer;
}

.quesRemains span{
    color: rgba(69, 176, 246, 1);
    font-weight: 700;
    font-size: 16px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(247, 247, 247, 1);
    border-radius: 100%;
    position: relative;
}

.quesRemains span div{
    cursor: pointer;
}

.tabsHolder{
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.tabsNav {
  display: flex;
  padding: 0;
  margin: 0;
  height: 77px;
  gap: 12px;
}

.tabLink {
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 18px 16px 16px;
    font-size: 16px;
    font-weight: 600;
    color: #C4C4C4;
    text-decoration: none;
    border-bottom: 4px solid transparent;
    transition: color 0.2s ease, border-bottom-color 0.2s ease;
    white-space: nowrap;
    text-transform: uppercase;
    margin-bottom: -1px;
    gap: 9px;
}

.tabLink img{
    display: block;
}

.tabLink span{
    position: relative;
    top: 1px;
}

.tabRedDotBox{
    position: relative;
}

.tabRedDotBox.tabRedDotActive:before{
    content: '';
    width: 6px;
    height: 6px;
    background: rgba(249, 83, 102, 1);
    border-radius: 100%;
    position: absolute;
    z-index: 1;
    right: -3px;
    top: 0px;
}

.tabLink:hover {
  color: #45B0F6;
}

.activeTab {
    color: #6bcc09 !important;
    border-bottom-color: #6bcc09 !important;
}

.tabsTransitioning{

}

.tabsTransitionOverlay{

}

.tabGreenLink img{
    filter: grayscale(100%);
    opacity: 0.8;
    transition: all 0.2s ease;
}

.tabGreenLink:hover{
    color: #6bcc09 !important;
}

.tabGreenLink.adminActiveTab{
    color: #6bcc09;
    border-bottom-color: #6bcc09;
}

.tabGreenLink.adminActiveTab img{
    filter: grayscale(0%)!important;
    opacity: 1!important;
}

.tabGreenLink:hover img{
    filter: grayscale(0%)!important;
    opacity: 1!important;
}

.contentArea {
  padding-top: 60px;
  color: #333;
}

.contentArea .textCenter{
    text-align: center;
}

.contentArea h1 {
    font-weight: 700;
    font-size: 36px;
    line-height: 100%;
    color: #4B4B4B;
    margin-bottom: 16px;
    text-align: center;
}

.contentArea h2 {
    font-weight: 700;
    font-size: 24px;
    margin-top: 25px;
    margin-bottom: 8px;
    color: #4B4B4B;
}

.contentArea .noteText{
    text-align: center;
    padding-top: 5px;
}

.contentArea .noteText,
.contentArea p {
    margin-bottom: 25px;
    color: #A1A1A1;
    font-size: 16px;
    font-weight: 500;
    line-height: 160%;
}

.contentArea .noteText{
    margin-bottom: 40px;
}

.contentWrapper{
    max-width: 900px;
    margin: 0 auto;
}

.faqListing{
    margin: 40px auto 0;
}

.faqItem {
    border: 2px solid #E5E5E5;
    border-top: none;
    background: #fff;
    position: relative;
}

.faqItem:first-child {
    border-top: 2px solid #E5E5E5;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
}

.faqItem:last-child {
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
}

.faqQuestion {
    font-size: 18px;
    color: #4B4B4B;
    padding: 25px 55px 25px 24px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faqQuesBold{
    font-weight: 700;
}

.faqChevron{
    position: absolute;
    right: 24px;
}

.faqAnswer {
    font-size: 16px;
    color: #777777;
    font-weight: 500;
    padding: 0 24px 17px 24px;
    line-height: 160%;
    animation: fadeIn 0.1s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-4px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.faqChevron {
    transition: transform 0.1s ease;
}

.faqChevronOpen {
    transform: rotate(180deg);
}

@media screen and (max-width: 959px)
{
    .tabsContainer{
        display: none;
    }
}

@media screen and (max-width: 659px)
{
    .contentArea{
        padding-top: 30px;
    }

    .contentArea h1{
        font-weight: 700;
        font-size: 20px;
        line-height: 140%;
        margin-bottom: 2px;
        text-align: left;
    }

    .contentArea h2 {
        font-size: 18px;
        line-height: 140%;
    }

    .contentArea .noteText,
    .contentArea p {
        font-size: 14px;
        font-weight: 500;
        line-height: 140%;
        margin-bottom: 20px;
    }

    .contentArea .noteText{
        text-align: left;
    }

    .faqListing{
        margin-top: 32px;
    }

    .faqQuestion {
        font-size: 14px;
        padding: 21px 45px 21px 16px;
    }

    .faqItem {
        border: 1px solid #E5E5E5;
        border-top: none;
    }

    .faqItem:first-child {
        border-top: 1px solid #E5E5E5;
    }

    .faqChevron{
        width: 20px;
        right: 16px;
    }

    .faqAnswer{
        font-size: 12px;
        padding: 0 16px 21px 16px;
        line-height: 140%;
        margin-top: -10px;
    }
}