.logoSection {
  padding: 30px 0;
}

.createSection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100% - 88px);
  min-height: 520px;
  margin: 0 15px;
}

.contentSection {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50%;
}

.boxWithoutBorder {
  height: 480px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 450px;
  text-align: left;
}

.boxWithoutBorder h2 {
  font-size: 32px;
  margin: 0 0 15px;
}

.list {
  margin-top: 20px;
  display: flex;
  gap: 36px;
  flex-direction: column;
}

.item {
  display: flex;
  gap: 10px;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
}

.item img {
  display: block;
}

.signupSection {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50%;
}

.box {
  margin-top: 20px;
  text-align: left;
  width: 480px;
  min-height: 480px;
  max-height: 580px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 30px 32px 32px 32px;
  border-radius: 16px;
  overflow-y: scroll;
  border: 1px solid #d0d5dd;
  box-shadow: 1px 1px 30px #d0d5dd;
}

.field {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 7px;
}

.inputText {
  height: 44px;
  border-radius: 8px;
}

.helper {
  margin-top: 15px;
  color: gray;
  font-size: 14px;
  display: flex;
  gap: 8px;
}

.label {
  color: #344054;
  font-size: 14px;
}

.signupType {
  cursor: pointer;
  text-align: center;
  color: #1f3bad;
  text-decoration: underline;
  font-weight: 400;
  margin-bottom: 20px;
}

.login {
  font-weight: 700;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0;
  text-align: center;
  color: #45B0F6;
  margin-top: 20px;
}

.mustBeText {
  background: #F6FCFF;
  padding: 2px 16px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 16px;
  line-height: 160%;
  text-align: center;
  color: #777;
  width: fit-content;
  margin: 18px auto 20px;
}

.loginLink {
  text-decoration: underline !important;
  color: #45B0F6;
  cursor: pointer;
  font-weight: 700;
}

@media screen and (max-width: 899px) {
  .createSection {
    flex-direction: column;
    height: auto;
    margin-top: 20px;
  }

  .contentSection {
    width: 100%;
    justify-content: start;
  }

  .signupSection {
    width: 100%;
  }

  .boxWithoutBorder {
    height: auto;
    margin-bottom: 20px;
  }

  .box {
    width: auto;
    height: auto;
    margin-bottom: 30px;
  }
}

@media screen and (max-width: 600px) {
  .box {
    padding: 20px 22px 22px 22px;
    height: auto;
  }

  .boxWithoutBorder h2 {
    margin: 0 0 5px;
  }

  .login{
    color: #4B4B4B;
  }

  .login, .login span {
    font-size: 14px;
    font-weight: 500;
    color: #45B0F6;
  }

  .list {
    gap: 20px;
  }

  .item {
    font-size: 16px;
  }
}

@supports (-webkit-touch-callout: none) and (not (translate: none)) {
  .box,
  .list {
    display: grid;
  }

  .item {
    display: grid;
    grid-auto-flow: column;
  }
}
