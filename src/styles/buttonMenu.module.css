.menuContainer {
    position: relative;
    z-index: 1;
}

.menuHeader,
.menuBorderHeader{
    color: #335CFF;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: -0.08px;
}

.menuBorderHeader{
    background: none;
    border-radius: 4px;
    border: 1px solid #DFDEDB;
    color: #003B51;
    justify-content: start;
    height: 26px;
    padding: 6px 10px 8px;
}

.menuBorderHeader .arrow{
    margin-left: auto;
    position: relative;
    top: 1px;
}

.menuBorderHeader .arrow svg{
    width: 8px;
    height: 6px;
}

.arrow {
    display: flex;
}

.arrow.open {
    transform: rotate(180deg);
}

.subMenuBox {
    background: #FFF;
    box-shadow: 0px 16px 32px -2px rgba(14, 18, 27, 0.10);
    width: 210px;
    position: absolute;
    top:100%;
    right:0;
    padding: 10px;
    margin-top: 2px;
    display: flex;
    flex-direction: column;
    gap: 2px;
    border-radius: 8px;
}

.childItem {
    user-select: none;
    display: flex;
    padding: 5px 5px;
    align-items: center;
    gap: 6px;
    border-radius: 6px;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 12px;
    color: #444;
    height: 36px;
    cursor: pointer;
}

.childItem:hover {
    background-color: #f6f6f8;
}

.childItemDivider{
    width: 100%;
    height: 1px;
    background: #e0dedc;
    margin: 5px 0;
}

.icon {
}
