@font-face {
  font-family: 'Gotham Rounded';
  src: url('../../public/fonts/GothamRnd-MediumItalic.woff2') format('woff2'),
  url('../../public/fonts/GothamRnd-MediumItalic.woff') format('woff');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Rounded';
  src: url('../../public/fonts/GothamRnd-LightItalic.woff2') format('woff2'),
  url('../../public/fonts/GothamRnd-LightItalic.woff') format('woff');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Rounded';
  src: url('../../public/fonts/GothamRnd-Light.woff2') format('woff2'),
  url('../../public/fonts/GothamRnd-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Rounded';
  src: url('../../public/fonts/GothamRnd-BoldItalic.woff2') format('woff2'),
  url('../../public/fonts/GothamRnd-BoldItalic.woff') format('woff');
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Rounded';
  src: url('../../public/fonts/GothamRnd-Medium.woff2') format('woff2'),
  url('../../public/fonts/GothamRnd-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Rounded';
  src: url('../../public/fonts/GothamRnd-Bold.woff2') format('woff2'),
  url('../../public/fonts/GothamRnd-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Rounded';
  src: url('../../public/fonts/GothamRnd-BookItalic.woff2') format('woff2'),
  url('../../public/fonts/GothamRnd-BookItalic.woff') format('woff');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Rounded';
  src: url('../../public/fonts/GothamRnd-Book.woff2') format('woff2'),
  url('../../public/fonts/GothamRnd-Book.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

*{
  box-sizing: border-box !important;
}

html,
body, body * {
  padding: 0;
  margin: 0;
  font-family: 'Gotham Rounded';
  font-size: 16px;
}

html {
  scroll-behavior: smooth;
}

.mt5 {
  margin-top: 5px !important;
}

.mt10 {
  margin-top: 10px !important;
}

.mt20 {
  margin-top: 20px !important;
}

.mt25 {
  margin-top: 25px;
}

.pb0 {
  padding-bottom: 0 !important;
}

body .navbarOption div{
  color: #C4C4C4 !important;
}

body .navbarOption div img{
  filter: grayscale(100%)!important;
  opacity: 0.8!important;
}

body[data-pathname="/"] .navbarOption div[data-pathname="/"],
body[data-pathname="/ask"] .navbarOption div[data-pathname="/ask"],
body[data-pathname="/questions"] .navbarOption div[data-pathname="/questions"],
body[data-pathname="/my-questions"] .navbarOption div[data-pathname="/my-questions"],
body[data-pathname="/packs"] .navbarOption div[data-pathname="/packs"],
body[data-pathname="/payment"] .navbarOption div[data-pathname="/packs"],
body[data-pathname="/settings"] .navbarOption div[data-pathname="/settings"],
body[data-pathname="/history"] .navbarOption div[data-pathname="/history"],
body[data-pathname="/earnings"] .navbarOption div[data-pathname="/earnings"],
body[data-pathname="/tutor-settings"] .navbarOption div[data-pathname="/tutor-settings"],
body[data-pathname="/registration"] .navbarOption div[data-pathname="/registration"]{
  color: #6bcc09!important;
  border-bottom-color: #6bcc09!important;
}

body[data-pathname="/"] .navbarOption div[data-pathname="/"] img,
body[data-pathname="/ask"] .navbarOption div[data-pathname="/ask"] img,
body[data-pathname="/questions"] .navbarOption div[data-pathname="/questions"] img,
body[data-pathname="/my-questions"] .navbarOption div[data-pathname="/my-questions"] img,
body[data-pathname="/packs"] .navbarOption div[data-pathname="/packs"] img,
body[data-pathname="/payment"] .navbarOption div[data-pathname="/packs"] img,
body[data-pathname="/settings"] .navbarOption div[data-pathname="/settings"] img,
body[data-pathname="/history"] .navbarOption div[data-pathname="/history"] img,
body[data-pathname="/earnings"] .navbarOption div[data-pathname="/earnings"] img,
body[data-pathname="/tutor-settings"] .navbarOption div[data-pathname="/tutor-settings"] img,
body[data-pathname="/registration"] .navbarOption div[data-pathname="/registration"] img{
  filter: grayscale(0%)!important;
  opacity: 1!important;
}

body[data-pathname="/terms"] .navbarOption div[data-pathname="/terms"],
body[data-pathname="/privacy"] .navbarOption div[data-pathname="/privacy"],
body[data-pathname="/help"] .navbarOption div[data-pathname="/help"]{
  color: #45B0F6!important;
  border-bottom-color: #45B0F6!important;
}

.customHelperText {
  font-size: 14px;
  color: rgba(196, 196, 196, 1) !important;
  margin-top: 3px;
  margin-left: 1px;
}

button {
  text-decoration: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

.containerWidth {
  max-width: 1380px;
  overflow: hidden;
  margin: 0 auto;
}

.containerWidth2 {
  max-width: 1380px;
  overflow-y: scroll;
}

a,
a.tdNone {
  text-decoration: none;
}

.blueColor {
  color: #0052cc;
}

.blueColorRow td,
.blueColorRow .actionLink {
  color: #335cff !important;
}

.overflow-hidden {
  overflow: hidden;
}

.webkitScrollbar::-webkit-scrollbar {
  height: 12px;
  width: 5px;
  background: #ebf1f8;
}
.webkitScrollbar::-webkit-scrollbar-thumb {
  background: #9cb1c3;
}

#mainNavigation.open {
  transform: translateX(0) ;
  opacity: 1;
}

input {
  touch-action: manipulation; /* Prevents zoom on tap */
}

.mainContainer{
  width: 100%;
  max-width: 1230px;
  margin: 0 auto;
  padding: 0 15px;
}

@-moz-document url-prefix() {
  .webkitScrollbar {
    scrollbar-width: thin; /* Only applies in Firefox */
    scrollbar-color: #aaa #f1f1f1; /* Thumb and track color in Firefox */
  }
}

.tableWrapper tr:last-child td .moreOptionsBox .moreOptionMenu {
  top: inherit !important;
  bottom: calc(100% + 8px) !important;
}

.language-select .MuiOutlinedInput-notchedOutline {
  border-width: 1px !important;
  border-color: rgba(0, 0, 0, 0.23) !important;
}

#language-select {
  outline: none !important;
  border: 0;
  box-shadow: none;
  width: 178px;
  padding: 6px;
  font-size: 14px;
  font-family: inherit;
  color: #0e121b;
}

#language-select-mobile fieldset,
#language-select-desktop fieldset {
  border-color: #e1e4ea !important;
}

#language-select-mobile .dropdownIcon,
#language-select-desktop .dropdownIcon {
  margin-right: 10px;
  display: flex;
}

#language-select-mobile span,
#language-select-desktop span {
  position: relative;
  top: 0;
  left: -2px;
}

#language-select-desktop {
  display: none;
  margin-right: 0;
}

#language-select-mobile {
  display: block;
}

#language-select-mobile .MuiSvgIcon-root {
  right: 0;
}

#language-select-mobile #language-select {
  padding: 8px 23px 8px 5px;
}

#language-select-mobile #language-select div {
  font-size: 13px;
  align-items: center;
  display: flex;
  gap: 5px;
}

.verifyMenuLink {
  display: none !important;
}

.myAccountTabs button {
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: start;
  flex-direction: row;
  text-transform: capitalize;
  color: #3f3f3f;
  min-height: 46px !important;
  padding: 6px 16px;
  border-radius: 8px;
  margin-top: 10px;
}

.myAccountTabs button img {
  margin: 0 !important;
}

.myAccountTabs button.Mui-selected {
  background: #3f3f3f;
  color: #fff;
  width: 100%;
  height: 46px;
  box-shadow: none;
  font-size: 16px;
  font-weight: 600;
}

.myAccountTabs button.Mui-selected img {
  -webkit-filter: invert(1);
  filter: invert(1);
}

.myAccountTabs .MuiTabs-indicator {
  display: none !important;
}

.tableContainer {
  overflow-x: auto;
  background-color: white;
  border-radius: 8px;
}

.tableContainer .tableHead th {
  background: rgba(247, 247, 247, 1);
  font-size: 18px;
  color: rgba(75, 75, 75, 1) !important;
  font-weight: 700;
  padding: 5px 16px;
  border-radius: 0px !important;
  border: 0;
  line-height: 140%;
}

.tableContainer .tableHead th span{
  font-size: 18px;
}

.tableContainer .tableHead tr th:first-child{
  border-left: 0!important;
}

.tableContainer .tableHead th svg {
  opacity: 1;
}

.tableContainer table tr:nth-child(even) {
  background-color: rgba(247, 247, 247, 1);
}

.tableContainer tbody tr td {
  color: rgba(119, 119, 119, 1);
  padding: 11px 16px;
  font-size: 16px;
  font-weight: 500;
  line-height: 160%;
  border-top: 1px solid rgba(229, 229, 229, 1);
  border-left: 0;
  border-right: 0;
  border-bottom: 0;
}

.adminUsersTable .tableHead th span,
.adminUsersTable .tableHead th,
.adminUsersTable tbody tr td{
  font-size: 14px;
}

tbody {
  background-color: white !important;
}

.tableContainer .tableHead tr th:first-child,
.tableContainer tbody tr td:first-child {
  /*border-left: 1px solid rgba(229, 229, 229, 1) !important;*/
}

.tableContainer tbody tr td:last-child {
  /*border-right: 1px solid rgba(229, 229, 229, 1);*/
  color: rgba(107, 204, 9, 1);
}

.tableContainer .actionLink {
  color: #335cff !important;
  font-size: 14px;
  text-decoration: underline;
  cursor: pointer;
}

.tableContainer .actionLink:hover {
  text-decoration: none;
}

.tableContainer a.moreInfo {
  width: 32px;
  height: 32px;
  padding: 5px;
  display: flex;
  border-radius: 4px;
}

.tableContainer a.moreInfo img {
  max-width: 100%;
}

.tableContainer a.moreInfo:hover {
  background: #ddd;
}

.tableContainer .tableColCheckbox {
  display: flex;
  gap: 10px;
  align-items: center;
  cursor: pointer;
  max-width: 100px;
  word-break: break-all;
}

.tableContainer .tableColCheckbox .checkbox {
  padding: 0;
}

.tableContainer .tableNoDataFound td {
  font-size: 14px;
  font-weight: 400;
  line-height: 16.94px;
  color: #5a5f68;
  text-align: center;
}

.menuBars svg {
  width: 1em;
  height: 1em;
  font-size: 1.5rem;
}

.placeholderItem .MuiSelect-select {
  color: #667085;
}

.tablePagination {
  border-top: 1px solid #f5f7fa;
}

.tablePagination .MuiToolbar-regular {
  padding-left: 15px;
}

/* Questions Accordion */
.quesAccordion {
  border: 1px solid #e3e8ef;
  border-radius: 12px !important;
  box-shadow: none;
  padding: 25px;
}

.quesAccordion::before {
  display: none !important;
}

.quesAccordion .MuiAccordionSummary-root {
  padding: 0;
}

.quesAccordion .MuiAccordionSummary-content {
  margin: 0;
}

.quesAccordion .MuiAccordionDetails-root {
  padding: 5px 0 0 20px;
}

/* Date Range Picker */
.range-datePicker .react-datepicker {
  width: 360px;
  border-radius: 16px;
  border: 1px solid rgba(28, 28, 28, 0.10);
  background: linear-gradient(0deg, #FFF 0%, #FFF 100%), #FFF;
  box-shadow: 0px 16px 32px -12px rgba(14, 18, 27, 0.10);
  font-family: inherit;
}

.react-datepicker__day--selected{
  background: #375dfb !important;
  color: #fff !important;
}

.range-datePicker .react-datepicker__header {
  border: 0;
  background: none;
}

.range-datePicker .react-datepicker__navigation {
  top: 12px;
}

.range-datePicker .react-datepicker__current-month {
  color: #525866;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: -0.011px;
  position: relative;
  top: 6px;
}

.range-datePicker .react-datepicker__day-names {
  border-top: 1px solid #F2F5F8;
  margin-top: 20px;
}

.range-datePicker .react-datepicker__day-name {
  color: rgba(28, 28, 28, 0.40) !important;
  font-size: 12px !important;
  margin-bottom: 0 !important;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  text-transform: uppercase;
}

.range-datePicker .react-datepicker__triangle {
  display: none;
}

.range-datePicker .react-datepicker__month-container {
  width: 100%;
}

.range-datePicker .react-datepicker__children-container {
  width: 100%;
  padding: 0;
  margin: 0;
  display: flex;
}

.range-datePicker .react-datepicker__month {
  padding: 0;
  margin: 0;
}

.range-datePicker .react-datepicker__day--outside-month.react-datepicker__day-name,
.range-datePicker .react-datepicker__day--outside-month.react-datepicker__day,
.range-datePicker .react-datepicker__day--outside-month.react-datepicker__time-name {
  color: #bbb;
}

.range-datePicker .react-datepicker__day-name,
.range-datePicker .react-datepicker__day,
.range-datePicker .react-datepicker__time-name {
  width: 41px;
  line-height: 37px;
  margin: 3px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  color: #0E121B;
}

.range-datePicker .react-datepicker__day:hover{
  background: #F5F7FA;
}

.range-datePicker .react-datepicker__day--in-range {
  background: #335CFF !important;
  color: #fff;
}

.range-datePicker .react-datepicker__day--keyboard-selected{
  background: #335CFF !important;
  color: #fff !important;
  border:0 !important;
  box-shadow: none !important;
}

.range-datePicker .react-datepicker__day--in-selecting-range{
  background: #F5F7FA !important;
  color: #333 !important;
  border:0 !important;
  box-shadow: none !important;
}

.filterLanguage fieldset {
  border: 0;
}

.tableWrapper .MuiTablePagination-input {
  border: 1px solid #ddd;
  border-radius: 6px;
  margin-right: 15px;
}

.tableWrapper .MuiTablePagination-spacer {
  display: none;
}

.tableWrapper .MuiTablePagination-displayedRows {
  width: auto;
  margin-left: 0;
}

.tableWrapper .MuiTablePagination-actions {
  margin-right: 0;
  margin-left: auto;
}

label.checkboxLabel a {
  color: #777 !important;
  text-decoration: underline;
}

.flag-dropdown svg {
  width: 24px;
  height: 24px;
}

.MuiPaper-root .MuiAccordionSummary-contentGutters {
  margin: 0 !important;
}

.pricingSlider{

}

/* Removes default slick padding */
.tutorCardSlider .slick-list {
  margin-left: -12px;
  margin-right: -12px;
}

/* Adds spacing between cards */
.tutorCardSlider .slick-slide {
  padding: 0 12px;
  box-sizing: border-box;
}

@media screen and (max-width: 1229px) {
  .mainContainer{
    /*max-width: 930px;*/
    max-width: calc(100% - 60px);
  }
}

@media screen and (max-width: 959px)
{
  .mainContainer{
    max-width: calc(100% - 30px);
  }

  .adminContainer .verifyBox {
    display: none;
  }

  .adminContainer .topNavbar {
    position: relative;
    z-index: 1;
    width: 50%;
    margin-left: auto;
  }
}

@media screen and (max-width: 940px) {
  .webCamModal .MuiBox-root {
    width: calc(100vw + 1px);
    height: 100vh;
    border-radius: 0;
    transform: translate(0%, 0%);
    left: 0;
    right: 0;
    margin: 0 auto;
    top: 0;
    overflow: auto;
  }
}

@media screen and (max-width: 899px) {
  .tableWrapper.multiColsTable {
    width: calc(100vw - 48px);
    padding-bottom: 10px;
  }
}

@media screen and (max-width: 849px) {
  .adminUsersTable .table-col-email,
  .adminUsersTable .table-col-answers,
  .adminUsersTable .table-col-dateJoined {
    display: none !important;
  }
}

@media screen and (max-width: 659px)
{
  html, body{
    overflow-x: hidden;
  }

  .inputStartIcon,.inputEndIcon{
    width:20px !important;
  }

  .mainContainer{
    max-width: calc(100% - 10px);
    padding: 0 10px;
  }

  .primaryButton,.borderButton{
    font-size: 14px;
    height: 44px;
    padding: 6px 18px 8px;
    border-radius: 12px;
    font-weight: 500;
  }

  .tutorCardSlider .slick-slide {
    padding: 0 4px;
    box-sizing: border-box;
  }
}

@media screen and (min-width: 600px) {
  #language-select-desktop {
    display: block;
  }

  #language-select-mobile {
    display: none;
  }
}

@media screen and (max-width: 599px) {
  .tableWrapper,
  .tableWrapper.multiColsTable {
    width: calc(100vw - 30px);
    padding-bottom: 10px;
  }

  .customHelperText{
    font-size: 12px !important;
  }

  .tableContainer .tableHead th {
    font-size: 13px;
  }

  .tableContainer tbody tr td {
    font-size: 13px;
    color: #000;
    font-weight: 500;
  }

  .quesAccordion {
    padding: 20px;
  }

  .verifyMenuLink {
    display: flex !important;
  }

  .range-datePicker .react-datepicker {
    padding: 8px;
    border-radius: 10px;
    width: 250px;
  }

  .range-datePicker .react-datepicker__day-name,
  .range-datePicker .react-datepicker__day,
  .range-datePicker .react-datepicker__time-name {
    width: 23px;
    line-height: 23px;
  }

  .range-datePicker button {
    font-size: 13px;
    height: 36px;
    border-radius: 6px;
  }
}

@media screen and (max-width: 599px) {
  .checkboxLabel, .checkboxLabel a {
    font-size: 13px;
  }

  input[type="color"],
  input[type="date"],
  input[type="datetime"],
  input[type="datetime-local"],
  input[type="email"],
  input[type="month"],
  input[type="number"],
  input[type="password"],
  input[type="search"],
  input[type="tel"],
  input[type="text"],
  input[type="time"],
  input[type="url"],
  input[type="week"],
  select:focus,
  textarea {
    font-size: 14px;
  }

  .tableContainer .tableHead th span{
    font-size: 12px;
  }
}

@media screen and (max-width: 499px) {
  .tableContainer .tableHead th {
    font-size: 11px !important;
  }

  .tableContainer .tableHead th,
  .tableContainer tbody tr td{
    padding: 10px 6px !important;
  }
}

@supports (-webkit-touch-callout: none) {
  select,
  textarea,
  input[type="color"],
  input[type="date"],
  input[type="datetime"],
  input[type="datetime-local"],
  input[type="email"],
  input[type="month"],
  input[type="number"],
  input[type="password"],
  input[type="search"],
  input[type="tel"],
  input[type="text"],
  input[type="time"],
  input[type="url"],
  input[type="week"] {
    font-size: 16px !important;
  }
}

/* iPhone 14 and smaller */
@media only screen and (max-width: 430px) and (-webkit-device-pixel-ratio: 3) {
  select,
  textarea,
  input {
    font-size: 16px;
  }
}

/* iPhone 15 Pro Max */
@media only screen and (max-width: 430px) and (-webkit-device-pixel-ratio: 3.5) {
  select,
  textarea,
  input {
    font-size: 16px;
  }
}
