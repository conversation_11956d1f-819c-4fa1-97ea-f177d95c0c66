.userProfileBox,
.payoutBox {
    margin-top: 50px;
    padding: 20px 20px;
    border-radius: 12px;
    background: white;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
}

.userField {

}

.userProfileBox {
    margin-top: 0;
    border-radius: 16px;
    background: #FFF;
    padding: 24px;
    border: 1px solid rgba(229, 229, 229, 1) !important;
    gap: 48px;
    margin-bottom: 50px;
}

.payoutRequestContainer {
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.payoutBox {
    margin: 0px;
}

.userDetails {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.divider {
    width: 100%;
    height: 1px;
    background-color: #f5f5f5;
    margin: 15px 0;
}

.userActionBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 25px;
    width: 100%;
}

.inputUserName,
.inputUserEmail {
    font-size: 32px;
    font-weight: 600;
    border: 0;
    color: #3f3f3f;
    padding: 0;
    background: no-repeat;
    height: 48px;
    outline: none !important;
    max-width: 160px;
    border-bottom: 1px solid #aaa;
}

.inputUserEmail {
    font-size: 17px;
    color: #3f3f3f;
    height: 30px;
    width: 100%;
    margin-top: 10px;
    font-weight: 500;
    max-width: inherit;
}

.adminLabel, .deniedLabel {
    display: inline-block;
    background: #1d78d2;
    color: #fff;
    width: fit-content;
    text-align: center;
    padding: 3px 5px;
    font-size: 12px;
    text-transform: uppercase;
    font-weight: 600;
    line-height: 1.25;
    border-radius: 3px;
    letter-spacing: 1px;
    margin-bottom: 5px;
}

.deniedLabel{
    background: red;
}

.spinnerOverPage{
    background: rgba(255, 255, 255, 0.5);
    position: absolute;
    top:0;
    bottom:0;
    left:0;
    right:0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
}

.userName {
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
    letter-spacing: -0.72px;
    color: #0E121B;
}

.userInfo {
    display: flex;
    width: 90%;
    align-items: center;
    justify-content: space-between;
}

.userInfoField {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.userFieldHeading {
    color: #525866;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0;
}

.userFieldContent {
    color: #0E121B;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: -0.011px;
}

.userName img {
    cursor: pointer;
}

.userEmail {
    font-size: 17px;
    color: #3f3f3f;
    display: flex;
    gap: 12px;
    align-items: center;
}

.allQuesTitle {
    color: #0E121B;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: -0.011px;
    margin-bottom: 7px;
    margin-top: 20px;
}

.approvalBtns {
    display: flex;
    gap: 16px;
}

.userEmail img {
    width: 12px;
    cursor: pointer;
}

.gotoBack {
    font-size: 24px;
    cursor: pointer;
    margin-top: -15px;
    display: block;
}

.noDataFound {
    margin-top: 30px;
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 6px;
    font-size: 15px;
    color: red;
}

.editAccount {
    color: #335CFF;
    cursor: pointer;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: -0.08px;
}

.btnContainer {
    display: flex;
    justify-content: space-between;
}

.leftButtons {
    display: flex;
    align-items: center;
    gap: 12px;
}

.leftButtons button {
    height: 38px;
    padding-bottom: 6px;
}

@media screen and (max-width: 699px) {
    .userProfileBox {
        flex-direction: column;
        justify-content: start;
        align-items: start;
        gap: 20px;
        padding: 20px;
    }

    .userName {
        font-size: 24px;
    }

    .inputUserName,
    .inputUserEmail {
        font-size: 17px;
        width: 100%;
    }

    .userActionBox button {
        font-size: 16px;
        padding: 10px 15px;
    }

    .userInfo {
        display: grid;
        justify-content: space-between;
        gap: 15px;
        margin-top: 10px;
        grid-template-columns: 1.5fr 0.75fr;
        width: 100%;
    }

    .userInfo > :nth-child(odd) {
        justify-self: start;
    }

    .userInfo > :nth-child(even) {
        justify-self: end;
    }

    .userDetails {
        flex-direction: column;
        gap: 10px;
        align-items: start;
    }

    .divider {
        display: none;
    }

}
