.dropdown {
    position: relative;
    min-width: 112px;
}

.dropdownOpen .selected{
    border: 1px solid #6bcc09;
}

.dropdownOpen .arrow{
    transform: rotate(-180deg);
    position: relative;
    top: 2px;
}

.selected {
    display: flex;
    align-items: center;
    border: 1px solid #E5E5E5;
    background: #fff;
    cursor: pointer;
    border-radius: 30px;
    height: 49px;
    line-height: 100%;
    letter-spacing: 0;
    padding: 16px 16px 16px 16px;
}

.selected span{
    color: #aaa;
    font-weight: 500;
    font-size: 14px;
}

.selected > img{
    width: 20px;
    margin-right: 8px;
}

.placeholder {

}

.arrow {
    position: relative;
    top: -2px;
    margin-left: 10px;
}

.menu {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    margin-top: 4px;
    z-index: 10;
    padding: 8px 0;
    min-width: 280px;
    margin-bottom: 5px;
}

.item {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    cursor: pointer;
    transition: background 0.2s;
}

.item span{
    font-size: 14px;
    font-weight: 500;
    color: #4e4e4e;
}

.item:hover {
    background-color: #f5f5f5;
}

.item .icon {
    width: 20px;
    margin-right: 8px;
}

@media screen and (max-width: 399px) {
    .selected{
        height: 32px;
        font-size: 12px;
        padding: 5px 10px;
    }

    .selected span{
        font-size: 12px;
    }

    .arrow {
        position: relative;
        top: -1px;
        margin-left: auto;
        padding-left: 6px;
    }
    .menu{
        min-width: 235px;
    }

    .selected > img{
        width: 14px;
    }
}