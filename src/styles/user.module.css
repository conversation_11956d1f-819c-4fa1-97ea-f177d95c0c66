.borderLine {
  margin: 45px 0;
  border-top: 1px solid #e0e0e0;
}

.centerText {
  text-align: center;
}

.testsBox {
  padding: 20px 30px 30px 0;
}

.testBoxTitle {
  padding: 0 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #3f3f3f;
  margin-bottom: 25px;
}

.personalBox {
  padding-top: 35px;
}

.personalForm {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.personalForm h3{
  font-weight: 700;
  font-size: 24px;
  line-height: 140%;
  color: rgba(75, 75, 75, 1);
  margin: 0;
}

.personalInfo{
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.formSectionTitle {
  font-size: 18px;
  color: #0E121B;
  margin-bottom: 0px;
  display: flex;
  justify-content: space-between;
  font-weight: 500;
}

.borderBox {
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.editAction {
  font-size: 14px;
  color: #0E121B;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: inherit;
}

.twoColsFields{
  display: flex;
  gap: 20px;
}

.creditBox{
  display: flex;
  justify-content: space-between;
}

.creditBox .label{
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.creditBox .label span{
  color: rgba(75, 75, 75, 1);
  font-weight: 700;
  font-size: 24px;
  line-height: 140%;
}

.creditBoxFields{
  display: flex;
  min-width: 390px;
  gap: 16px;
}

.field {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 11px;
}

.label {
  font-size: 16px;
  color: rgba(119, 119, 119, 1);
  font-weight: 700;
  line-height: 100%;
  display: flex;
  gap: 8px;
}

.changePasswordBox {
  border-top: 1px solid #eee;
  margin-top: -10px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding-top: 20px;
}

.updateSubmitBox{
  padding-top: 26px;
}

.registerFormBox{

}

@media screen and (max-width: 959px) {
  .testsBox {
    padding: 0;
  }

  .personalForm {
    max-width: 100%;
  }
}

@media screen and (max-width: 659px) {
  .personalForm{
    gap: 20px;
  }

  .updateSubmitBox{
    padding-top: 0;
  }
}

@media screen and (max-width: 600px) {
  .twoColsFields{
    flex-direction: column;
    gap: 10px;
  }

  .twoColsFields > div{
    margin-bottom: 8px;
  }

  .updateSubmitBox{
    padding-top: 0;
  }

  .updateSubmitBox button{
    width: 100%;
    min-width: 100%;
  }
}


@supports (-webkit-touch-callout: none) and (not (translate: none)) {
  .personalForm,
  .borderBox,
  .changePasswordBox {
    display: grid;
  }

  .tabText {
    margin-left: 10px;
  }
  .item {
    display: grid;
    grid-auto-flow: column;
  }
}
