.footer {
    margin-top: 185px;
    position: relative;
    background: url('/images/footer-bg.svg') top center;
    color: white;
    text-align: center;
    padding: 110px 15px 33px;
    overflow: hidden;
    background-size: cover;
}

.footerCtaContainer {
    position: relative;
    z-index: 2;
    margin-bottom: 2rem;
}

.footerCtaContainer button {
    width: 100%;
    max-width: 278px;
    font-size: 14px;
}

.footerCtaContainer button img {
    margin-right: 10px;
}

.footerHeading {
    font-weight: 700;
    font-size: 58px;
    line-height: 1;
    letter-spacing: 0;
    color: #fff;
}

.footerSubheading {
    font-weight: 500;
    font-size: 20px;
    line-height: 140%;
    letter-spacing: 0;
    color: #fff;
    padding-top: 18px;
    margin-bottom: 26px;
}

.footerIcon {
    font-size: 1.2rem;
}

.footerDivider {
    border: none;
    height: 1px;
    background-color: #D6F2CE;
    margin: 44px 0;
    position: relative;
    z-index: 2;
}

.footerLinksContainer {
    display: flex;
    justify-content: center;
    gap: 61px;
    flex-wrap: wrap;
    position: relative;
    z-index: 2;
}

.footerLink {
    font-weight: 700;
    font-size: 16px;
    color: #FFFFFF;
    cursor: pointer;
    text-transform: uppercase;
    transition: transform 0.2s;
}

.footerLink:hover {
    color: #fff;
}

.footerCopyright {
    font-size: 14px;
    color: #FFFFFF;
    font-weight: 500;
    margin-top: 38px;
}

/* Static Pages Footer */

.legalFooter, .publicFooter {
    background: #45B0F6;
    padding-top: 55px;
    margin-top: 60px;
}

.legalFooter .footerDivider {
    background-color: #D5EEFF;
}

.legalFooter .footerLink {
    color: #D5EEFF;
}

.legalFooter .footerCopyright {
    color: #D5EEFF;
}

/* Public Footer */
.publicFooter {
    background: none;
    margin-top: 20px;
}

.publicFooter .footerDivider {
    background: rgba(229, 229, 229, 1);
}

.publicFooter .footerLink {
    color: rgba(196, 196, 196, 1);
    font-size: 16px;
}

.publicFooter .footerCopyright {
    color: rgba(196, 196, 196, 1);
    font-size: 12px;
}

@media screen and (max-width: 1229px) {
    .footer {
        margin-top: 90px;
    }

    .legalFooter {
        margin-top: 60px;
    }

    .footer.publicFooter {
        margin-top: 20px;
    }
}

@media screen and (max-width: 729px) {
    .footerLinksContainer {
        gap: 45px;
    }
}

@media screen and (max-width: 659px) {
    .footer {
        padding: 80px 0 26px;
        margin-top: 35px;
    }

    .legalFooter {
        padding-top: 30px;
    }

    .footerHeading {
        font-size: 32px;
    }

    .footerSubheading {
        font-size: 16px;
        line-height: 160%;
        font-weight: 500;
    }

    .footerDivider {
        margin: 30px 0;
    }

    .footerLinksContainer {
        gap: 15px;
    }

    .footerLink {
        font-size: 10px;
    }

    .footerCopyright {
        margin-top: 30px;
        font-size: 9px;
    }

    .footer.publicFooter {
        margin-top: 0;
        padding-top: 30px;
    }

    .publicFooter .footerLink {
        font-size: 14px;
    }

    .publicFooter .footerCopyright {
        margin-top: 20px;
    }
}

@media screen and (max-width: 499px) {
    .publicFooter .footerLink {
        font-size: 13px;
    }
}

@media screen and (max-width: 399px) {
    .publicFooter .footerLink {
        font-size: 11px;
        font-weight: 500;
    }

    .publicFooter .footerCopyright {
        font-size: 9px;
    }
}

@media screen and (max-width: 349px) {
    .publicFooter .footerLinksContainer {
        gap: 8px;
    }
}