"use client";
import { useRouter } from "next/navigation";
import { useCallback, useContext } from "react";
import { UserContext } from "@/src/Contexts/UserContext";
import { TAB_ROUTES, SPECIAL_ROUTES } from "@/src/Constants/tabNavigation";

export const useTabNavigation = () => {
    const router = useRouter();
    const { user } = useContext(UserContext);

    return useCallback((tabIndex: number) => {

        const userType = user?.type;
        if (!userType || (userType !== 'user' && userType !== 'tutor')) {
            return;
        }

        const routes = TAB_ROUTES[userType === 'user' ? 'student' : 'tutor'];
        const tab = routes.find(route => route.index === tabIndex);

        if (!tab) {
            if (userType === 'tutor') {
                if (tabIndex === 5) {
                    router.push(SPECIAL_ROUTES.PROFILE);
                    return;
                }
                if (tabIndex === 6) {
                    router.push(SPECIAL_ROUTES.PAYOUT_METHODS);
                    return;
                }
            }

            return;
        }

        router.push(tab.path);
    }, [router, user?.type]);
};