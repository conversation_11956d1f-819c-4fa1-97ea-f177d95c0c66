"use client";
import {FirebaseApp, getApp as firebaseGetApp, getApps, initializeApp} from "firebase/app";
import {getAnalytics, isSupported} from "firebase/analytics";
import {firebaseConfig} from "./firebase.config";
import {
    createUserWithEmailAndPassword,
    EmailAuthProvider,
    getAuth,
    NextOrObserver,
    onAuthStateChanged,
    reauthenticateWithCredential,
    sendPasswordResetEmail,
    signInWithEmailAndPassword,
    signOut,
    updatePassword,
    User,
    UserCredential,
} from "firebase/auth";
import {
    collection,
    deleteDoc,
    doc,
    documentId,
    getDoc,
    getDocs,
    getFirestore,
    limit,
    onSnapshot,
    orderBy,
    query,
    setDoc,
    where,
    writeBatch,
    DocumentReference,
    WhereFilterOp,
    OrderByDirection,
} from "firebase/firestore";
import {translatedVariants} from "../Utils/translationObject";
import {getDownloadURL, getStorage, ref, uploadBytes,} from "firebase/storage";
import {AppNotify} from "../Utils/AppNotify";

let id: string | null = "";
let app: FirebaseApp;
if (typeof window !== "undefined") {
    id = localStorage.getItem("HMWK_LANGUAGE");
}
const langObj = translatedVariants[id === "" || !id ? "en" : (id as keyof typeof translatedVariants)];

if (getApps().length === 0) {
    app = initializeApp(firebaseConfig);
    isSupported().then((yes) => (yes ? getAnalytics(app) : null));
} else {
    app = firebaseGetApp();
}

export {app};

export const imageDb = getStorage(app);
export const auth = getAuth(app);
export const db = getFirestore(app);

export const isUserLoggedIn = (): Promise<string | null> => {
    return new Promise((resolve) => {
        const unsubscribe = onAuthStateChanged(auth, (user) => {
            unsubscribe();
            resolve(user ? user.uid : null);
        });
    });
};

export const createUserDocumentFromAuth = async (
    userAuth: User,
    additionalInformation = {}
) => {
    if (!userAuth) return;

    const userDocRef = doc(db, "users", userAuth.uid);
    const userSnapshot = await getDoc(userDocRef);

    if (!userSnapshot.exists()) {
        const {email} = userAuth;
        const createdAt = new Date();

        try {
            await setDoc(userDocRef, {
                email,
                createdAt,
                ...additionalInformation,
            });

            return {
                status: true,
                id: userDocRef.id,
            };
        } catch (error) {
        }
    }

    return userSnapshot;
};

export const createAuthUserWithEmailAndPassword = async (
  email: string,
  password: string
): Promise<UserCredential> => {  
  if (!email || !password) {
    throw new Error("Email and password are required");
  }
  
  try {
    return await createUserWithEmailAndPassword(auth, email, password);
  } catch (error) {
    console.error("Firebase auth error:", error);
    throw error; 
  }
};

export const checkIfGoogleAuthEmail = async (email: string) => {
    try {
        const q = query(collection(db, "users"), where("email", "==", email));
        const querySnapshot = await getDocs(q);
        if (querySnapshot.docs.length > 0) {
            const docData = querySnapshot.docs[0].data();
            if (docData.provider === "google") {
                return {
                    googleEmail: true,
                };
            } else {
                return {
                    googleEmail: false,
                };
            }
        } else {
            return {
                googleEmail: false,
            };
        }
    } catch (error) {
        return {
            status: false,
        };
    }
};

export const signInAuthUserWithEmailAndPassword = async (
    email: string,
    password: string,
    dontShowError?: boolean,
    isPasswordUpdate?: boolean
) => {
    if (!isPasswordUpdate && (!email || !password)) {
        const validationError = new Error("Email and password are required.");
        (validationError as any).code = 'auth/missing-credentials';
        throw validationError;
    }

    try {
        if (!isPasswordUpdate) {
            const resp = await checkIfGoogleAuthEmail(email);
            if (resp.googleEmail) {
                if (!dontShowError) {
                    AppNotify(langObj.google_email, "error");
                }
                const googleAuthError = new Error(langObj.google_email);
                (googleAuthError as any).code = 'auth/google-account';
                throw googleAuthError;
            }
        }

        return await signInWithEmailAndPassword(auth, email, password);

    } catch (error: any) {
        console.error("Error caught in signInAuthUserWithEmailAndPassword util:", error);
        if (!dontShowError) {
            if (error.code !== 'auth/google-account') {
                AppNotify(langObj.wrong_email_or_password, "error");
            }
        }
        // re-throw the error so the caller / login handles it (fix for tutor disabled)
        throw error;
    }
};

export const sendPasswordResetEmailFunc = async (email: string) => {
    if (!email) return;
    try {
        return await sendPasswordResetEmail(auth, email, {
            url: "https://odevly.com/login",
        });
    } catch (error) {
        AppNotify(langObj.error_sending_pass_reset, "error");
    }
};

export const signOutUser = async () => {
    // Clear the userType cookie
    if (typeof document !== 'undefined') {
        document.cookie = 'userType=; path=/; max-age=0';
    }

    return await signOut(auth);
};

export const onAuthStateChangedListener = (callback: NextOrObserver<User>) => onAuthStateChanged(auth, callback);

export const checkAccountExists = async (matchVal: string) => {
    try {
        const q = query(collection(db, "users"), where("email", "==", matchVal));
        const querySnapshot = await getDocs(q);
        if (querySnapshot.docs.length > 0) {
            return {
                status: true,
                exists: true,
            };
        } else {
            return {
                status: true,
                exists: false,
            };
        }
    } catch (error) {
        return {
            status: false,
        };
    }
};

export const getDocument = async (id: string, collectionName: string) => {
    try {
        const docRef = doc(db, collectionName, id);
        const docSnap = await getDoc(docRef);
        if (docSnap.exists()) {
            return {
                exists: true,
                status: true,
                data: docSnap.data(),
            };
        } else {
            return {
                exists: false,
                status: true,
            };
        }
    } catch (error) {
        return {
            status: false,
        };
    }
};

export const checkOldPassword = async (email: string, password: string) => {
    try {
        const user = await reauthenticateWithCredential(
            auth.currentUser as User,
            EmailAuthProvider.credential(email, password)
        );
        if (user.user) {
            return {
                status: true,
            };
        }
    } catch (error) {
        return {
            status: false,
        };
    }
};

export const addDocInCollection = async (
    collectionName: string,
    data: any,
    dontShowMsg?: boolean,
    examsCompleted?: number,
    examsAttempted?: number,
    createdExamId?: string | null
) => {
    try {
        const batch = writeBatch(db);
        let docRef: DocumentReference;
        
        // Fix 1: Only create ONE document reference based on whether we're creating or updating
        if (createdExamId) {
            // Update existing document
            docRef = doc(db, collectionName, createdExamId);
            batch.set(docRef, data, { merge: true });
        } else {
            // Create new document with auto-generated ID
            docRef = doc(collection(db, collectionName));
            batch.set(docRef, data);
        }

        // Special handling for exams_completed collection
        if (collectionName === "exams_completed" && data.user_id) {
            const userId = data.user_id;
            
            // Fix 2: Add validation for userId
            if (typeof userId !== 'string' || !userId) {
                throw new Error('Invalid user_id provided for exam completion');
            }
            
            batch.set(
                doc(db, "users", userId),
                {
                    attempted_exams: examsAttempted ? examsAttempted + 1 : 1,
                    completed_exams: examsCompleted ? examsCompleted + 1 : 1,
                },
                { merge: true }
            );
        }

        await batch.commit();

        // Fix 3: Show success message if not suppressed
        if (!dontShowMsg) {
            AppNotify(
                createdExamId ? "Updated successfully!" : "Created successfully!", 
                "success"
            );
        }

        return {
            status: true,
            docId: docRef.id, // Now returns the correct ID
            isUpdate: !!createdExamId, // Let caller know if it was an update
        };
        
    } catch (error) {
        // Fix 4: Proper error handling with logging
        console.error("Error in addDocInCollection:", error);
        
        // Provide more specific error messages
        let errorMessage = "An unknown error occurred. Try again later";
        
        if (error instanceof Error) {
            if (error.message.includes('permission')) {
                errorMessage = "You don't have permission to perform this action";
            } else if (error.message.includes('network')) {
                errorMessage = "Network error. Please check your connection";
            } else if (error.message.includes('user_id')) {
                errorMessage = error.message;
            }
        }
        
        if (!dontShowMsg) {
            AppNotify(errorMessage, "error");
        }
        
        return {
            status: false,
            docId: "",
            error: errorMessage,
        };
    }
};

export const updateCollection = async (
    collectionName: string,
    id: string,
    data: any,
    dontShowMessage?: boolean
) => {
    try {
        const batch = writeBatch(db);
        batch.set(
            doc(db, collectionName, id),
            {
                ...data,
            },
            {
                merge: true,
            }
        );

        await batch.commit();

        if (!dontShowMessage) {
            AppNotify("Updated Successfully!", "success");
        }
        return {
            status: true,
        };
    } catch (error) {
        if (!dontShowMessage) {
            AppNotify("An unknown error occurred. Try again later", "error");
        }
        return {
            status: false,
        };
    }
};

export const answerQuestion = async (
    question: any,
    answer: any,
    userId: string,
    img?: string,
    userName?: string
) => {
    try {
        const batch = writeBatch(db);
        const docRef = doc(collection(db, "tutor_answers"));
        const docRef2 = doc(collection(db, "tutor_earnings_log"));
        const docRef3 = doc(db, "users_questions", question.questionId);
        const docRef4 = doc(collection(db, "tutor_exclusive_questions", question.docId));
        const data: any = {
            questionCustomId: question.id,
            questionId: question.questionId,
            question: question.question,
            questionAskedByName: question.name ? question.name : question.email,
            questionAnsweredByName: userName,
            answer,
            answeredBy: userId,
            date: new Date().getTime().toString(),
        };

        if (img) {
            data.imgUrl = img;
        }

        batch.set(docRef, data);
        batch.set(docRef2, {
            userId,
            date: new Date().getTime(),
            type: "Answer",
            amount: 10,
        });
        batch.update(docRef3, {
            exclusive: false,
            isAnswered: true,
            answeredByUsername: userName,
            answeredUserId: userId,
        });
        batch.delete(docRef4);

        await batch.commit();

        return {
            status: true,
        };
    } catch (error) {
        console.log(error);
        AppNotify("An unknown error occurred. Try again later", "error");
        return {
            status: false,
            docId: "",
        };
    }
};

export const listenToDocument = (
    id: string,
    collectionName: string,
    callback?: (data: { exists?: boolean; status?: boolean; data?: any; error?: string }) => void,
    options?: {
        includeMetadataChanges?: boolean;
        maxRetries?: number;
        retryDelay?: number;
    }
) => {
    const { includeMetadataChanges = false, maxRetries = 3, retryDelay = 1000 } = options || {};
    let retryCount = 0;
    let unsubscribe: (() => void) | null = null;
    
    const setupListener = () => {
        try {
            const docRef = doc(db, collectionName, id);
            
            unsubscribe = onSnapshot(
                docRef,
                {
                    includeMetadataChanges
                },
                (docSnap) => {
                    if (typeof callback === "function") {
                        if (docSnap.exists()) {
                            // Maintain backward compatibility - don't include error field on success
                            callback({
                                exists: true,
                                status: true,
                                data: docSnap.data(),
                            });
                        } else {
                            callback({
                                exists: false,
                                status: true,
                            });
                        }
                    }
                    // Reset retry count on successful connection
                    retryCount = 0;
                },
                (error) => {
                    console.error("Error listening to document:", error);
                    
                    // Retry logic for network errors
                    if (retryCount < maxRetries && (
                        error.code === 'unavailable' || 
                        error.code === 'deadline-exceeded' ||
                        error.message.includes('network')
                    )) {
                        retryCount++;
                        console.log(`Retrying listener connection (${retryCount}/${maxRetries})...`);
                        
                        setTimeout(() => {
                            if (unsubscribe) {
                                unsubscribe();
                            }
                            setupListener();
                        }, retryDelay * retryCount);
                        
                        return;
                    }
                    
                    if (typeof callback === "function") {
                        // Include error field only on failure for backward compatibility
                        callback({
                            status: false,
                            error: error.message || 'Connection failed'
                        });
                    }
                }
            );
            
        } catch (error) {
            console.error("Error initializing real-time listener:", error);
            if (typeof callback === "function") {
                callback({
                    status: false,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
    };
    
    setupListener();
    
    // Return cleanup function
    return () => {
        if (unsubscribe) {
            unsubscribe();
            unsubscribe = null;
        }
    };
};

export const getAllDocs = async (
    collectionName: string, 
    options?: {
        limit?: number;
        startAfter?: any;
        orderBy?: { field: string; direction: OrderByDirection };
        where?: { field: string; operator: WhereFilterOp; value: any }[];
    }
) => {
    try {
        // Backward compatibility: if no options provided, use default behavior
        if (!options) {
            const q = query(collection(db, collectionName));
            const querySnapshot = await getDocs(q);
            const data: any = [];
            
            querySnapshot.forEach((doc) => {
                data.push({...doc.data(), docId: doc.id});
            });
            
            return {
                status: true,
                data: data[0],
                fullData: data,
            };
        }

        const { limit: limitCount = 20, startAfter, orderBy: orderByOption, where: whereClauses = [] } = options;
        
        // Build query with proper limits and ordering
        let q = query(collection(db, collectionName));
        
        // Add where clauses
        whereClauses.forEach(({ field, operator, value }) => {
            q = query(q, where(field, operator, value));
        });
        
        // Add ordering
        if (orderByOption) {
            q = query(q, orderBy(orderByOption.field, orderByOption.direction));
        }
        
        // Add pagination
        q = query(q, limit(limitCount));
        if (startAfter) {
            q = query(q, startAfter(startAfter));
        }
        
        const querySnapshot = await getDocs(q);
        const data: any = [];
        
        querySnapshot.forEach((doc) => {
            data.push({...doc.data(), docId: doc.id});
        });
        
        // Return pagination info for next page
        const lastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];
        const hasMore = querySnapshot.docs.length === limitCount;
        
        return {
            status: true,
            data: data[0],
            fullData: data,
            pagination: {
                hasMore,
                lastDoc: hasMore ? lastDoc : null,
                totalInBatch: data.length
            }
        };
    } catch (error) {
        console.error("Error fetching documents:", error);
        return {
            status: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
};

export const uploadImage = async (
    imgFile: any,
    imgId?: any,
    profileImg?: any,
    options?: {
        maxSizeMB?: number;
        quality?: number;
        maxWidth?: number;
        maxHeight?: number;
    }
) => {
    try {
        const { maxSizeMB = 5, quality = 0.8, maxWidth = 1920, maxHeight = 1080 } = options || {};
        
        // Validate file size
        const fileSizeMB = imgFile.size / (1024 * 1024);
        if (fileSizeMB > maxSizeMB) {
            throw new Error(`File size (${fileSizeMB.toFixed(2)}MB) exceeds maximum allowed size (${maxSizeMB}MB)`);
        }
        
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(imgFile.type)) {
            throw new Error(`File type ${imgFile.type} is not supported. Please use JPEG, PNG, or WebP.`);
        }
        
        // Compress image if it's too large
        let processedFile = imgFile;
        if (imgFile.size > 1024 * 1024) { // Compress if larger than 1MB
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                await new Promise((resolve, reject) => {
                    img.onload = resolve;
                    img.onerror = reject;
                    img.src = URL.createObjectURL(imgFile);
                });
                
                // Calculate new dimensions while maintaining aspect ratio
                let { width, height } = img;
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width *= ratio;
                    height *= ratio;
                }
                
                canvas.width = width;
                canvas.height = height;
                
                if (ctx) {
                    ctx.drawImage(img, 0, 0, width, height);
                    canvas.toBlob(
                        (blob) => {
                            if (blob) {
                                processedFile = new File([blob], imgFile.name, {
                                    type: imgFile.type,
                                    lastModified: Date.now()
                                });
                            }
                        },
                        imgFile.type,
                        quality
                    );
                }
                
                URL.revokeObjectURL(img.src);
            } catch (compressionError) {
                console.warn('Image compression failed, using original file:', compressionError);
                // Continue with original file if compression fails
            }
        }
        
        const id = imgId || "id" + Math.random().toString(16).slice(2);
        const imageDbPath = profileImg ? "profileImages" : "images";
        const imgRef: any = ref(imageDb as any, `${imageDbPath}/${id}`);
        
        await uploadBytes(imgRef, processedFile);
        let downloadUrl = "";
        if (!imgId || profileImg) {
            downloadUrl = await getDownloadURL(imgRef);
        }
        
        // Maintain backward compatibility - return same structure as before
        return {
            status: true,
            id,
            downloadUrl,
            // Only include new fields if they differ from original
            ...(processedFile !== imgFile && {
                originalSize: imgFile.size,
                processedSize: processedFile.size,
            })
        };
    } catch (error) {
        console.error("Error uploading image:", error);
        return {
            status: false,
            downloadUrl: '',
            error: error instanceof Error ? error.message : 'Upload failed'
        };
    }
};

export const deleteDocument = async (
    collectionName: string,
    id: string,
    dontShowMessage?: boolean
) => {
    try {
        const docRef = doc(db, collectionName, id);
        await deleteDoc(docRef);
        if (!dontShowMessage) {
            AppNotify("Deleted Successfully", "success");
        }
        return {
            status: true,
        };
    } catch (error) {
        console.error("Error initializing:", error);
        return {
            status: false,
        };
    }
};

export const getUserById = async (userId: string) => {
    const docRef = doc(db, "users", userId);
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
        return null; // No such document
    }

    return {
        id: docSnap.id,
        ...docSnap.data(),
    };
};

export const queryData = async (
    collectionName: string,
    field: string,
    matchVal: any
) => {
    try {
        let q = query(collection(db, collectionName), where(field, "==", matchVal));
        if (collectionName === "exams_questions") {
            q = query(
                collection(db, collectionName),
                where(field, "==", matchVal),
                where("is_archived", "==", false)
            );
        }

        const querySnapshot = await getDocs(q);
        const data: any = [];
        querySnapshot.forEach((doc) => {
            data.push({...doc.data(), docId: doc.id});
        });
        return {
            status: true,
            data: data[0],
            fullData: data,
        };
    } catch (error) {
        console.error("Error initializing:", error);
        return {
            status: false,
        };
    }
};

export const deleteDocumentQuery = async (
    param: string,
    value: string,
    tableName: string
) => {
    try {
        const batch = writeBatch(db);
        const docQuery = query(
            collection(db, tableName),
            where(param, "==", value)
        );
        const docsData = await getDocs(docQuery);
        docsData.forEach((doc) => {
            batch.delete(doc.ref);
        });
        await batch.commit();
        return {
            status: true,
        };
    } catch (error) {
    }
};


const filterQuestions = (docs, userId) => {
    return docs.filter((doc) => !doc.answeredBy?.includes(userId));
};

export const getApplicantQuestionsFunc = async (userId) => {
    try {
        const q = query(collection(db, "applicant_questions"));
        const querySnapshot = await getDocs(q);
        const data: any = [];

        querySnapshot.forEach((doc) => {
            data.push({...doc.data(), docId: doc.id});
        });

        const filteredArr = filterQuestions(data, userId);

        return {
            status: true,
            data: filteredArr.length > 0 ? filteredArr[0] : null,
            fullData: filteredArr,
        };
    } catch (error) {
        console.log(error);
        return null;
    }
};

export const getUserAskedQuestions = async (userId) => {
    try {
        const resp: any = await queryData("users_skipped_questions", "userId", userId);
        let skippedIds: string[] = [];
        if (resp.status && resp.fullData.length > 0) {
            skippedIds = resp.fullData.map((doc) => doc.questionId);
        }

        let allQuestions: any[] = [];

        if (skippedIds.length > 0) {
            const batchSize = 10;
            const chunks = [];

            for (let i = 0; i < skippedIds.length; i += batchSize) {
                // @ts-ignore
                chunks.push(skippedIds.slice(i, i + batchSize));
            }

            for (const chunk of chunks) {
                const q = query(
                    collection(db, "users_questions"),
                    where("exclusive", "==", false),
                    where("isAnswered", "==", false),
                    where(documentId(), "not-in", chunk),
                    orderBy("date", "desc"),
                    limit(1)
                );

                const snapshot = await getDocs(q);
                snapshot.forEach((doc) => {
                    allQuestions.push({...doc.data(), docId: doc.id});
                });

                if (allQuestions.length > 0) break;
            }
        }

        // If no skipped IDs or none of the chunks returned results
        if (allQuestions.length === 0) {
            const q = query(
                collection(db, "users_questions"),
                where("exclusive", "==", false),
                where("isAnswered", "==", false),
                orderBy("date", "desc"),
                limit(1)
            );
            const snapshot = await getDocs(q);
            snapshot.forEach((doc) => {
                allQuestions.push({...doc.data(), docId: doc.id});
            });
        }

        return {
            status: true,
            data: allQuestions[0],
            fullData: allQuestions,
        };
    } catch (error) {
        console.log(error);
        return null;
    }
};

export const updateUserPassword = async (currentPassword: string, newPassword: string) => {
    const auth = getAuth();
    const currentUser = auth.currentUser;

    if (!currentUser || !currentUser.email) {
        console.error("No user is signed in or missing email.");
        AppNotify("You must be signed in to change the password.", "error");
        return;
    }

    try {
        const credential = EmailAuthProvider.credential(currentUser.email, currentPassword);
        await reauthenticateWithCredential(currentUser, credential);

        await updatePassword(currentUser, newPassword);
        AppNotify("Password updated successfully!", "success");
    } catch (error: any) {
        console.error("Error updating password:", error);
        AppNotify(error.message || "Failed to update password.", "error");
    }
};

export const isStudentAuthUser = async (userId: string): Promise<boolean> => {
    try {
        const resp: any = await queryData("transactions", "userId", userId);
        return !!(resp?.status && Array.isArray(resp.fullData) && resp.fullData.length > 0);
    } catch (error) {
        console.error("error checking student paid:", error);
        return false;
    }
};