import type {NextRequest} from 'next/server';
import {NextResponse} from 'next/server';
import {authMiddleware, redirectToHome, redirectToLogin} from 'next-firebase-auth-edge';

const BASIC_AUTH_USER = process.env.BASIC_AUTH_USER || 'admin';
const BASIC_AUTH_PASS = process.env.BASIC_AUTH_PASS || 'odevly';
const MAINTENANCE_MODE = process.env.MAINTENANCE_MODE === 'true';

const studentRoutes = ['/my-questions', '/packs', '/settings', '/transactions'];
const tutorRoutes = ['/history', '/earnings', '/registration', '/profile', '/tutor-settings', '/payout-method'];
const adminRoutes = ['/applicant-questions', '/dashboard', '/edit-user', '/history', '/settings', '/test-questions', '/user-questions', '/users'];
const protectedRoutes = [...studentRoutes, ...tutorRoutes, ...adminRoutes];

// Simple in-memory cache for user types (in production, use Redis or similar)
const userTypeCache = new Map<string, { type: string; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

function getUserTypeFromCache(uid: string): string | null {
    const cached = userTypeCache.get(uid);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        return cached.type;
    }
    if (cached) {
        userTypeCache.delete(uid); // Remove expired entry
    }
    return null;
}

function setUserTypeInCache(uid: string, type: string): void {
    userTypeCache.set(uid, { type, timestamp: Date.now() });
    
    // Clean up old entries to prevent memory leaks
    if (userTypeCache.size > 1000) {
        const now = Date.now();
        Array.from(userTypeCache.entries()).forEach(([key, value]) => {
            if (now - value.timestamp > CACHE_TTL) {
                userTypeCache.delete(key);
            }
        });
    }
}

export async function middleware(request: NextRequest) {
    const {pathname} = request.nextUrl;

    if (MAINTENANCE_MODE) {
        const authHeader = request.headers.get('authorization');

        if (authHeader) {
            const [scheme, encoded] = authHeader.split(' ');

            if (scheme === 'Basic') {
                const decoded = atob(encoded);
                const [user, pass] = decoded.split(':');

                if (user !== BASIC_AUTH_USER || pass !== BASIC_AUTH_PASS) {
                    return getMaintenanceResponse();
                }
            } else {
                return getMaintenanceResponse();
            }
        } else {
            return getMaintenanceResponse();
        }
    }

    // Protected routes
    if (protectedRoutes.includes(pathname)) {
        return authMiddleware(request, {
            loginPath: '/api/login',
            logoutPath: '/api/logout',
            apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
            cookieName: 'AuthToken',
            cookieSignatureKeys: [process.env.COOKIE_SECRET_CURRENT!, process.env.COOKIE_SECRET_PREVIOUS!],
            cookieSerializeOptions: {
                path: '/',
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax' as const,
                maxAge: 14 * 60 * 60 * 24 * 1000, // 14 days
            },
            serviceAccount: {
                projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
                clientEmail: process.env.FB_CLIENT_EMAIL!,
                privateKey: process.env.FB_PRIVATE_KEY?.replace(/\\n/g, '\n')!,
            },
            handleValidToken: async ({token, decodedToken}) => {
                // Custom claims TODO
                let userType: string | null = (decodedToken.userType as string) || (decodedToken.type as string) || null;

                // Check cache first
                if (!userType && decodedToken.uid) {
                    userType = getUserTypeFromCache(decodedToken.uid);
                }

                // Fallback check type in firestore
                if (!userType && decodedToken.uid) {
                    userType = await getUserTypeFromFirestore(decodedToken.uid);
                    if (userType) {
                        setUserTypeInCache(decodedToken.uid, userType);
                    }
                }

                const finalUserType: string = userType || 'user';

                if (adminRoutes.includes(pathname) && finalUserType !== 'admin') {
                    return redirectToHome(request);
                }

                if (tutorRoutes.includes(pathname) && finalUserType === 'user') {
                    return redirectToHome(request);
                }

                if (studentRoutes.includes(pathname) && finalUserType === 'tutor') {
                    return redirectToHome(request);
                }

                const response = NextResponse.next();
                response.cookies.set('userType', finalUserType, {
                    path: '/',
                    maxAge: 60 * 60 * 24 * 7,
                    sameSite: 'lax',
                });

                return response;
            },
            handleInvalidToken: async () => {
                return redirectToLogin(request, {
                    path: '/login',
                    publicPaths: ['/login', '/signup', '/'],
                });
            },
            handleError: async (error) => {
                return redirectToLogin(request, {
                    path: '/login',
                    publicPaths: ['/login', '/signup', '/'],
                });
            },
        });
    }

    return NextResponse.next();
}

async function getUserTypeFromFirestore(uid: string): Promise<string | null> {
    try {
        const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
        const response = await fetch(`${baseUrl}/api/internal/user-type/${uid}`, {
            headers: {
                'x-internal-auth': process.env.INTERNAL_API_SECRET || 'default-secret',
            },
        });

        if (response.ok) {
            const data = await response.json();
            return data.type || 'user';
        }

        return 'user';

    } catch (error) {
        console.error('Error fetching user type:', error);
        return 'user';
    }
}

function getMaintenanceResponse() {
    const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Under Maintenance</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body {
                font-family: Arial, sans-serif;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                margin: 0;
                background-color: #f5f5f5;
            }
            .container {
                text-align: center;
                padding: 2rem;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                max-width: 400px;
            }
            h1 {
                color: #333;
                margin-bottom: 1rem;
            }
            p {
                color: #666;
                margin-bottom: 1.5rem;
            }
            .auth-btn {
                background-color: #4CAF50;
                color: white;
                padding: 12px 24px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 16px;
                text-decoration: none;
                display: inline-block;
            }
            .auth-btn:hover {
                background-color: #45a049;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔧 Under Maintenance</h1>
            <p>This site is currently undergoing maintenance. Please authenticate to continue.</p>
            <a href="#" class="auth-btn" onclick="window.location.reload()">Authenticate</a>
        </div>
    </body>
    </html>
    `;

    return new NextResponse(html, {
        status: 401,
        headers: {
            'Content-Type': 'text/html',
            'WWW-Authenticate': 'Basic realm="Under Maintenance"',
        },
    });
}

export const config = {
    matcher: [
        '/((?!api/public|_next/static|_next/image|favicon.ico|robots.txt).*)',
    ],
};