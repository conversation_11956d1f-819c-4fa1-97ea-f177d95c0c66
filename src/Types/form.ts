import {ChangeEvent} from "react";

export interface CurrencyInputProps {
    label?: string;
    value?: string;
    onChange: (value: string) => void;
    currencySymbol?: string;
    currencyCode?: string;
    defaultValue?: string;
}

export interface CustomCheckboxProps {
    id?: string;
    defaultLabel?: string;
    defaultChecked?: boolean;
    onChange?: (checked: boolean) => void;
}

export interface CustomSelectOption {
    label: string;
    value: any;
}

export interface CustomSelectProps {
    options: CustomSelectOption[];
    onSelect: (value: string) => void;
    icon?: string;
    placeholder?: string;
    defaultValue?: any;
    type?: string;
    width?: string;
    message?: string;
    highlightField?: boolean;
    fullWidth?: boolean;
    disabled?: boolean;
}

export interface InputTextFieldProps {
    label?: string;
    icon?: string;
    value?: string;
    onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
    isLoading?: boolean;
    disabled?: boolean;
    highlightField?: boolean;
    fullWidth?: boolean;

    [key: string]: any;
}

export interface PasswordFieldProps {
    label?: string;
    showIcon?: boolean;
    value?: string;
    onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
    fullWidth?: boolean;
    autoShrink?: boolean;
    checkStrength?: boolean;

    [key: string]: any;
}

export interface QuantityStepperProps {
    value?: number;
    defaultValue?: number;
    suffix?: string;
    min: number;
    max: number;
    onChange: (value: number) => void;
}

export interface TextAreaFieldProps {
    label?: string;
    value?: string;
    placeholder?: string;
    onChange?: (event: ChangeEvent<HTMLTextAreaElement>) => void;
    showBorder?: boolean;
    disabled?: boolean;
    fullWidth?: boolean;
    filledArea?: boolean;
    rows?: number;
    maxRows?: number;
    maxLength?: number;

    [key: string]: any;
}

export interface FileInputFieldProps {
    simpleChange?: boolean;
    onChange: (value: object | null) => void;
    settingsPage?: boolean;
    profilePage?: boolean;
    userImg?: string;
    disabled?: boolean;
}