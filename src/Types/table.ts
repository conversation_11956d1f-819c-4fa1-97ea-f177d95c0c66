// Base column interface
import * as React from "react";

export interface BaseTableColumn<T = string> {
    id: T;
    label: string;
    minWidth?: number;
    maxWidth?: number;
    sorting?: boolean;
    align?: "right" | "center" | "left" | "inherit" | "justify";
    format?: (value: number | string) => string;
}

// Dashboard table column
export type DashTableColumn = BaseTableColumn<"requestDate" | "profile" | "amount">;

// User table column
export type UserTableColumn = BaseTableColumn<"user" | "email" | "dateJoined" | "action" | "type" | "answers">;

// Test Answer table column
export type TestAnswersTableColumn = BaseTableColumn<"date" | "id" | "correct" | "amount" | "view">;

// Main question table column
export type QuestionTableColumn = BaseTableColumn<"date" | "questions">;

// User table column
export type UserProfileTableColumn = BaseTableColumn<"question" | "date" | "id" | "answered" | "view">;

// Tutor Balance table column
export type TutorBalanceTableColumn = BaseTableColumn<"date" | "type" | "earnings">;

// Payment Billing table column
export type PaymentBillingTableColumn = BaseTableColumn<"date" | "pack" | "price">;

// Payment Question table column
export type PaymentQuestionTableColumn = BaseTableColumn<"date" | "status" | "questions">;

// Dashboard data row
export interface DashCreateData {
    requestDate: string | Date;
    amount: string;
    profile: string | object;
}

// Create Data
export interface UserCreateData {
    docId: string;
    user: string;
    email: string;
    answers: unknown;
    dateJoined: string | Date;
    type: string;
    action: string;
    isAdmin: boolean;
    status: string;
}

export interface UserProfileCreateData {
    question?: string;
    date: string;
    id?: string;
    answered?: string;
    view?: string;
}

export interface AnswerCreateData {
    date: string;
    id: string;
    correct: string;
    amount: string;
    view: string;
}

export interface EnhancedTableColumn {
    id: string;
    label: string;
    sorting?: boolean;
    align?: "center" | "left" | "right" | "inherit" | "justify";
}

export interface EnhancedTableHeadProps {
    order?: string;
    orderBy?: string;
    onRequestSort?: (event: React.MouseEvent<unknown>, property: string) => void;
    columns: EnhancedTableColumn[];
    rowCount?: number;
}

export interface TablePaginationControlsProps {
    showFullBar?: boolean;
    filteredRowsLength: number;
    page: number;
    rowsPerPage: number;
    setPage: (page: number) => void;
    setRowsPerPage: (rowsPerPage: number) => void;
}