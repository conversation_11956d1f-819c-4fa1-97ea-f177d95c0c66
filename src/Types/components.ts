import React from "react";

// Base structure 
export interface BaseQuestion {
    docId: string;
    userId: string;
    name: string;
    email: string;
    date: number;
    question: string | string[];
    imageUrl: string;
    isAnswered: boolean;
    answeredByUsername?: string;
    answeredUserId?: string;
}

export interface MainQuestion extends BaseQuestion {
    id: number;
}

export interface HistoryQuestion extends BaseQuestion {
    id: string;
    question: string;
}

export interface TutorQuestion extends BaseQuestion {
    questionCustomId: number;
    answer: any;
    answerDate?: number;
    answerImageUrl?: string;
    answeredBy?: string;
    questionAnsweredByName?: string;
    questionAskedByName?: string;
}

export interface QuestionOriginalItem {
    date: string;
    question?: string;

    [key: string]: any;
}

export interface QuestionGroupedItem {
    date: string;
    questions: number;
}

export interface QuestionRowArrType {
    date: string;
    action: any;
    status: any;
}

export interface SettingsProps {
    isTutorReg?: boolean;
    value?: string;
    onProgressUpdate?: (progress: { value: number; message: string }) => void;
}

export interface HomeSectionProps {
    title?: string;
    text?: string;
}

export interface PricingSectionProps {
    onlyPlans?: boolean;
}

export interface FooterProps {
    id?: any;
    userType?: string;
    isLegalPage?: boolean;
    isPublicPage?: boolean;
    setActiveTab?: (tabName: any) => void;
}

export interface TabActivePrevProps {
    setActiveTab?: (tab: number | ((prev: number) => number)) => void;
}

export interface TabActivePros {
    setActiveTab?: (tabName: any) => void;
}

export interface Transaction {
    docId: string;
    date: any;
    type: string;
    amount: number;
}

export interface ProcessedTransaction {
    docId: string;
    date: number;
    type: string;
    earnings: number;
    isAdmin?: boolean;
}

export interface NotificationProps {
    type?: 'info' | 'warning' | 'error' | 'success';
    title: string;
    message: React.ReactNode;
    iconSrc?: string;
    style?: React.CSSProperties;
}

export interface ProgressBarProps {
    value: number;
    max: number;
    showTooltip?: boolean;
    tooltipText?: string;
}

export interface FiltersProps {
    ref?;
    onApplyFilters?: (e) => void;
    setFiltersApplied?: (val: boolean) => void;
}

export interface SearchBoxProps {
    onInputChange?: (e) => void;
    className?: string;
    placeholder?: string;
    style?: any;
}

export interface SelectDatesProps {
    className?: string;
    onApplyDateRange?: (e: { startDate: Date | null; endDate: Date | null }) => void;
    singleDateMode?: boolean;
}

export interface TextToggleProps {
    text: any;
}

export interface TopBarProps {
    buttonLabel: string;
    buttonUrl: string;
}

export interface NavigationContextType {
    activeTab: number;
    setActiveTab: (tab: number) => void;
    userType: string | null;
    userId: string | null;
    shouldShowNavbar: boolean;
    updateUserInfo: (userId: string | null, userType: string | null) => void;
}

export interface Achievement {
    icon: string;
    title: string;
    description: string;
    value: number;
    max: number;
    id?: string;
    isRegistration?: boolean;
}

export interface RankDefinition {
    icon: string;
    name: string;
    description: string;
    rewards: string;
    bonus?: string;
    minAnswers: number;
    nextRankAnswers?: number;
}

export interface DisplayRank extends RankDefinition {
    locked: boolean;
    active: boolean;
}

export interface Tab {
    name: string;
    icon: string;
    path: string;
    index: number;
}

export interface EditProfileFormProps {
    selectedUser: Record<string, any>;
    selectedUserId: string;
}

