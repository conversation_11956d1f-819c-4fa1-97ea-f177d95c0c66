import React from "react";

export const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    const day = date.getDate();
    const month = date.toLocaleString("en-US", {month: "short"});
    const year = date.getFullYear();
    return `${day} ${month}, ${year}`;
};

export const isArray = (value: any): value is any[] => Array.isArray(value);
export const zeroPadId = (id) => {
    const maxLength = 6;

    if (id >= Math.pow(10, maxLength)) {
        throw new Error(
            `ID ${id} exceeds the maximum allowed length of ${maxLength} digits.`
        );
    }

    return id.toString().padStart(maxLength, "0");
};

export const validateEmail = (email: string) => {
    return String(email)
        .toLowerCase()
        .match(
            /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        );
};

export const descendingComparator = (a, b, orderBy) => {
    if (b[orderBy] < a[orderBy]) {
        return -1;
    }
    if (b[orderBy] > a[orderBy]) {
        return 1;
    }
    return 0;
};

export const getComparator = (order, orderBy) => {
    return order === "desc"
        ? (a, b) => descendingComparator(a, b, orderBy)
        : (a, b) => -descendingComparator(a, b, orderBy);
};

export const stableSort = (array, comparator) => {
    const stabilizedThis = array.map((el, index) => [el, index]);
    stabilizedThis.sort((a, b) => {
        const order = comparator(a[0], b[0]);
        if (order !== 0) return order;
        return a[1] - b[1];
    });
    return stabilizedThis.map((el) => el[0]);
};

export const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 2,
    minimumFractionDigits: 0,
});

export const fraction = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
});

export function formatCurrency(amount) {
    let currencyAmount: string | number = 0;

    if (amount % 1 !== 0) {
        currencyAmount = formatter.format(amount);
    } else {
        currencyAmount = fraction.format(amount);
    }

    return currencyAmount;
}

export const maskAccountNumber = (number) => {
    if (!number) return "";

    const lastThree = number.slice(-3);
    const maskedPart = "*".repeat(number.length - 3);

    return maskedPart + lastThree;
};

export const compareNumbers = (a, b) => {
    return a - b;
};

export const pricingPlans = [
    {
        id: 'pack1',
        questions: 10,
        price: 50,
        color: '#6BCC09',
        popular: true,
        button: 'greenBtn',
        features: ['24/7 Tutor access', '100% Guarantee', 'Credits never expire'],
    },
    {
        id: 'pack2',
        questions: 15,
        price: 75,
        color: '#45B0F6',
        popular: false,
        button: 'blueBtn',
        features: ['24/7 Tutor access', '100% Guarantee', 'Credits never expire'],
    },
    {
        id: 'pack3',
        questions: 20,
        price: 100,
        color: '#FF9600',
        popular: false,
        button: 'orangeBtn',
        features: ['24/7 Tutor access', '100% Guarantee', 'Credits never expire'],
    },
];

export function calculateTutorRegProgress({
                                              firstName,
                                              lastName,
                                              phoneNumber,
                                              dateOfBirth,
                                              educationLevel,
                                              tutorExperience,
                                              country,
                                              certificateNumber,
                                          }: {
    firstName: string;
    lastName: string;
    phoneNumber: string;
    dateOfBirth: string;
    educationLevel: string;
    tutorExperience: string;
    country: string;
    certificateNumber: string;
}): number {
    const requiredFields = [
        {key: 'firstName', value: firstName},
        {key: 'lastName', value: lastName},
        {key: 'phoneNumber', value: phoneNumber, validate: (v: string) => v.trim() && v !== '+',},
        {key: 'dateOfBirth', validate: () => dateOfBirth},
        {key: 'educationLevel', value: educationLevel},
        {key: 'tutorExperience', value: tutorExperience},
        {key: 'country', value: country},
        {key: 'certificateNumber', value: certificateNumber},
    ];

    const completedFields = requiredFields.reduce((count, field: any) => {
        const isValid = field.validate ? field.validate(field.value) : field.value && field.value.trim();
        return isValid ? count + 1 : count;
    }, 0);

    const totalFields = requiredFields.length;
    return (completedFields / totalFields) * 100;
}

export const handleScrollToSection = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();

    const targetId = e.currentTarget.getAttribute('data-href');
    const fallbackHref = e.currentTarget.getAttribute('href');
    if (!targetId) return;

    const element = document.getElementById(targetId);
    const header = document.getElementById('appHeaderBar');

    const headerHeight = header?.getBoundingClientRect().height ?? 0;
    const adjustedHeaderHeight = headerHeight < 100 ? 100 : headerHeight;

    if (element) {
        const elementPosition = element.getBoundingClientRect().top + window.scrollY;
        const offsetPosition = elementPosition - adjustedHeaderHeight;

        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth',
        });
    } else if (fallbackHref) {
        window.location.href = `/${fallbackHref}`;
    }
};