export const defaultFilledTextareaFieldStyle = {
    "& .MuiOutlinedInput-root": {
        borderRadius: "10px",
        fontSize: "14px",
        color: "#333",
        fontWeight: 400,
        lineHeight: "20px",
        borderColor: "transparent !important",
        backgroundColor: "#F1F5F9",
        padding: "10px",
        "& fieldset": {
            top: 0,
            borderWidth: "1px",
            borderColor: "#E1E4EA",
        },
        "& legend": {
            display: "none", 
        },
        "&.Mui-focused fieldset": {
            borderWidth: "1px",
            borderColor: "#E1E4EA", 
        },
    },
    "& .MuiInputBase-input": {
        height: "auto",
        padding: "0px 5px 0 0",
        marginTop: "0",
        lineHeight: "20px",
    },
    "& .Mui-disabled": {
        opacity: 0.7,
    },
    "& .Mui-disabled:hover": {
        cursor: "not-allowed",
    },
};
export const filterSelectStyle = {
  width: "100%",
  maxWidth: {xs: "100%", sm: "100%"},
  height: "48px",
  border: "2px solid #E0E0E0",
  borderRadius: "8px",
  marginBottom: "0px",
  fontsize: "16px",
  color: "#3F3F3F",
  fontFamily: 'inherit',
  paddingRight: '10px',
  "& .MuiOutlinedInput-notchedOutline": {
    border: "0 !important",
  },
  "&.MuiInputBase-root": {
    ' svg': {
      width: '30px'
    }
  },
};
export const defaultSmallButtonStyle = {
  width: "125px",
  height: "40px",
  boxShadow: "none",
  padding: "6px 12px 8px",
  backgroundColor: "#375DFB",
  color: "#fff",
  fontSize: "12px",
  borderRadius: "6px",
  fontFamily: "inherit",
  fontWeight: 400,
  textTransform: "none",
  letterSpacing: "0.48px",
  "&.MuiButtonBase-root:hover": {
    backgroundColor: "#1a42ea",
  },
  "&.Mui-disabled": {
    backgroundColor: "#ccc",
    color: "#F8F7F3",
  },
  "& .MuiButton-startIcon": {
    margin: 0,
  },
};
export const defaultPrimaryButtonStyle = {
  height: "49px",
  boxShadow: "0px -4px 0px 0px #D6F2CE inset",
  padding: "6px 42px 8px",
  backgroundColor: "#fff",
  color: "#6BCC09",
  fontSize: "16px",
  borderRadius: "14px",
  fontFamily: "inherit",
  fontWeight: 700,
  textTransform: "none",
  "&.MuiButtonBase-root:hover": {
    backgroundColor: "#f3f3f3",
  },
  "&.Mui-disabled": {
    backgroundColor: "rgba(229, 229, 229, 1)",
    color: "rgba(196, 196, 196, 1)",
    boxShadow: '0px -4px 0px 0px rgba(196, 196, 196, 1) inset'
  },
  "& .MuiButton-startIcon": {
    margin: 0,
  },
};
export const defaultGreenButtonStyle = {
  ...defaultPrimaryButtonStyle,
  backgroundColor: '#6BCC09',
  color: '#fff',
  boxShadow: '0px -4px 0px 0px #64C205 inset',
  "&.MuiButtonBase-root:hover": {
    backgroundColor: "#64C205",
  },
};
export const defaultOrangeButtonStyle = {
  ...defaultPrimaryButtonStyle,
  backgroundColor: '#FF9600',
  color: '#fff',
  boxShadow: '0px -4px 0px 0px #DF8D17 inset',
  "&.MuiButtonBase-root:hover": {
    backgroundColor: "#EA8E0A",
  },
};
export const defaultBlueButtonStyle = {
  ...defaultPrimaryButtonStyle,
  backgroundColor: '#45B0F6',
  color: '#fff',
  boxShadow: ' 0px -4px 0px 0px #088DCC inset',
  "&.MuiButtonBase-root:hover": {
    backgroundColor: "#3BA4E8",
  },
};
export const defaultWhiteButtonStyle = {
  ...defaultPrimaryButtonStyle,
  backgroundColor: '#fff',
  color: '#45B0F6',
  boxShadow: '0px -4px 0px 0px #D5EEFF inset',
  "&.MuiButtonBase-root:hover": {
    color: "#2689CA",
    backgroundColor: "#fff",
  },
};
export const confirmModalButtonStyle = {
  ...defaultPrimaryButtonStyle,
  height: "43px",
  padding: "6px 16px 8px",
  fontSize: "14px",
  fontWeight: "500",
  width: "150px",
};
export const defaultMediumButtonStyle = {
  ...defaultPrimaryButtonStyle,
  height: "38px",
  padding: "6px 16px 8px",
  fontSize: "14px",
  width: "180px",
};
export const defaultBiggerButtonStyle = {
  ...defaultPrimaryButtonStyle,
  height: "50px",
  padding: "6px 16px 8px",
  fontSize: "16px",
};
export const defaultBorderButtonStyle = {
  height: "49px",
  boxShadow: "none",
  padding: "6px 22px 8px",
  color: "#fff",
  fontSize: "16px",
  borderRadius: "14px",
  border: "2px solid #fff",
  fontFamily: "inherit",
  fontWeight: 700,
  textTransform: "none",
  "&.MuiButtonBase-root:not(.Mui-disabled)": {
    color: "#fff",
    "&:hover": {
      color: "#6bcc09",
      borderColor: '#fff',
      backgroundColor: '#fff',
    },
  },
  "&.Mui-disabled": {
    color: "#335CFF",
    "&:hover": {
      backgroundColor: "#335CFF",
      color: "#fff",
    },
  },
  "& .MuiButton-startIcon": {
    margin: 0,
  },
};
export const defaultCancelButtonStyle = {
  width: "100%",
  height: "36px",
  boxShadow: "none",
  padding: "8px",
  backgroundColor: "#fff",
  color: "#0E121B",
  fontSize: "14px",
  borderRadius: "8px",
  border: "1px solid #E1E4EA",
  fontFamily: "inherit",
  fontWeight: 400,
  textTransform: "none",
  letterSpacing: "-0.08px",
  "&.Mui-disabled": {
    backgroundColor: "#fff",
    color: "#335CFF",
    border: "1px solid #335CFF",
    "&:hover": {
      backgroundColor: "#335CFF",
      color: "#fff",
    },
  },
  "& .MuiButton-startIcon": {
    margin: 0,
  },
};
export const defaultDarkBorderButtonStyle = {
  ...defaultBorderButtonStyle,
  height: "50px",
  padding: "6px 16px 8px",
  fontSize: "16px",
  borderColor: "rgba(229, 229, 229, 1)",
  backgroundColor: "#fff",
  fontsize: "16px",
  color: "rgba(107, 204, 9, 1) !important",
  fontWeight: "500",
  "&.MuiButtonBase-root:hover": {
    borderColor: "rgba(107, 204, 9, 1)!important",
  },
};
export const defaultGreenBorderButtonStyle = {
  ...defaultBorderButtonStyle,
  height: "50px",
  padding: "6px 16px 8px",
  fontSize: "16px",
  border: "1px solid #E5E5E5",
  backgroundColor: "#fff",
  fontsize: "16px",
  color: "#6BCC09 !important",
  fontWeight: "500",
  boxShadow: ' 0px -4px 0px 0px #DCDFE8 inset',
  "&.MuiButtonBase-root:hover": {
    border: "1px solid #E5E5E5 !important",
  },
};

export const defaultBlackBorderButtonStyle = {
  ...defaultBorderButtonStyle,
  minWidth:'189px',
  height: "58px",
  padding: "6px 16px 8px",
  fontSize: "16px",
  border: "1px solid rgba(229, 229, 229, 1)",
  backgroundColor: "#fff",
  fontsize: "16px",
  color: "rgba(75, 75, 75, 1) !important",
  fontWeight: "700",
  boxShadow: '0px -4px 0px 0px rgba(229, 229, 229, 1) inset',
  "&.MuiButtonBase-root:hover": {
    border: "1px solid rgba(229, 229, 229, 1) !important",
    color: "#6bcc09 !important",
  },
};
export const defaultInputFieldStyle = {
  "& .MuiOutlinedInput-root": {
    height: "58px",
    borderRadius: "12px",
    fontSize: "16px",
    color: "#333",
    fontWeight: 500,
    lineHeight: "18px",
    borderColor: "transparent",
    backgroundColor: "#fff",
    paddingLeft: "16px",
    paddingRight: "17px",
    "& fieldset": {
      top: 0,
      borderWidth: "1px",
      borderColor: "#E5E5E5",
    },
    "& legend": {
      display: "none", 
    },
    "&.Mui-focused fieldset": {
      borderWidth: "1px",
      borderColor: "#B0BEC5", 
    },
    "&:hover fieldset": {
      borderColor: "#B0BEC5", 
    },
  },
  "& .MuiInputBase-input": {
    height: "auto",
    padding: "0px",
    lineHeight: "16px",
  },
  "& .Mui-disabled ": {
    opacity: 0.7,
    borderColor: "red",
  },
  "& .Mui-disabled:hover": {
    cursor: "not-allowed",
  },
};

export const highlightFieldStyle = {
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#fffcf2',
    height: "58px",
    borderRadius: "12px",
    fontSize: "16px",
    color: "#333",
    fontWeight: 500,
    lineHeight: "18px",
    borderColor: "transparent",
    paddingLeft: "16px",
    paddingRight: "17px",
    "& fieldset": {
      borderColor: "#E5E5E5",
    },
    "&.Mui-focused fieldset": {
      borderWidth: "1px",
      borderColor: "#B0BEC5",
    },
    "&:hover fieldset": {
      borderColor: "#B0BEC5",
    },
  },
}

export const defaultErrorInputFieldStyle = {
  "& .MuiOutlinedInput-root": {
    height: "40px",
    borderRadius: "8px",
    fontSize: "14px",
    color: "#333",
    fontWeight: 400,
    lineHeight: "18px",
    borderColor: "red",
    letterSpacing: "-0.08px",
    backgroundColor: "#fff",
    paddingLeft: "13px",
    paddingRight: "15px",
    "& fieldset": {
      top: 0,
      borderWidth: "1px",
      borderColor: "red",
    },
    "& legend": {
      display: "none",
    },
    "&.Mui-focused fieldset": {
      borderWidth: "1px",
      borderColor: "red",
    },
    "&:hover fieldset": {
      borderColor: "red",
    },
  },
  "& .MuiInputBase-input": {
    height: "auto",
    padding: "0px",
    lineHeight: "16px",
  },
  "& .Mui-disabled ": {
    opacity: 0.7,
    borderColor: "red",
  },
  "& .Mui-disabled:hover": {
    cursor: "not-allowed",
  },
};
export const defaultTextareaFieldStyle = {
  "& .MuiOutlinedInput-root": {
    borderRadius: "16px",
    fontSize: "16px",
    color: "#333",
    fontWeight: 500,
    lineHeight: "20px",
    borderColor: "transparent",
    backgroundColor: "#fff",
    padding: "16px 24px 20px",
    "& fieldset": {
      top: 0,
      borderWidth: "0px",
      borderColor: "transparent",
    },
    "& legend": {
      display: "none",
    },
    "&.Mui-focused fieldset": {
      borderWidth: "0px",
      borderColor: "#E1E4EA", 
    },
  },
  "& .MuiInputBase-input": {
    height: "auto",
    padding: "0px 5px 0 0",
    marginTop: "0",
    lineHeight: "20px",
  },
  "& .Mui-disabled": {
    opacity: 0.7,
  },
  "& .Mui-disabled:hover": {
    cursor: "not-allowed",
  },
};
export const defaultTextareaLabelStyle = {
  transform: "translate(16px, 15px)",
  fontSize: "13px",
  "&.MuiInputLabel-shrink": {
    transform: "translate(16px, 10px)",
    fontSize: "11px",
    color: "#003B51",
    backgroundColor: "transparent",
  },
};
export const askModalStyle = {
  position: "absolute" as "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  bgcolor: "background.paper",
  boxShadow: 24,
  width: "calc(100% - 20px)",
  maxWidth: "486px",
  height: "auto",
  padding:{xs: '15px', sm : "20px"},
  borderRadius: "16px",
  outline: "none",
  overflowY: "auto",
  display: "flex",
  flexDirection: "column",
  maxHeight: "92vh", 
};
export const notificationModalStyle = {
  ...askModalStyle,
  padding: "24px",
  maxWidth: "480px",
};
export const tutorQuesStyle = {
  ...askModalStyle,
  padding: "20px",
  maxWidth: "1000px",
};
export const deleteModalStyle = {
  ...askModalStyle,
  maxWidth: "430px",
};
export const deleteConfirmModalStyle = {
  ...askModalStyle,
  maxWidth: "380px",
};

export const reminderModalStyle = {
  ...askModalStyle,
  bgcolor: "rgba(69, 176, 246, 1)",
  maxWidth: "526px",
};

export const accordionStyle = {
  maxWidth: "950px",
  width: "100%",
  border: "1px solid #E1E4EA !important",
  borderRadius: "16px !important",
  boxShadow: "none !important",
  padding: '16px !important',
  margin: '0px !important',
  "&::before": {
    display: "none",
  },
}