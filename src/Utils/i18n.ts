"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Supported locales - ADDED 'de' for Crowdin pseudo-language
export const locales = {
  en: 'English',
  tr: 'Türkçe',
  es: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  de: 'Deuts<PERSON>', // Added German/pseudo-language support
} as const;

export type Locale = keyof typeof locales;

// Translation context
interface I18nContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
  isLoading: boolean;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

// Translation hook
export function useTranslation() {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useTranslation must be used within an I18nProvider');
  }
  return context;
}

// Translation function
function translate(
  translations: Record<string, any>,
  key: string,
  params?: Record<string, string | number>
): string {
  const keys = key.split('.');
  let value: any = translations;

  // Navigate through nested keys
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // Return the key if translation not found
      return key;
    }
  }

  // If value is not a string, return the key
  if (typeof value !== 'string') {
    return key;
  }

  // Replace parameters in the translation
  if (params) {
    return value.replace(/\{\{(\w+)\}\}/g, (match: string, param: string) => {
      return params[param]?.toString() || match;
    });
  }

  return value;
}

// Helper function to get locale from URL
function getLocaleFromURL(): Locale | null {
  if (typeof window === 'undefined') return null;
  
  const pathname = window.location.pathname;
  const segments = pathname.split('/').filter(Boolean);
  const firstSegment = segments[0];
  
  // Check if first segment is a valid locale
  if (firstSegment && locales[firstSegment as Locale]) {
    return firstSegment as Locale;
  }
  
  return null;
}

// I18n Provider component
interface I18nProviderProps {
  children: ReactNode;
  defaultLocale?: Locale;
}

export function I18nProvider({ children, defaultLocale = 'en' }: I18nProviderProps) {
  const [locale, setLocale] = useState<Locale>(defaultLocale);
  const [translations, setTranslations] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(true);

  // Detect locale from URL on mount
  useEffect(() => {
    const urlLocale = getLocaleFromURL();
    if (urlLocale) {
      console.log('🌍 Detected locale from URL:', urlLocale);
      setLocale(urlLocale);
    } else {
      // Fallback to localStorage or default
      if (typeof window !== 'undefined') {
        const savedLocale = localStorage.getItem('locale') as Locale;
        if (savedLocale && locales[savedLocale]) {
          setLocale(savedLocale);
        }
      }
    }
  }, []);

  // Load translations
  useEffect(() => {
    async function loadTranslations() {
      setIsLoading(true);
      try {
        console.log(`📚 Loading translations for locale: ${locale}`);
        
        // Dynamic import of translation files
        const translationModule = await import(`../locales/${locale}.json`);
        const loadedTranslations = translationModule.default || translationModule;
        
        console.log(`✅ Loaded ${Object.keys(loadedTranslations).length} translation categories for ${locale}`);
        
        // For 'de' locale, log sample to verify pseudo-language content
        if (locale === 'de') {
          console.log('🔍 Sample DE translations:', {
            loading: loadedTranslations.common?.loading,
            title: loadedTranslations.homepage?.title,
            subtitle: loadedTranslations.homepage?.subtitle
          });
        }
        
        setTranslations(loadedTranslations);
      } catch (error) {
        console.error(`❌ Failed to load translations for locale: ${locale}`, error);
        
        // For 'de' locale, do NOT fallback to English - this is crucial for Crowdin
        if (locale === 'de') {
          console.warn('⚠️ DE locale failed to load - this will break Crowdin In-Context');
          setTranslations({}); // Empty translations, let keys show
        } else {
          // Fallback to English for other locales
          if (locale !== 'en') {
            try {
              const fallbackModule = await import('../locales/en.json');
              setTranslations(fallbackModule.default || fallbackModule);
            } catch (fallbackError) {
              console.error('Failed to load fallback translations', fallbackError);
              setTranslations({});
            }
          } else {
            setTranslations({});
          }
        }
      } finally {
        setIsLoading(false);
      }
    }

    loadTranslations();
  }, [locale]);

  // Save locale to localStorage (but don't save 'de' as it's for Crowdin only)
  useEffect(() => {
    if (typeof window !== 'undefined' && locale !== 'de') {
      localStorage.setItem('locale', locale);
    }
  }, [locale]);

  const t = (key: string, params?: Record<string, string | number>): string => {
    const translation = translate(translations, key, params);
    
    // Debug log for 'de' locale to verify pseudo-language content
    if (locale === 'de' && translation.startsWith('crwdns')) {
      console.log(`🎯 Crowdin string found: ${key} -> ${translation}`);
    }
    
    return translation;
  };

  const value: I18nContextType = {
    locale,
    setLocale,
    t,
    isLoading
  };

  return React.createElement(I18nContext.Provider, { value }, children);
}

// Utility function to get nested translation
export function getNestedTranslation(
  translations: Record<string, any>,
  path: string
): any {
  const keys = path.split('.');
  let value = translations;

  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      return undefined;
    }
  }

  return value;
}

// Language switcher component - Updated to include 'de' for testing
export function LanguageSwitcher() {
  const { locale, setLocale } = useTranslation();

  return React.createElement('div', { className: 'language-switcher' },
    React.createElement('select', {
      value: locale,
      onChange: (e) => setLocale(e.target.value as Locale),
      className: 'language-select'
    },
      Object.entries(locales).map(([code, name]) =>
        React.createElement('option', { key: code, value: code }, 
          code === 'de' ? `${name} (Crowdin)` : name
        )
      )
    )
  );
}

// Export types
export type { I18nContextType };