import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css'; 

// Performance monitoring
const performanceMetrics = {
    apiCalls: 0,
    errors: 0,
    slowOperations: 0,
    startTime: Date.now()
};

// Error tracking
const errorTracker = {
    errors: [] as Array<{
        message: string;
        timestamp: number;
        type: string;
        context?: any;
    }>,
    maxErrors: 100
};

export const trackError = (error: any, context?: any) => {
    const errorInfo = {
        message: error instanceof Error ? error.message : String(error),
        timestamp: Date.now(),
        type: error instanceof Error ? error.constructor.name : 'Unknown',
        context
    };
    
    errorTracker.errors.push(errorInfo);
    
    // Keep only the last maxErrors
    if (errorTracker.errors.length > errorTracker.maxErrors) {
        errorTracker.errors = errorTracker.errors.slice(-errorTracker.maxErrors);
    }
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
        console.error('Tracked Error:', errorInfo);
    }
    
    // Send to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
        // You can integrate with Sentry, LogRocket, or other monitoring services here
        // Example: Sentry.captureException(error, { extra: context });
    }
};

export const trackPerformance = (operation: string, duration: number) => {
    performanceMetrics.apiCalls++;
    
    if (duration > 3000) { // 3 seconds threshold
        performanceMetrics.slowOperations++;
        console.warn(`Slow operation detected: ${operation} took ${duration}ms`);
    }
    
    // Log performance metrics periodically
    if (performanceMetrics.apiCalls % 10 === 0) {
        console.log('Performance Metrics:', {
            totalCalls: performanceMetrics.apiCalls,
            errors: performanceMetrics.errors,
            slowOperations: performanceMetrics.slowOperations,
            uptime: Date.now() - performanceMetrics.startTime
        });
    }
};

export const AppNotify = (
    message: string, 
    type: 'success' | 'error' | 'warning' | 'info' = 'info',
    options?: {
        autoClose?: number;
        hideProgressBar?: boolean;
        closeOnClick?: boolean;
        pauseOnHover?: boolean;
        draggable?: boolean;
        context?: any;
    }
) => {
    const {
        autoClose = type === 'error' ? 5000 : 3000,
        hideProgressBar = false,
        closeOnClick = true,
        pauseOnHover = true,
        draggable = true,
        context
    } = options || {};

    // Track errors for monitoring
    if (type === 'error') {
        performanceMetrics.errors++;
        trackError(new Error(message), context);
    }

    // Enhanced error messages for better user experience
    let displayMessage = message;
    if (type === 'error') {
        if (message.includes('network') || message.includes('connection')) {
            displayMessage = 'Network connection issue. Please check your internet connection and try again.';
        } else if (message.includes('permission') || message.includes('unauthorized')) {
            displayMessage = 'You don\'t have permission to perform this action. Please contact support if this is an error.';
        } else if (message.includes('timeout')) {
            displayMessage = 'The operation timed out. Please try again.';
        } else if (message.includes('quota') || message.includes('limit')) {
            displayMessage = 'Service limit reached. Please try again later or contact support.';
        }
    }

    // Show toast notification
    toast[type](displayMessage, {
        position: "top-right",
        autoClose,
        hideProgressBar,
        closeOnClick,
        pauseOnHover,
        draggable,
        toastId: `${type}-${Date.now()}`, // Prevent duplicate toasts
    });

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
        console.log(`[${type.toUpperCase()}] ${message}`, context || '');
    }
};

// Export performance metrics for monitoring
export const getPerformanceMetrics = () => ({
    ...performanceMetrics,
    errorRate: performanceMetrics.apiCalls > 0 ? (performanceMetrics.errors / performanceMetrics.apiCalls) * 100 : 0,
    slowOperationRate: performanceMetrics.apiCalls > 0 ? (performanceMetrics.slowOperations / performanceMetrics.apiCalls) * 100 : 0
});

export const getErrorLog = () => errorTracker.errors;

// Reset metrics (useful for testing)
export const resetMetrics = () => {
    performanceMetrics.apiCalls = 0;
    performanceMetrics.errors = 0;
    performanceMetrics.slowOperations = 0;
    performanceMetrics.startTime = Date.now();
    errorTracker.errors = [];
};